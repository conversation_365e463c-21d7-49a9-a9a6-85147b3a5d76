import { invoke } from "@tauri-apps/api/core";
import type { HooksConfiguration } from '@/types/hooks';

/** Process type for tracking in ProcessRegistry */
export type ProcessType = 
  | { AgentRun: { agent_id: number; agent_name: string } }
  | { ClaudeSession: { session_id: string } };

/** Information about a running process */
export interface ProcessInfo {
  run_id: number;
  process_type: ProcessType;
  pid: number;
  started_at: string;
  project_path: string;
  task: string;
  model: string;
}

/**
 * Represents a project in the ~/.claude/projects directory
 */
export interface Project {
  /** The project ID (derived from the directory name) */
  id: string;
  /** The original project path (decoded from the directory name) */
  path: string;
  /** List of session IDs (JSONL file names without extension) */
  sessions: string[];
  /** Unix timestamp when the project directory was created */
  created_at: number;
}

/**
 * Represents a session with its metadata
 */
export interface Session {
  /** The session ID (UUID) */
  id: string;
  /** The project ID this session belongs to */
  project_id: string;
  /** The project path */
  project_path: string;
  /** Optional todo data associated with this session */
  todo_data?: any;
  /** Unix timestamp when the session file was created */
  created_at: number;
  /** First user message content (if available) */
  first_message?: string;
  /** Timestamp of the first user message (if available) */
  message_timestamp?: string;
}

/**
 * Represents the settings from ~/.claude/settings.json
 */
export interface ClaudeSettings {
  [key: string]: any;
}

/**
 * Represents the Claude Code version status
 */
export interface ClaudeVersionStatus {
  /** Whether Claude Code is installed and working */
  is_installed: boolean;
  /** The version string if available */
  version?: string;
  /** The full output from the command */
  output: string;
}

/**
 * Represents a CLAUDE.md file found in the project
 */
export interface ClaudeMdFile {
  /** Relative path from the project root */
  relative_path: string;
  /** Absolute path to the file */
  absolute_path: string;
  /** File size in bytes */
  size: number;
  /** Last modified timestamp */
  modified: number;
}

/**
 * Represents a file or directory entry
 */
export interface FileEntry {
  name: string;
  path: string;
  is_directory: boolean;
  size: number;
  extension?: string;
}

/**
 * Represents a Claude installation found on the system
 */
export interface ClaudeInstallation {
  /** Full path to the Claude binary */
  path: string;
  /** Version string if available */
  version?: string;
  /** Source of discovery (e.g., "nvm", "system", "homebrew", "which") */
  source: string;
  /** Type of installation */
  installation_type: "System" | "Custom";
}

// Agent API types
export interface Agent {
  id?: number;
  name: string;
  icon: string;
  system_prompt: string;
  default_task?: string;
  model: string;
  hooks?: string; // JSON string of HooksConfiguration
  created_at: string;
  updated_at: string;
  metadata?: {
    run_count?: number;
    failed_count?: number;
    last_run?: string;
    total_tokens?: number;
  };
}

export interface AgentExport {
  version: number;
  exported_at: string;
  agent: {
    name: string;
    icon: string;
    system_prompt: string;
    default_task?: string;
    model: string;
    hooks?: string;
  };
}

export interface GitHubAgentFile {
  name: string;
  path: string;
  download_url: string;
  size: number;
  sha: string;
}

export interface AgentRun {
  id?: number;
  agent_id: number;
  agent_name: string;
  agent_icon: string;
  task: string;
  model: string;
  project_path: string;
  session_id: string;
  status: string; // 'pending', 'running', 'completed', 'failed', 'cancelled'
  pid?: number;
  process_started_at?: string;
  created_at: string;
  completed_at?: string;
}

export interface AgentRunMetrics {
  duration_ms?: number;
  total_tokens?: number;
  cost_usd?: number;
  message_count?: number;
}

export interface AgentRunWithMetrics {
  id?: number;
  agent_id: number;
  agent_name: string;
  agent_icon: string;
  task: string;
  model: string;
  project_path: string;
  session_id: string;
  status: string; // 'pending', 'running', 'completed', 'failed', 'cancelled'
  pid?: number;
  process_started_at?: string;
  created_at: string;
  completed_at?: string;
  metrics?: AgentRunMetrics;
  output?: string; // Real-time JSONL content
}

// Usage Dashboard types
export interface UsageEntry {
  project: string;
  timestamp: string;
  model: string;
  input_tokens: number;
  output_tokens: number;
  cache_write_tokens: number;
  cache_read_tokens: number;
  cost: number;
}

// Claude Code API types
export interface ClaudeCodeApiConfig {
  enabled: boolean;
  url: string;
  apiKey?: string;
  defaultModel: string;
}

export interface ModelUsage {
  model: string;
  total_cost: number;
  total_tokens: number;
  input_tokens: number;
  output_tokens: number;
  cache_creation_tokens: number;
  cache_read_tokens: number;
  session_count: number;
}

export interface DailyUsage {
  date: string;
  total_cost: number;
  total_tokens: number;
  models_used: string[];
}

export interface ProjectUsage {
  project_path: string;
  project_name: string;
  total_cost: number;
  total_tokens: number;
  session_count: number;
  last_used: string;
}

export interface UsageStats {
  total_cost: number;
  total_tokens: number;
  total_input_tokens: number;
  total_output_tokens: number;
  total_cache_creation_tokens: number;
  total_cache_read_tokens: number;
  total_sessions: number;
  by_model: ModelUsage[];
  by_date: DailyUsage[];
  by_project: ProjectUsage[];
}

/**
 * Represents a checkpoint in the session timeline
 */
export interface Checkpoint {
  id: string;
  sessionId: string;
  projectId: string;
  messageIndex: number;
  timestamp: string;
  description?: string;
  parentCheckpointId?: string;
  metadata: CheckpointMetadata;
}

/**
 * Metadata associated with a checkpoint
 */
export interface CheckpointMetadata {
  totalTokens: number;
  modelUsed: string;
  userPrompt: string;
  fileChanges: number;
  snapshotSize: number;
}

/**
 * Represents a file snapshot at a checkpoint
 */
export interface FileSnapshot {
  checkpointId: string;
  filePath: string;
  content: string;
  hash: string;
  isDeleted: boolean;
  permissions?: number;
  size: number;
}

/**
 * Represents a node in the timeline tree
 */
export interface TimelineNode {
  checkpoint: Checkpoint;
  children: TimelineNode[];
  fileSnapshotIds: string[];
}

/**
 * The complete timeline for a session
 */
export interface SessionTimeline {
  sessionId: string;
  rootNode?: TimelineNode;
  currentCheckpointId?: string;
  autoCheckpointEnabled: boolean;
  checkpointStrategy: CheckpointStrategy;
  totalCheckpoints: number;
}

/**
 * Strategy for automatic checkpoint creation
 */
export type CheckpointStrategy = 'manual' | 'per_prompt' | 'per_tool_use' | 'smart';

/**
 * Result of a checkpoint operation
 */
export interface CheckpointResult {
  checkpoint: Checkpoint;
  filesProcessed: number;
  warnings: string[];
}

/**
 * Diff between two checkpoints
 */
export interface CheckpointDiff {
  fromCheckpointId: string;
  toCheckpointId: string;
  modifiedFiles: FileDiff[];
  addedFiles: string[];
  deletedFiles: string[];
  tokenDelta: number;
}

/**
 * Diff for a single file
 */
export interface FileDiff {
  path: string;
  additions: number;
  deletions: number;
  diffContent?: string;
}

/**
 * Represents an MCP server configuration
 */
export interface MCPServer {
  /** Server name/identifier */
  name: string;
  /** Transport type: "stdio" or "sse" */
  transport: string;
  /** Command to execute (for stdio) */
  command?: string;
  /** Command arguments (for stdio) */
  args: string[];
  /** Environment variables */
  env: Record<string, string>;
  /** URL endpoint (for SSE) */
  url?: string;
  /** Configuration scope: "local", "project", or "user" */
  scope: string;
  /** Whether the server is currently active */
  is_active: boolean;
  /** Server status */
  status: ServerStatus;
}

/**
 * Server status information
 */
export interface ServerStatus {
  /** Whether the server is running */
  running: boolean;
  /** Last error message if any */
  error?: string;
  /** Last checked timestamp */
  last_checked?: number;
}

/**
 * MCP configuration for project scope (.mcp.json)
 */
export interface MCPProjectConfig {
  mcpServers: Record<string, MCPServerConfig>;
}

/**
 * Individual server configuration in .mcp.json
 */
export interface MCPServerConfig {
  command: string;
  args: string[];
  env: Record<string, string>;
}

/**
 * Command categories for organization and filtering
 */
export type CommandCategory =
  | "development"
  | "git"
  | "documentation"
  | "testing"
  | "refactoring"
  | "review"
  | "deployment"
  | "utility"
  | "custom";

/**
 * Command usage statistics
 */
export interface CommandStats {
  usage_count: number;
  last_used_at?: string;
  average_execution_time?: number;
  success_rate?: number;
}

/**
 * Enhanced slash command with additional metadata and capabilities
 */
export interface SlashCommand {
  /** Unique identifier for the command */
  id: string;
  /** Command name (without prefix) */
  name: string;
  /** Full command with prefix (e.g., "/project:optimize") */
  full_command: string;
  /** Command scope: "project" or "user" */
  scope: string;
  /** Optional namespace (e.g., "frontend" in "/project:frontend:component") */
  namespace?: string;
  /** Path to the markdown file */
  file_path: string;
  /** Command content (markdown body) */
  content: string;
  /** Optional description from frontmatter */
  description?: string;
  /** Allowed tools from frontmatter */
  allowed_tools: string[];
  /** Whether the command has bash commands (!) */
  has_bash_commands: boolean;
  /** Whether the command has file references (@) */
  has_file_references: boolean;
  /** Whether the command uses $ARGUMENTS placeholder */
  accepts_arguments: boolean;
  
  // Enhanced fields
  /** Command category for organization */
  category: CommandCategory;
  /** Tags for better searchability */
  tags: string[];
  /** Command dependencies (other commands that should run first) */
  dependencies: string[];
  /** Keyboard shortcuts for quick access */
  shortcuts: string[];
  /** Usage statistics */
  usage_stats: CommandStats;
  /** Version of the command */
  version: number;
  /** Whether the command is active */
  is_active: boolean;
  /** Creation timestamp */
  created_at: string;
  /** Last update timestamp */
  updated_at: string;
}

/**
 * Command execution context
 */
export interface CommandContext {
  project_path?: string;
  current_file?: string;
  selected_text?: string;
  environment_variables?: Record<string, string>;
}

/**
 * Command execution result
 */
export interface CommandResult {
  success: boolean;
  output?: string;
  error_message?: string;
  execution_time_ms?: number;
  files_modified?: string[];
}

/**
 * Command filter options for enhanced search
 */
export interface CommandFilters {
  category?: CommandCategory;
  search?: string;
  tags?: string[];
  scope?: string;
  is_active?: boolean;
}

/**
 * Result of adding a server
 */
export interface AddServerResult {
  success: boolean;
  message: string;
  server_name?: string;
}

/**
 * Import result for multiple servers
 */
export interface ImportResult {
  imported_count: number;
  failed_count: number;
  servers: ImportServerResult[];
}

/**
 * Result for individual server import
 */
export interface ImportServerResult {
  name: string;
  success: boolean;
  error?: string;
}

/**
 * API client for interacting with the Rust backend
 */
export const api = {
  /**
   * Lists all projects in the ~/.claude/projects directory
   * @returns Promise resolving to an array of projects
   */
  async listProjects(): Promise<Project[]> {
    try {
      return await invoke<Project[]>("list_projects");
    } catch (error) {
      throw error;
    }
  },

  /**
   * Retrieves sessions for a specific project
   * @param projectId - The ID of the project to retrieve sessions for
   * @returns Promise resolving to an array of sessions
   */
  async getProjectSessions(projectId: string): Promise<Session[]> {
    try {
      return await invoke<Session[]>('get_project_sessions', { projectId });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Fetch list of agents from GitHub repository
   * @returns Promise resolving to list of available agents on GitHub
   */
  async fetchGitHubAgents(): Promise<GitHubAgentFile[]> {
    try {
      return await invoke<GitHubAgentFile[]>('fetch_github_agents');
    } catch (error) {
      throw error;
    }
  },

  /**
   * Fetch and preview a specific agent from GitHub
   * @param downloadUrl - The download URL for the agent file
   * @returns Promise resolving to the agent export data
   */
  async fetchGitHubAgentContent(downloadUrl: string): Promise<AgentExport> {
    try {
      return await invoke<AgentExport>('fetch_github_agent_content', { downloadUrl });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Import an agent directly from GitHub
   * @param downloadUrl - The download URL for the agent file
   * @returns Promise resolving to the imported agent
   */
  async importAgentFromGitHub(downloadUrl: string): Promise<Agent> {
    try {
      return await invoke<Agent>('import_agent_from_github', { downloadUrl });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Reads the Claude settings file
   * @returns Promise resolving to the settings object
   */
  async getClaudeSettings(): Promise<ClaudeSettings> {
    try {
      const result = await invoke<{ data: ClaudeSettings }>("get_claude_settings");
      
      // The Rust backend returns ClaudeSettings { data: ... }
      // We need to extract the data field
      if (result && typeof result === 'object' && 'data' in result) {
        return result.data;
      }
      
      // If the result is already the settings object, return it
      return result as ClaudeSettings;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Opens a new Claude Code session
   * @param path - Optional path to open the session in
   * @returns Promise resolving when the session is opened
   */
  async openNewSession(path?: string): Promise<string> {
    try {
      return await invoke<string>("open_new_session", { path });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Reads the CLAUDE.md system prompt file
   * @returns Promise resolving to the system prompt content
   */
  async getSystemPrompt(): Promise<string> {
    try {
      return await invoke<string>("get_system_prompt");
    } catch (error) {
      throw error;
    }
  },

  /**
   * Checks if Claude Code is installed and gets its version
   * @returns Promise resolving to the version status
   */
  async checkClaudeVersion(): Promise<ClaudeVersionStatus> {
    try {
      return await invoke<ClaudeVersionStatus>("check_claude_version");
    } catch (error) {
      throw error;
    }
  },

  /**
   * Saves the CLAUDE.md system prompt file
   * @param content - The new content for the system prompt
   * @returns Promise resolving when the file is saved
   */
  async saveSystemPrompt(content: string): Promise<string> {
    try {
      return await invoke<string>("save_system_prompt", { content });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Saves the Claude settings file
   * @param settings - The settings object to save
   * @returns Promise resolving when the settings are saved
   */
  async saveClaudeSettings(settings: ClaudeSettings): Promise<string> {
    try {
      return await invoke<string>("save_claude_settings", { settings });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Finds all CLAUDE.md files in a project directory
   * @param projectPath - The absolute path to the project
   * @returns Promise resolving to an array of CLAUDE.md files
   */
  async findClaudeMdFiles(projectPath: string): Promise<ClaudeMdFile[]> {
    try {
      return await invoke<ClaudeMdFile[]>("find_claude_md_files", { projectPath });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Reads a specific CLAUDE.md file
   * @param filePath - The absolute path to the file
   * @returns Promise resolving to the file content
   */
  async readClaudeMdFile(filePath: string): Promise<string> {
    try {
      return await invoke<string>("read_claude_md_file", { filePath });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Saves a specific CLAUDE.md file
   * @param filePath - The absolute path to the file
   * @param content - The new content for the file
   * @returns Promise resolving when the file is saved
   */
  async saveClaudeMdFile(filePath: string, content: string): Promise<string> {
    try {
      return await invoke<string>("save_claude_md_file", { filePath, content });
    } catch (error) {
      throw error;
    }
  },

  // Agent API methods
  
  /**
   * Lists all CC agents
   * @returns Promise resolving to an array of agents
   */
  async listAgents(): Promise<Agent[]> {
    try {
      return await invoke<Agent[]>('list_agents');
    } catch (error) {
      throw error;
    }
  },

  /**
   * Creates a new agent
   * @param name - The agent name
   * @param icon - The icon identifier
   * @param system_prompt - The system prompt for the agent
   * @param default_task - Optional default task
   * @param model - Optional model (defaults to 'sonnet')
   * @param hooks - Optional hooks configuration as JSON string
   * @returns Promise resolving to the created agent
   */
  async createAgent(
    name: string, 
    icon: string, 
    system_prompt: string, 
    default_task?: string, 
    model?: string,
    hooks?: string
  ): Promise<Agent> {
    try {
      return await invoke<Agent>('create_agent', { 
        name, 
        icon, 
        systemPrompt: system_prompt,
        defaultTask: default_task,
        model,
        hooks
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Updates an existing agent
   * @param id - The agent ID
   * @param name - The updated name
   * @param icon - The updated icon
   * @param system_prompt - The updated system prompt
   * @param default_task - Optional default task
   * @param model - Optional model
   * @param hooks - Optional hooks configuration as JSON string
   * @returns Promise resolving to the updated agent
   */
  async updateAgent(
    id: number, 
    name: string, 
    icon: string, 
    system_prompt: string, 
    default_task?: string, 
    model?: string,
    hooks?: string
  ): Promise<Agent> {
    try {
      return await invoke<Agent>('update_agent', { 
        id, 
        name, 
        icon, 
        systemPrompt: system_prompt,
        defaultTask: default_task,
        model,
        hooks
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Deletes an agent
   * @param id - The agent ID to delete
   * @returns Promise resolving when the agent is deleted
   */
  async deleteAgent(id: number): Promise<void> {
    try {
      return await invoke('delete_agent', { id });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Gets a single agent by ID
   * @param id - The agent ID
   * @returns Promise resolving to the agent
   */
  async getAgent(id: number): Promise<Agent> {
    try {
      return await invoke<Agent>('get_agent', { id });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Exports a single agent to JSON format
   * @param id - The agent ID to export
   * @returns Promise resolving to the JSON string
   */
  async exportAgent(id: number): Promise<string> {
    try {
      return await invoke<string>('export_agent', { id });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Imports an agent from JSON data
   * @param jsonData - The JSON string containing the agent export
   * @returns Promise resolving to the imported agent
   */
  async importAgent(jsonData: string): Promise<Agent> {
    try {
      return await invoke<Agent>('import_agent', { jsonData });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Imports an agent from a file
   * @param filePath - The path to the JSON file
   * @returns Promise resolving to the imported agent
   */
  async importAgentFromFile(filePath: string): Promise<Agent> {
    try {
      return await invoke<Agent>('import_agent_from_file', { filePath });
    } catch (error) {
      throw error;
    }
  },
  
  /**
   * Load all CC agents from cc_agents directory
   * @returns Promise resolving to array of CC agents
   */
  async loadCCAgents(): Promise<any[]> {
    try {
      return await invoke<any[]>('load_cc_agents', {});
    } catch (error) {
      throw error;
    }
  },
  
  /**
   * Import a specific CC agent
   * @param agentName - Name of the CC agent to import
   * @returns Promise resolving to the imported agent
   */
  async importCCAgent(agentName: string): Promise<Agent> {
    try {
      return await invoke<Agent>('import_cc_agent', { agentName });
    } catch (error) {
      throw error;
    }
  },
  
  /**
   * Import all CC agents in batch
   * @returns Promise resolving to array of imported agents
   */
  async importAllCCAgents(): Promise<Agent[]> {
    try {
      return await invoke<Agent[]>('import_all_cc_agents', {});
    } catch (error) {
      throw error;
    }
  },

  /**
   * Executes an agent
   * @param agentId - The agent ID to execute
   * @param projectPath - The project path to run the agent in
   * @param task - The task description
   * @param model - Optional model override
   * @returns Promise resolving to the run ID when execution starts
   */
  async executeAgent(agentId: number, projectPath: string, task: string, model?: string): Promise<number> {
    try {
      return await invoke<number>('execute_agent', { agentId, projectPath, task, model });
    } catch (error) {
      // Return a sentinel value to indicate error
      throw new Error(`Failed to execute agent: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Initialize agent orchestrator
   * @returns Promise resolving to success message
   */
  async initAgentOrchestrator(): Promise<string> {
    try {
      return await invoke<string>('init_agent_orchestrator');
    } catch (error) {
      throw new Error(`Failed to initialize agent orchestrator: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Create orchestration session
   * @param name - Session name
   * @param objective - Session objective
   * @param strategyType - Strategy type (sequential, parallel, etc.)
   * @returns Promise resolving to session data
   */
  async createOrchestrationSession(
    name: string,
    objective: string,
    strategyType: string
  ): Promise<any> {
    try {
      return await invoke<any>('create_orchestration_session', { name, objective, strategyType });
    } catch (error) {
      throw new Error(`Failed to create orchestration session: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Add agent to orchestration session
   * @param sessionId - Session ID
   * @param agentId - Agent ID
   * @param role - Agent role
   * @returns Promise resolving to success message
   */
  async addAgentToOrchestration(
    sessionId: string,
    agentId: string,
    role: string
  ): Promise<string> {
    try {
      return await invoke<string>('add_agent_to_orchestration', { sessionId, agentId, role });
    } catch (error) {
      throw new Error(`Failed to add agent to orchestration: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Start orchestration session
   * @param sessionId - Session ID
   * @returns Promise resolving to success message
   */
  async startOrchestration(sessionId: string): Promise<string> {
    try {
      return await invoke<string>('start_orchestration', { sessionId });
    } catch (error) {
      throw new Error(`Failed to start orchestration: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Stop orchestration session
   * @param sessionId - Session ID
   * @returns Promise resolving to success message
   */
  async stopOrchestration(sessionId: string): Promise<string> {
    try {
      return await invoke<string>('stop_orchestration', { sessionId });
    } catch (error) {
      throw new Error(`Failed to stop orchestration: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Get orchestration status
   * @param sessionId - Session ID
   * @returns Promise resolving to status data
   */
  async getOrchestrationStatus(sessionId: string): Promise<any> {
    try {
      return await invoke<any>('get_orchestration_status', { sessionId });
    } catch (error) {
      throw new Error(`Failed to get orchestration status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * List orchestration sessions
   * @returns Promise resolving to list of sessions
   */
  async listOrchestrationSessions(): Promise<any[]> {
    try {
      return await invoke<any[]>('list_orchestration_sessions');
    } catch (error) {
      throw new Error(`Failed to list orchestration sessions: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Coordinate task within orchestration
   * @param sessionId - Session ID
   * @param taskDescription - Task description
   * @returns Promise resolving to coordination result
   */
  async coordinateTask(
    sessionId: string,
    taskDescription: string
  ): Promise<any> {
    try {
      return await invoke<any>('coordinate_task', { sessionId, taskDescription });
    } catch (error) {
      throw new Error(`Failed to coordinate task: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Get orchestration metrics
   * @param sessionId - Session ID
   * @returns Promise resolving to metrics data
   */
  async getOrchestrationMetrics(sessionId: string): Promise<any> {
    try {
      return await invoke<any>('get_orchestration_metrics', { sessionId });
    } catch (error) {
      throw new Error(`Failed to get orchestration metrics: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Optimize orchestration strategy
   * @param sessionId - Session ID
   * @returns Promise resolving to optimization recommendations
   */
  async optimizeOrchestration(sessionId: string): Promise<any> {
    try {
      return await invoke<any>('optimize_orchestration', { sessionId });
    } catch (error) {
      throw new Error(`Failed to optimize orchestration: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Execute an agent within a Claude session context
   * @param sessionId The Claude session ID
   * @param agentId The agent ID to execute
   * @param task The task to perform
   * @param context Session context for the agent
   * @returns Promise resolving to the agent execution result
   */
  async executeAgentInSession(
    sessionId: string,
    agentId: number,
    task: string,
    context: {
      session_id: string;
      project_path: string;
      recent_messages: any[];
      checkpoints: string[];
      metrics: Record<string, any>;
    }
  ): Promise<{
    agent_id: number;
    agent_name: string;
    task_id: string;
    success: boolean;
    output?: string;
    error?: string;
    execution_time_ms: number;
  }> {
    try {
      return await invoke('execute_agent_in_session', {
        session_id: sessionId,
        agent_id: agentId,
        task,
        context
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Detect agent opportunities in Claude messages
   * @param message The message to analyze
   * @param availableAgents List of available agents
   * @returns Promise resolving to agent suggestions
   */
  async detectAgentOpportunity(
    message: string,
    availableAgents: Agent[]
  ): Promise<Array<{
    agent_id: number;
    agent_name: string;
    confidence: number;
    reason: string;
  }>> {
    try {
      return await invoke('detect_agent_opportunity', {
        message,
        available_agents: availableAgents
      });
    } catch (error) {
      return [];
    }
  },

  /**
   * Lists agent runs with metrics
   * @param agentId - Optional agent ID to filter runs
   * @returns Promise resolving to an array of agent runs with metrics
   */
  async listAgentRuns(agentId?: number): Promise<AgentRunWithMetrics[]> {
    try {
      return await invoke<AgentRunWithMetrics[]>('list_agent_runs', { agentId });
    } catch (error) {
      // Return empty array instead of throwing to prevent UI crashes
      return [];
    }
  },

  /**
   * Gets a single agent run by ID with metrics
   * @param id - The run ID
   * @returns Promise resolving to the agent run with metrics
   */
  async getAgentRun(id: number): Promise<AgentRunWithMetrics> {
    try {
      return await invoke<AgentRunWithMetrics>('get_agent_run', { id });
    } catch (error) {
      throw new Error(`Failed to get agent run: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Gets a single agent run by ID with real-time metrics from JSONL
   * @param id - The run ID
   * @returns Promise resolving to the agent run with metrics
   */
  async getAgentRunWithRealTimeMetrics(id: number): Promise<AgentRunWithMetrics> {
    try {
      return await invoke<AgentRunWithMetrics>('get_agent_run_with_real_time_metrics', { id });
    } catch (error) {
      throw new Error(`Failed to get agent run with real-time metrics: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Lists all currently running agent sessions
   * @returns Promise resolving to list of running agent sessions
   */
  async listRunningAgentSessions(): Promise<AgentRun[]> {
    try {
      return await invoke<AgentRun[]>('list_running_sessions');
    } catch (error) {
      throw new Error(`Failed to list running agent sessions: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Kills a running agent session
   * @param runId - The run ID to kill
   * @returns Promise resolving to whether the session was successfully killed
   */
  async killAgentSession(runId: number): Promise<boolean> {
    try {
      return await invoke<boolean>('kill_agent_session', { runId });
    } catch (error) {
      throw new Error(`Failed to kill agent session: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Gets the status of a specific agent session
   * @param runId - The run ID to check
   * @returns Promise resolving to the session status or null if not found
   */
  async getSessionStatus(runId: number): Promise<string | null> {
    try {
      return await invoke<string | null>('get_session_status', { runId });
    } catch (error) {
      throw new Error(`Failed to get session status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Cleanup finished processes and update their status
   * @returns Promise resolving to list of run IDs that were cleaned up
   */
  async cleanupFinishedProcesses(): Promise<number[]> {
    try {
      return await invoke<number[]>('cleanup_finished_processes');
    } catch (error) {
      throw new Error(`Failed to cleanup finished processes: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Get real-time output for a running session (with live output fallback)
   * @param runId - The run ID to get output for
   * @returns Promise resolving to the current session output (JSONL format)
   */
  async getSessionOutput(runId: number): Promise<string> {
    try {
      return await invoke<string>('get_session_output', { runId });
    } catch (error) {
      throw new Error(`Failed to get session output: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Get live output directly from process stdout buffer
   * @param runId - The run ID to get live output for
   * @returns Promise resolving to the current live output
   */
  async getLiveSessionOutput(runId: number): Promise<string> {
    try {
      return await invoke<string>('get_live_session_output', { runId });
    } catch (error) {
      throw new Error(`Failed to get live session output: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Start streaming real-time output for a running session
   * @param runId - The run ID to stream output for
   * @returns Promise that resolves when streaming starts
   */
  async streamSessionOutput(runId: number): Promise<void> {
    try {
      return await invoke<void>('stream_session_output', { runId });
    } catch (error) {
      throw new Error(`Failed to start streaming session output: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Lists all running processes (both agent runs and Claude sessions)
   * @returns Promise resolving to an array of process information
   */
  async listProcesses(): Promise<ProcessInfo[]> {
    try {
      return await invoke<ProcessInfo[]>('list_processes');
    } catch (error) {
      return [];
    }
  },

  /**
   * Loads the JSONL history for a specific session
   */
  async loadSessionHistory(sessionId: string, projectId: string): Promise<any[]> {
    try {
      return await invoke("load_session_history", { sessionId, projectId });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Loads the JSONL history for a specific agent session
   * Similar to loadSessionHistory but searches across all project directories
   * @param sessionId - The session ID (UUID)
   * @returns Promise resolving to array of session messages
   */
  async loadAgentSessionHistory(sessionId: string): Promise<any[]> {
    try {
      return await invoke<any[]>('load_agent_session_history', { sessionId });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Executes a new interactive Claude Code session with streaming output
   */
  async executeClaudeCode(projectPath: string, prompt: string, model: string): Promise<void> {
    try {
      return await invoke("execute_claude_code", { projectPath, prompt, model });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Continues an existing Claude Code conversation with streaming output
   */
  async continueClaudeCode(projectPath: string, prompt: string, model: string): Promise<void> {
    return invoke("continue_claude_code", { projectPath, prompt, model });
  },

  /**
   * Resumes an existing Claude Code session by ID with streaming output
   */
  async resumeClaudeCode(projectPath: string, sessionId: string, prompt: string, model: string): Promise<void> {
    try {
      return await invoke("resume_claude_code", { projectPath, sessionId, prompt, model });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Cancels the currently running Claude Code execution
   * @param sessionId - Optional session ID to cancel a specific session
   */
  async cancelClaudeExecution(sessionId?: string): Promise<void> {
    return invoke("cancel_claude_execution", { sessionId });
  },

  /**
   * Approves plan execution to continue
   * @param sessionId - The session ID to approve
   * @returns Promise that resolves when approval is sent
   */
  async approvePlanExecution(sessionId: string): Promise<void> {
    try {
      await invoke('approve_plan_execution', { sessionId });
    } catch (error) {
      throw new Error("Failed to approve plan execution");
    }
  },

  /**
   * Rejects plan execution and cancels the session
   * @param sessionId - The session ID to reject
   * @returns Promise that resolves when rejection is sent
   */
  async rejectPlanExecution(sessionId: string): Promise<void> {
    try {
      await invoke('reject_plan_execution', { sessionId });
    } catch (error) {
      throw new Error("Failed to reject plan execution");
    }
  },

  /**
   * Sends a session to background execution mode
   * @param sessionId - The session ID to send to background
   * @returns Promise that resolves when session is backgrounded
   */
  async sendToBackground(sessionId: string): Promise<void> {
    try {
      await invoke('send_to_background', { sessionId });
    } catch (error) {
      throw new Error("Failed to send to background");
    }
  },

  /**
   * Brings a background session back to foreground
   * @param sessionId - The session ID to bring to foreground
   * @returns Promise that resolves when session is foregrounded
   */
  async bringToForeground(sessionId: string): Promise<void> {
    try {
      await invoke('bring_to_foreground', { sessionId });
    } catch (error) {
      throw new Error("Failed to bring to foreground");
    }
  },

  /**
   * Lists all currently running Claude sessions
   * @returns Promise resolving to list of running Claude sessions
   */
  async listRunningClaudeSessions(): Promise<any[]> {
    return invoke("list_running_claude_sessions");
  },

  /**
   * Gets live output from a Claude session
   * @param sessionId - The session ID to get output for
   * @returns Promise resolving to the current live output
   */
  async getClaudeSessionOutput(sessionId: string): Promise<string> {
    return invoke("get_claude_session_output", { sessionId });
  },

  /**
   * Lists files and directories in a given path
   */
  async listDirectoryContents(directoryPath: string): Promise<FileEntry[]> {
    return invoke("list_directory_contents", { directoryPath });
  },

  /**
   * Searches for files and directories matching a pattern
   */
  async searchFiles(basePath: string, query: string): Promise<FileEntry[]> {
    return invoke("search_files", { basePath, query });
  },

  /**
   * Gets overall usage statistics
   * @returns Promise resolving to usage statistics
   */
  async getUsageStats(): Promise<UsageStats> {
    try {
      return await invoke<UsageStats>("get_usage_stats");
    } catch (error) {
      throw error;
    }
  },

  /**
   * Gets usage statistics filtered by date range
   * @param startDate - Start date (ISO format)
   * @param endDate - End date (ISO format)
   * @returns Promise resolving to usage statistics
   */
  async getUsageByDateRange(startDate: string, endDate: string): Promise<UsageStats> {
    try {
      return await invoke<UsageStats>("get_usage_by_date_range", { startDate, endDate });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Gets usage statistics grouped by session
   * @param since - Optional start date (YYYYMMDD)
   * @param until - Optional end date (YYYYMMDD)
   * @param order - Optional sort order ('asc' or 'desc')
   * @returns Promise resolving to an array of session usage data
   */
  async getSessionStats(
    since?: string,
    until?: string,
    order?: "asc" | "desc"
  ): Promise<ProjectUsage[]> {
    try {
      return await invoke<ProjectUsage[]>("get_session_stats", {
        since,
        until,
        order,
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Gets detailed usage entries with optional filtering
   * @param limit - Optional limit for number of entries
   * @returns Promise resolving to array of usage entries
   */
  async getUsageDetails(limit?: number): Promise<UsageEntry[]> {
    try {
      return await invoke<UsageEntry[]>("get_usage_details", { limit });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Creates a checkpoint for the current session state
   */
  async createCheckpoint(
    sessionId: string,
    projectId: string,
    projectPath: string,
    messageIndex?: number,
    description?: string
  ): Promise<CheckpointResult> {
    return invoke("create_checkpoint", {
      sessionId,
      projectId,
      projectPath,
      messageIndex,
      description
    });
  },

  /**
   * Restores a session to a specific checkpoint
   */
  async restoreCheckpoint(
    checkpointId: string,
    sessionId: string,
    projectId: string,
    projectPath: string
  ): Promise<CheckpointResult> {
    return invoke("restore_checkpoint", {
      checkpointId,
      sessionId,
      projectId,
      projectPath
    });
  },

  /**
   * Lists all checkpoints for a session
   */
  async listCheckpoints(
    sessionId: string,
    projectId: string,
    projectPath: string
  ): Promise<Checkpoint[]> {
    return invoke("list_checkpoints", {
      sessionId,
      projectId,
      projectPath
    });
  },

  /**
   * Forks a new timeline branch from a checkpoint
   */
  async forkFromCheckpoint(
    checkpointId: string,
    sessionId: string,
    projectId: string,
    projectPath: string,
    newSessionId: string,
    description?: string
  ): Promise<CheckpointResult> {
    return invoke("fork_from_checkpoint", {
      checkpointId,
      sessionId,
      projectId,
      projectPath,
      newSessionId,
      description
    });
  },

  /**
   * Gets the timeline for a session
   */
  async getSessionTimeline(
    sessionId: string,
    projectId: string,
    projectPath: string
  ): Promise<SessionTimeline> {
    return invoke("get_session_timeline", {
      sessionId,
      projectId,
      projectPath
    });
  },

  /**
   * Updates checkpoint settings for a session
   */
  async updateCheckpointSettings(
    sessionId: string,
    projectId: string,
    projectPath: string,
    autoCheckpointEnabled: boolean,
    checkpointStrategy: CheckpointStrategy
  ): Promise<void> {
    return invoke("update_checkpoint_settings", {
      sessionId,
      projectId,
      projectPath,
      autoCheckpointEnabled,
      checkpointStrategy
    });
  },

  /**
   * Deletes a checkpoint
   */
  async deleteCheckpoint(
    checkpointId: string,
    sessionId: string,
    projectId: string
  ): Promise<void> {
    return invoke("delete_checkpoint", {
      checkpointId,
      sessionId,
      projectId
    });
  },

  /**
   * Gets diff between two checkpoints
   */
  async getCheckpointDiff(
    fromCheckpointId: string,
    toCheckpointId: string,
    sessionId: string,
    projectId: string
  ): Promise<CheckpointDiff> {
    try {
      return await invoke<CheckpointDiff>("get_checkpoint_diff", {
        fromCheckpointId,
        toCheckpointId,
        sessionId,
        projectId
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Tracks a message for checkpointing
   */
  async trackCheckpointMessage(
    sessionId: string,
    projectId: string,
    projectPath: string,
    message: string
  ): Promise<void> {
    try {
      await invoke("track_checkpoint_message", {
        sessionId,
        projectId,
        projectPath,
        message
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Checks if auto-checkpoint should be triggered
   */
  async checkAutoCheckpoint(
    sessionId: string,
    projectId: string,
    projectPath: string,
    message: string
  ): Promise<boolean> {
    try {
      return await invoke<boolean>("check_auto_checkpoint", {
        sessionId,
        projectId,
        projectPath,
        message
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Triggers cleanup of old checkpoints
   */
  async cleanupOldCheckpoints(
    sessionId: string,
    projectId: string,
    projectPath: string,
    keepCount: number
  ): Promise<number> {
    try {
      return await invoke<number>("cleanup_old_checkpoints", {
        sessionId,
        projectId,
        projectPath,
        keepCount
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Gets checkpoint settings for a session
   */
  async getCheckpointSettings(
    sessionId: string,
    projectId: string,
    projectPath: string
  ): Promise<{
    auto_checkpoint_enabled: boolean;
    checkpoint_strategy: CheckpointStrategy;
    total_checkpoints: number;
    current_checkpoint_id?: string;
  }> {
    try {
      return await invoke("get_checkpoint_settings", {
        sessionId,
        projectId,
        projectPath
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Clears checkpoint manager for a session (cleanup on session end)
   */
  async clearCheckpointManager(sessionId: string): Promise<void> {
    try {
      await invoke("clear_checkpoint_manager", { sessionId });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Tracks a batch of messages for a session for checkpointing
   */
  trackSessionMessages: (
    sessionId: string, 
    projectId: string, 
    projectPath: string, 
    messages: string[]
  ): Promise<void> =>
    invoke("track_session_messages", { sessionId, projectId, projectPath, messages }),

  /**
   * Adds a new MCP server
   */
  async mcpAdd(
    name: string,
    transport: string,
    command?: string,
    args: string[] = [],
    env: Record<string, string> = {},
    url?: string,
    scope = "local"
  ): Promise<AddServerResult> {
    try {
      return await invoke<AddServerResult>("mcp_add", {
        name,
        transport,
        command,
        args,
        env,
        url,
        scope
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Lists all configured MCP servers
   */
  async mcpList(): Promise<MCPServer[]> {
    try {
      const result = await invoke<MCPServer[]>("mcp_list");
      return result;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Gets details for a specific MCP server
   */
  async mcpGet(name: string): Promise<MCPServer> {
    try {
      return await invoke<MCPServer>("mcp_get", { name });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Removes an MCP server
   */
  async mcpRemove(name: string): Promise<string> {
    try {
      return await invoke<string>("mcp_remove", { name });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Adds an MCP server from JSON configuration
   */
  async mcpAddJson(name: string, jsonConfig: string, scope = "local"): Promise<AddServerResult> {
    try {
      return await invoke<AddServerResult>("mcp_add_json", { name, jsonConfig, scope });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Imports MCP servers from Claude Desktop
   */
  async mcpAddFromClaudeDesktop(scope = "local"): Promise<ImportResult> {
    try {
      return await invoke<ImportResult>("mcp_add_from_claude_desktop", { scope });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Starts Claude Code as an MCP server
   */
  async mcpServe(): Promise<string> {
    try {
      return await invoke<string>("mcp_serve");
    } catch (error) {
      throw error;
    }
  },

  /**
   * Tests connection to an MCP server
   */
  async mcpTestConnection(name: string): Promise<string> {
    try {
      return await invoke<string>("mcp_test_connection", { name });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Resets project-scoped server approval choices
   */
  async mcpResetProjectChoices(): Promise<string> {
    try {
      return await invoke<string>("mcp_reset_project_choices");
    } catch (error) {
      throw error;
    }
  },

  /**
   * Gets the status of MCP servers
   */
  async mcpGetServerStatus(): Promise<Record<string, ServerStatus>> {
    try {
      return await invoke<Record<string, ServerStatus>>("mcp_get_server_status");
    } catch (error) {
      throw error;
    }
  },

  /**
   * Reads .mcp.json from the current project
   */
  async mcpReadProjectConfig(projectPath: string): Promise<MCPProjectConfig> {
    try {
      return await invoke<MCPProjectConfig>("mcp_read_project_config", { projectPath });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Saves .mcp.json to the current project
   */
  async mcpSaveProjectConfig(projectPath: string, config: MCPProjectConfig): Promise<string> {
    try {
      return await invoke<string>("mcp_save_project_config", { projectPath, config });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get the stored Claude binary path from settings
   * @returns Promise resolving to the path if set, null otherwise
   */
  async getClaudeBinaryPath(): Promise<string | null> {
    try {
      return await invoke<string | null>("get_claude_binary_path");
    } catch (error) {
      throw error;
    }
  },

  /**
   * Set the Claude binary path in settings
   * @param path - The absolute path to the Claude binary
   * @returns Promise resolving when the path is saved
   */
  async setClaudeBinaryPath(path: string): Promise<void> {
    try {
      return await invoke<void>("set_claude_binary_path", { path });
    } catch (error) {
      throw error;
    }
  },

  /**
   * List all available Claude installations on the system
   * @returns Promise resolving to an array of Claude installations
   */
  async listClaudeInstallations(): Promise<ClaudeInstallation[]> {
    try {
      return await invoke<ClaudeInstallation[]>("list_claude_installations");
    } catch (error) {
      throw error;
    }
  },

  // Storage API methods

  /**
   * Lists all tables in the SQLite database
   * @returns Promise resolving to an array of table information
   */
  async storageListTables(): Promise<any[]> {
    try {
      return await invoke<any[]>("storage_list_tables");
    } catch (error) {
      throw error;
    }
  },

  /**
   * Reads table data with pagination
   * @param tableName - Name of the table to read
   * @param page - Page number (1-indexed)
   * @param pageSize - Number of rows per page
   * @param searchQuery - Optional search query
   * @returns Promise resolving to table data with pagination info
   */
  async storageReadTable(
    tableName: string,
    page: number,
    pageSize: number,
    searchQuery?: string
  ): Promise<any> {
    try {
      return await invoke<any>("storage_read_table", {
        tableName,
        page,
        pageSize,
        searchQuery,
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Updates a row in a table
   * @param tableName - Name of the table
   * @param primaryKeyValues - Map of primary key column names to values
   * @param updates - Map of column names to new values
   * @returns Promise resolving when the row is updated
   */
  async storageUpdateRow(
    tableName: string,
    primaryKeyValues: Record<string, any>,
    updates: Record<string, any>
  ): Promise<void> {
    try {
      return await invoke<void>("storage_update_row", {
        tableName,
        primaryKeyValues,
        updates,
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Deletes a row from a table
   * @param tableName - Name of the table
   * @param primaryKeyValues - Map of primary key column names to values
   * @returns Promise resolving when the row is deleted
   */
  async storageDeleteRow(
    tableName: string,
    primaryKeyValues: Record<string, any>
  ): Promise<void> {
    try {
      return await invoke<void>("storage_delete_row", {
        tableName,
        primaryKeyValues,
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Inserts a new row into a table
   * @param tableName - Name of the table
   * @param values - Map of column names to values
   * @returns Promise resolving to the last insert row ID
   */
  async storageInsertRow(
    tableName: string,
    values: Record<string, any>
  ): Promise<number> {
    try {
      return await invoke<number>("storage_insert_row", {
        tableName,
        values,
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Executes a raw SQL query
   * @param query - SQL query string
   * @returns Promise resolving to query result
   */
  async storageExecuteSql(query: string): Promise<any> {
    try {
      return await invoke<any>("storage_execute_sql", { query });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Resets the entire database
   * @returns Promise resolving when the database is reset
   */
  async storageResetDatabase(): Promise<void> {
    try {
      return await invoke<void>("storage_reset_database");
    } catch (error) {
      throw error;
    }
  },

  // Theme settings helpers

  /**
   * Gets a setting from the app_settings table
   * @param key - The setting key to retrieve
   * @returns Promise resolving to the setting value or null if not found
   */
  async getSetting(key: string): Promise<string | null> {
    try {
      // Use storageReadTable to safely query the app_settings table
      const result = await this.storageReadTable('app_settings', 1, 1000);
      const setting = result?.data?.find((row: any) => row.key === key);
      return setting?.value || null;
    } catch (error) {
      return null;
    }
  },

  /**
   * Saves a setting to the app_settings table (insert or update)
   * @param key - The setting key
   * @param value - The setting value
   * @returns Promise resolving when the setting is saved
   */
  async saveSetting(key: string, value: string): Promise<void> {
    try {
      // Try to update first
      try {
        await this.storageUpdateRow(
          'app_settings',
          { key },
          { value }
        );
      } catch (updateError) {
        // If update fails (row doesn't exist), insert new row
        await this.storageInsertRow('app_settings', { key, value });
      }
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get hooks configuration for a specific scope
   * @param scope - The configuration scope: 'user', 'project', or 'local'
   * @param projectPath - Project path (required for project and local scopes)
   * @returns Promise resolving to the hooks configuration
   */
  async getHooksConfig(scope: 'user' | 'project' | 'local', projectPath?: string): Promise<HooksConfiguration> {
    try {
      return await invoke<HooksConfiguration>("get_hooks_config", { scope, projectPath });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Update hooks configuration for a specific scope
   * @param scope - The configuration scope: 'user', 'project', or 'local'
   * @param hooks - The hooks configuration to save
   * @param projectPath - Project path (required for project and local scopes)
   * @returns Promise resolving to success message
   */
  async updateHooksConfig(
    scope: 'user' | 'project' | 'local',
    hooks: HooksConfiguration,
    projectPath?: string
  ): Promise<string> {
    try {
      return await invoke<string>("update_hooks_config", { scope, projectPath, hooks });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Validate a hook command syntax
   * @param command - The shell command to validate
   * @returns Promise resolving to validation result
   */
  async validateHookCommand(command: string): Promise<{ valid: boolean; message: string }> {
    try {
      return await invoke<{ valid: boolean; message: string }>("validate_hook_command", { command });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get merged hooks configuration (respecting priority)
   * @param projectPath - The project path
   * @returns Promise resolving to merged hooks configuration
   */
  async getMergedHooksConfig(projectPath: string): Promise<HooksConfiguration> {
    try {
      const [userHooks, projectHooks, localHooks] = await Promise.all([
        this.getHooksConfig('user'),
        this.getHooksConfig('project', projectPath),
        this.getHooksConfig('local', projectPath)
      ]);

      // Import HooksManager for merging
      const { HooksManager } = await import('@/lib/hooksManager');
      return HooksManager.mergeConfigs(userHooks, projectHooks, localHooks);
    } catch (error) {
      throw error;
    }
  },

  // Slash Commands API methods

  /**
   * Lists all available slash commands
   * @param projectPath - Optional project path to include project-specific commands
   * @returns Promise resolving to array of slash commands
   */
  async slashCommandsList(projectPath?: string): Promise<SlashCommand[]> {
    try {
      return await invoke<SlashCommand[]>("slash_commands_list", { projectPath });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Gets a single slash command by ID
   * @param commandId - Unique identifier of the command
   * @returns Promise resolving to the slash command
   */
  async slashCommandGet(commandId: string): Promise<SlashCommand> {
    try {
      return await invoke<SlashCommand>("slash_command_get", { commandId });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Creates or updates a slash command
   * @param scope - Command scope: "project" or "user"
   * @param name - Command name (without prefix)
   * @param namespace - Optional namespace for organization
   * @param content - Markdown content of the command
   * @param description - Optional description
   * @param allowedTools - List of allowed tools for this command
   * @param projectPath - Required for project scope commands
   * @returns Promise resolving to the saved command
   */
  async slashCommandSave(
    scope: string,
    name: string,
    namespace: string | undefined,
    content: string,
    description: string | undefined,
    allowedTools: string[],
    projectPath?: string
  ): Promise<SlashCommand> {
    try {
      return await invoke<SlashCommand>("slash_command_save", {
        scope,
        name,
        namespace,
        content,
        description,
        allowedTools,
        projectPath
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Deletes a slash command
   * @param commandId - Unique identifier of the command to delete
   * @param projectPath - Optional project path for deleting project commands
   * @returns Promise resolving to deletion message
   */
  async slashCommandDelete(commandId: string, projectPath?: string): Promise<string> {
    try {
      return await invoke<string>("slash_command_delete", { commandId, projectPath });
    } catch (error) {
      throw error;
    }
  },

  // Enhanced Slash Commands API methods

  /**
   * Lists enhanced slash commands with filtering and search capabilities
   * @param projectPath - Optional project path to include project-specific commands
   * @param category - Optional category filter
   * @param search - Optional search term
   * @param tags - Optional tags filter
   * @returns Promise resolving to array of enhanced slash commands
   */
  async enhancedSlashCommandsList(
    projectPath?: string,
    category?: CommandCategory,
    search?: string,
    tags?: string[]
  ): Promise<SlashCommand[]> {
    try {
      return await invoke<SlashCommand[]>("enhanced_slash_commands_list", {
        projectPath,
        category,
        search,
        tags
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Gets available command categories
   * @returns Promise resolving to array of command categories
   */
  async getCommandCategories(): Promise<string[]> {
    try {
      return await invoke<string[]>("get_command_categories");
    } catch (error) {
      throw error;
    }
  },

  /**
   * Executes an enhanced command with context and arguments
   * @param commandId - Unique identifier of the command to execute
   * @param args - Optional arguments for the command
   * @param projectPath - Optional project path for execution context
   * @returns Promise resolving to execution result
   */
  async executeEnhancedCommand(
    commandId: string,
    args?: string,
    projectPath?: string
  ): Promise<string> {
    try {
      return await invoke<string>("execute_enhanced_command", {
        commandId,
        arguments: args,
        projectPath
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Executes an enhanced command with context information
   * @param commandId - Unique identifier of the command to execute
   * @param args - Optional arguments for the command
   * @param contextContent - Optional context content to provide to the command
   * @param projectPath - Optional project path for execution context
   * @returns Promise resolving to execution result
   */
  async executeEnhancedCommandWithContext(
    commandId: string,
    args?: string,
    contextContent?: string,
    projectPath?: string
  ): Promise<string> {
    try {
      return await invoke<string>("execute_enhanced_command_with_context", {
        commandId,
        arguments: args,
        contextContent,
        projectPath
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Gets context-aware command suggestions based on query and context
   * @param query - Search query for commands
   * @param contextContent - Optional context content to improve suggestions
   * @param projectPath - Optional project path for context
   * @returns Promise resolving to array of suggested commands
   */
  async getContextAwareSuggestions(
    query: string,
    contextContent?: string,
    projectPath?: string
  ): Promise<SlashCommand[]> {
    try {
      return await invoke<SlashCommand[]>("get_context_aware_suggestions", {
        query,
        contextContent,
        projectPath
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Gets command suggestions based on context
   * @param context - Current context (file, selection, etc.)
   * @param limit - Maximum number of suggestions
   * @returns Promise resolving to array of suggested commands
   */
  async getCommandSuggestions(
    context: CommandContext,
    limit = 5
  ): Promise<SlashCommand[]> {
    try {
      // This would be implemented to analyze context and suggest relevant commands
      // For now, return most used commands as a placeholder
      const allCommands = await this.enhancedSlashCommandsList(context.project_path);
      return allCommands
        .sort((a, b) => b.usage_stats.usage_count - a.usage_stats.usage_count)
        .slice(0, limit);
    } catch (error) {
      throw error;
    }
  },

  /**
   * Searches commands with advanced filtering
   * @param filters - Filter options for command search
   * @returns Promise resolving to filtered commands
   */
  async searchCommands(filters: CommandFilters): Promise<SlashCommand[]> {
    try {
      return await this.enhancedSlashCommandsList(
        undefined, // project_path will be handled by filters if needed
        filters.category,
        filters.search,
        filters.tags
      );
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get model usage information for automatic switching
   * @returns Promise resolving to model usage info
   */
  async getModelUsageInfo(): Promise<ModelUsageInfo> {
    try {
      return await invoke<ModelUsageInfo>("get_model_usage_info");
    } catch (error) {
      throw error;
    }
  },

  /**
   * Update model usage settings
   * @param totalLimit - Optional total token limit
   * @param thresholdPercentage - Optional threshold percentage for switching
   * @param primaryModel - Optional primary model (used up to threshold)
   * @param fallbackModel - Optional fallback model (used after threshold)
   * @returns Promise resolving when settings are updated
   */
  async updateModelUsageSettings(
    totalLimit?: number,
    thresholdPercentage?: number,
    primaryModel?: string,
    fallbackModel?: string
  ): Promise<void> {
    try {
      return await invoke("update_model_usage_settings", {
        totalLimit,
        thresholdPercentage,
        primaryModel,
        fallbackModel
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Reset model usage tracking
   * @returns Promise resolving when usage is reset
   */
  async resetModelUsage(): Promise<void> {
    try {
      return await invoke("reset_model_usage");
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get the model to use based on current usage
   * @param requestedModel - Optional requested model (if "auto", will use automatic selection)
   * @returns Promise resolving to the model that should be used
   */
  async getModelToUse(requestedModel?: string): Promise<string> {
    try {
      return await invoke<string>("get_model_to_use", { requestedModel });
    } catch (error) {
      throw error;
    }
  },

  // Context System API methods

  /**
   * Discovers and indexes all CLAUDE.md context files in a project directory
   * @param projectPath - The absolute path to the project
   * @returns Promise resolving to array of discovered context files
   */
  async discoverClaudeContexts(projectPath: string): Promise<ContextFile[]> {
    try {
      return await invoke<ContextFile[]>("discover_claude_contexts", { projectPath });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Gets available context templates
   * @returns Promise resolving to array of context templates
   */
  async getContextTemplates(): Promise<ContextTemplate[]> {
    try {
      return await invoke<ContextTemplate[]>("get_context_templates");
    } catch (error) {
      throw error;
    }
  },

  /**
   * Loads merged project context with inheritance
   * @param projectPath - The absolute path to the project
   * @returns Promise resolving to merged parsed context
   */
  async loadProjectContext(projectPath: string): Promise<ParsedContext> {
    try {
      return await invoke<ParsedContext>("load_project_context", { projectPath });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Creates a new context file from template
   * @param templateName - Name of the template to use
   * @param targetPath - Path where the context file should be created
   * @param variables - Variables to substitute in the template
   * @returns Promise resolving to the created context file
   */
  async createContextFromTemplate(
    templateName: string,
    targetPath: string,
    variables: Record<string, string>
  ): Promise<ContextFile> {
    try {
      return await invoke<ContextFile>("create_context_from_template", {
        templateName,
        targetPath,
        variables
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Gets context history for a specific context file
   * @param contextId - ID of the context file
   * @returns Promise resolving to array of context history entries
   */
  async getContextHistory(contextId: number): Promise<ContextHistory[]> {
    try {
      return await invoke<ContextHistory[]>("get_context_history", { contextId });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Updates context inheritance relationships
   * @param childContextId - ID of the child context
   * @param parentContextIds - Array of parent context IDs
   * @param inheritanceType - Type of inheritance (extends, includes, references)
   * @returns Promise resolving when inheritance is updated
   */
  async updateContextInheritance(
    childContextId: number,
    parentContextIds: number[],
    inheritanceType: string
  ): Promise<void> {
    try {
      return await invoke<void>("update_context_inheritance", {
        childContextId,
        parentContextIds,
        inheritanceType
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Initializes default context templates in the database
   * @returns Promise resolving when templates are initialized
   */
  async initializeContextTemplates(): Promise<void> {
    try {
      return await invoke<void>("initialize_context_templates");
    } catch (error) {
      throw error;
    }
  },

  // Context Watcher API methods

  /**
   * Starts watching a project directory for context file changes
   * @param projectPath - The absolute path to the project to watch
   * @returns Promise resolving to success message
   */
  async startContextWatching(projectPath: string): Promise<string> {
    try {
      return await invoke<string>("start_context_watching", { projectPath });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Stops watching a project directory for context file changes
   * @param projectPath - The absolute path to the project to stop watching
   * @returns Promise resolving to success message
   */
  async stopContextWatching(projectPath: string): Promise<string> {
    try {
      return await invoke<string>("stop_context_watching", { projectPath });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Gets the list of currently watched projects
   * @returns Promise resolving to array of watched project paths
   */
  async getWatchedProjects(): Promise<string[]> {
    try {
      return await invoke<string[]>("get_watched_projects");
    } catch (error) {
      throw error;
    }
  },

  // SuperClaude API functions

  /**
   * Execute a SuperClaude command
   * @param sessionId - Current session ID
   * @param projectId - Current project ID
   * @param input - User input containing command
   * @returns Promise resolving to parsed command result
   */
  async executeSuperClaudeCommand(
    sessionId: string,
    projectId: string,
    input: string
  ): Promise<ParsedCommand> {
    try {
      return await invoke<ParsedCommand>("execute_superclaude_command", {
        sessionId,
        projectId,
        input
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get SuperClaude command suggestions based on input
   * @param input - Partial user input
   * @returns Promise resolving to suggested commands
   */
  async getSuperClaudeSuggestions(input: string): Promise<SuperClaudeCommand[]> {
    try {
      return await invoke<SuperClaudeCommand[]>("get_superclaude_suggestions", {
        input
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Activate personas for a session
   * @param sessionId - Current session ID
   * @param projectId - Current project ID
   * @param personas - Array of persona names to activate
   * @returns Promise resolving when personas are activated
   */
  async activatePersonas(
    sessionId: string,
    projectId: string,
    personas: string[]
  ): Promise<void> {
    try {
      await invoke("activate_personas", {
        sessionId,
        projectId,
        personas
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get active personas for a session
   * @param sessionId - Current session ID
   * @param projectId - Current project ID
   * @returns Promise resolving to array of active persona names
   */
  async getSessionPersonas(
    sessionId: string,
    projectId: string
  ): Promise<string[]> {
    try {
      return await invoke<string[]>("get_session_personas", {
        sessionId,
        projectId
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Save command to history
   * @param sessionId - Current session ID
   * @param projectId - Current project ID
   * @param command - Command to save
   * @param parameters - Command parameters
   * @param success - Whether command was successful
   * @param result - Optional result message
   * @returns Promise resolving when saved
   */
  async saveCommandHistory(
    sessionId: string,
    projectId: string,
    command: string,
    trigger: string,
    args: string,
    personas: string[],
    success: boolean,
    output?: string
  ): Promise<void> {
    try {
      await invoke("save_command_history", {
        sessionId,
        projectId,
        command,
        trigger,
        args,
        personas,
        success,
        output
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get command history for a session
   * @param sessionId - Current session ID
   * @param projectId - Current project ID
   * @param limit - Optional limit on number of entries
   * @returns Promise resolving to command history
   */
  async getCommandHistory(
    sessionId: string,
    projectId: string,
    limit?: number
  ): Promise<CommandHistoryEntry[]> {
    try {
      return await invoke<CommandHistoryEntry[]>("get_command_history", {
        sessionId,
        projectId,
        limit
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Enhance prompt with SuperClaude context
   * @param sessionId - Current session ID
   * @param projectId - Current project ID
   * @param prompt - Original prompt
   * @returns Promise resolving to enhanced prompt
   */
  async enhancePromptWithContext(
    sessionId: string,
    projectId: string,
    prompt: string
  ): Promise<string> {
    try {
      return await invoke<string>("enhance_prompt_with_context", {
        sessionId,
        projectId,
        prompt
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Clear SuperClaude context for a session
   * @param sessionId - Current session ID
   * @param projectId - Current project ID
   * @returns Promise resolving when context is cleared
   */
  async clearSuperClaudeContext(
    sessionId: string,
    projectId: string
  ): Promise<void> {
    try {
      await invoke("clear_superclaude_context", {
        sessionId,
        projectId
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get SuperClaude usage statistics
   * @param projectId - Optional project ID for project-specific stats
   * @returns Promise resolving to usage statistics
   */
  async getSuperClaudeStats(projectId?: string): Promise<SuperClaudeStats> {
    try {
      return await invoke<SuperClaudeStats>("get_superclaude_stats", {
        projectId
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get all available SuperClaude commands
   * @returns Promise resolving to all commands
   */
  async getAllSuperClaudeCommands(): Promise<SuperClaudeCommand[]> {
    try {
      return await invoke<SuperClaudeCommand[]>("get_all_superclaude_commands");
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get all available SuperClaude personas
   * @returns Promise resolving to all personas
   */
  async getAllSuperClaudePersonas(): Promise<SuperClaudePersona[]> {
    try {
      return await invoke<SuperClaudePersona[]>("get_all_superclaude_personas");
    } catch (error) {
      throw error;
    }
  },

  /**
   * Detect personas from text content
   * @param text - Text to analyze for persona keywords
   * @returns Promise resolving to detected persona names
   */
  async detectPersonasFromText(text: string): Promise<string[]> {
    try {
      return await invoke<string[]>("detect_personas_from_text", {
        text
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Load SuperClaude context for a session
   * @param sessionId - Session ID to load context for
   * @param projectId - Project ID
   * @returns Promise resolving to session context
   */
  async loadSuperClaudeContext(
    sessionId: string,
    projectId: string
  ): Promise<SuperClaudeContext> {
    try {
      return await invoke<SuperClaudeContext>("load_superclaude_context", {
        sessionId,
        projectId
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Save SuperClaude context for a session
   * @param context - Context to save
   * @returns Promise resolving when context is saved
   */
  async saveSuperClaudeContext(context: SuperClaudeContext): Promise<void> {
    try {
      await invoke("save_superclaude_context", {
        context
      });
    } catch (error) {
      throw error;
    }
  },

  // Claude Code API Functions
  
  /**
   * Get Claude Code API configuration
   * @returns Promise resolving to current API configuration
   */
  async getClaudeCodeApiConfig(): Promise<ClaudeCodeApiConfig> {
    try {
      return await invoke<ClaudeCodeApiConfig>("get_claude_code_api_config");
    } catch (error) {
      throw error;
    }
  },

  /**
   * Update Claude Code API configuration
   * @param config - New API configuration
   * @returns Promise resolving when configuration is updated
   */
  async updateClaudeCodeApiConfig(config: ClaudeCodeApiConfig): Promise<void> {
    try {
      await invoke("update_claude_code_api_config", { config });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Test Claude Code API connection
   * @param url - API endpoint URL
   * @param apiKey - Optional API key
   * @returns Promise resolving to connection test result
   */
  async testClaudeCodeApiConnection(url: string, apiKey?: string): Promise<any> {
    try {
      return await invoke("test_claude_code_api_connection", { 
        url, 
        apiKey: apiKey || null 
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Execute a bash command through the backend
   * @param command - The command to execute
   * @returns Promise resolving to command execution result
   */
  async executeBashCommand(command: string): Promise<{ exitCode: number; output: string; error?: string }> {
    try {
      // Use Tauri's shell plugin instead of invoke
      const { Command } = await import('@tauri-apps/plugin-shell');
      const cmd = Command.create('bash', ['-c', command]);
      const result = await cmd.execute();
      
      return {
        exitCode: result.code,
        output: result.stdout,
        error: result.stderr || undefined
      };
    } catch (error) {
      throw error;
    }
  },

  // Analytics & ML Insights
  async getAgentPerformanceMetrics(agentId: string, timeRange?: { start: string; end: string }) {
    return invoke("get_agent_performance_metrics", { 
      agentId, 
      startTime: timeRange?.start, 
      endTime: timeRange?.end 
    });
  },

  async getSessionAnalytics(sessionId: string) {
    return invoke("get_session_analytics", { sessionId });
  },

  async getWorkflowAnalytics(workflowId: string) {
    return invoke("get_workflow_analytics", { workflowId });
  },

  async detectUsagePatterns(sessionIds: string[]) {
    return invoke("detect_usage_patterns", { sessionIds });
  },

  async generateMLInsights(context: any) {
    return invoke("generate_ml_insights", context);
  },

  async getAgentRecommendations(context: { task: string; sessionHistory?: any[]; projectContext?: string }) {
    return invoke("get_agent_recommendations", context);
  },

  async getCostAnalytics(period: string, startDate: string, endDate: string) {
    return invoke("get_cost_analytics", { period, startDate, endDate });
  },

  async getPerformanceTrend(metric: string, period: string, agentId?: string) {
    return invoke("get_performance_trend", { metric, period, agentId });
  },

  async getRealTimeMetrics() {
    return invoke("get_real_time_metrics");
  },

  async detectAnomalies(context: { sessionId?: string; agentId?: string }) {
    return invoke("detect_anomalies", context);
  },

  async getBenchmarkComparison(agentId: string, metrics: string[]) {
    return invoke("get_benchmark_comparison", { agentId, metrics });
  },

  async exportAnalytics(format: string, options: any) {
    return invoke("export_analytics", { format, ...options });
  },

  async getAnalyticsConfig() {
    return invoke("get_analytics_config");
  },

  async updateAnalyticsConfig(config: any) {
    return invoke("update_analytics_config", { config });
  },

  async initializeMLModels() {
    return invoke("initialize_ml_models");
  },

  async getAgentPerformanceHistory(context: any) {
    return invoke("get_agent_performance_history", context);
  },

  async getCostOptimizationOpportunities(context: any) {
    return invoke("get_cost_optimization_opportunities", context);
  },

  async getPredictiveAnalytics(context: any) {
    return invoke("get_predictive_analytics", context);
  },

  // Advanced Workflow System
  async saveWorkflow(workflow: any) {
    return invoke("save_workflow", { workflow });
  },

  async loadWorkflow(workflowId: string) {
    return invoke("load_workflow", { workflowId });
  },

  async listAdvancedWorkflows() {
    return invoke("list_advanced_workflows");
  },

  async executeAdvancedWorkflow(workflowId: string, trigger: any, input: any) {
    return invoke("execute_advanced_workflow", { workflowId, trigger, input });
  },

  async getAdvancedWorkflowAnalytics(workflowId: string, period: any) {
    return invoke("get_advanced_workflow_analytics", { workflowId, period });
  },

  async getOptimizationSuggestions(workflowId: string) {
    return invoke("get_optimization_suggestions", { workflowId });
  },

  async applyOptimization(workflow: any, suggestion: any) {
    return invoke("apply_optimization", { workflow, suggestion });
  },

  async exportWorkflow(workflow: any) {
    return invoke("export_workflow", { workflow });
  },

  async importWorkflow(workflowJson: string) {
    return invoke("import_workflow", { workflowJson });
  },

  async shareWorkflow(workflow: any) {
    return invoke("share_workflow", { workflow });
  },

  async executeWorkflowAgent(agentId: string, inputs: any, context: string) {
    return invoke("execute_workflow_agent", { agentId, inputs, context });
  },

  async executeScript(script: string, context: any) {
    return invoke("execute_script", { script, context });
  },

  async evaluateAICondition(expression: string, context: any) {
    return invoke("evaluate_ai_condition", { expression, context });
  },

  async applyTransformation(inputs: any, config: any) {
    return invoke("apply_transformation", { inputs, config });
  },

  async callIntegration(service: string, method: string, inputs: any, authentication?: any) {
    return invoke("call_integration", { service, method, inputs, authentication });
  },

  async makeAIDecision(model: string, prompt: string, inputs: any, temperature?: number, maxTokens?: number) {
    return invoke("make_ai_decision", { model, prompt, inputs, temperature, maxTokens });
  },

  async getCache(key: string) {
    return invoke("get_cache", { key });
  },

  async setCache(key: string, value: any, ttl?: number) {
    return invoke("set_cache", { key, value, ttl });
  },

  async executeWorkflowFallback(nodeId: string, inputs: any) {
    return invoke("execute_fallback", { nodeId, inputs });
  },

  async recordWorkflowMetrics(workflowId: string, executionId: string, metrics: any) {
    return invoke("record_workflow_metrics", { workflowId, executionId, metrics });
  },

  async saveOptimizationSuggestions(workflowId: string, suggestions: any[]) {
    return invoke("save_optimization_suggestions", { workflowId, suggestions });
  },

  async runCompensation(workflowId: string, executionId: string, errors: any[]) {
    return invoke("run_compensation", { workflowId, executionId, errors });
  },

  async activateCircuitBreaker(workflowId: string) {
    return invoke("activate_circuit_breaker", { workflowId });
  },

  async executeParallelTask(task: any) {
    return invoke("execute_parallel_task", { task });
  },

  async applyTransform(value: any, expression: string, language?: string) {
    return invoke("apply_transform", { value, expression, language });
  },

  async generateOptimizationSuggestions(workflow: any) {
    return invoke("generate_optimization_suggestions", { workflow });
  },

  async recordExecutionMetrics(metrics: any) {
    return invoke("record_execution_metrics", { metrics });
  },

  /**
   * Get MCP tools for a server
   */
  async mcpGetTools(serverId: string): Promise<any[]> {
    try {
      const result = await window.__TAURI__.invoke('mcp_get_tools', { serverId: parseInt(serverId) });
      return result as any[];
    } catch (error) {
      console.error('Failed to get MCP tools:', error);
      return [];
    }
  },

  /**
   * Get MCP resources for a server
   */
  async mcpGetResources(serverId: string): Promise<any[]> {
    try {
      const result = await window.__TAURI__.invoke('mcp_get_resources', { serverId: parseInt(serverId) });
      return result as any[];
    } catch (error) {
      console.error('Failed to get MCP resources:', error);
      return [];
    }
  },

  /**
   * Connect to an MCP server
   */
  async mcpConnect(serverId: string): Promise<void> {
    try {
      const result = await window.__TAURI__.invoke('mcp_connect', { serverId: parseInt(serverId) });
      if (!result.success) {
        throw new Error(result.error || 'Failed to connect to MCP server');
      }
    } catch (error) {
      throw error instanceof Error ? error : new Error('Failed to connect to MCP server');
    }
  },

  /**
   * Disconnect from an MCP server
   */
  async mcpDisconnect(serverId: string): Promise<void> {
    try {
      const result = await window.__TAURI__.invoke('mcp_disconnect', { serverId: parseInt(serverId) });
      if (!result.success) {
        throw new Error(result.error || 'Failed to disconnect from MCP server');
      }
    } catch (error) {
      throw error instanceof Error ? error : new Error('Failed to disconnect from MCP server');
    }
  },

  /**
   * Execute a tool on an MCP server
   */
  async mcpExecuteTool(serverId: string, toolName: string, input: any): Promise<any> {
    try {
      const result = await window.__TAURI__.invoke('mcp_execute_tool', { 
        serverId: parseInt(serverId), 
        toolName, 
        arguments: input 
      });
      if (result.success) {
        return result.result;
      } else {
        throw new Error(result.error || 'Failed to execute tool');
      }
    } catch (error) {
      throw error instanceof Error ? error : new Error('Failed to execute tool');
    }
  },

  /**
   * Fetch a resource from an MCP server
   */
  async mcpFetchResource(serverId: string, resourceUri: string): Promise<any> {
    try {
      const result = await window.__TAURI__.invoke('mcp_fetch_resource', { 
        serverId: parseInt(serverId), 
        resourceUri 
      });
      if (result.success) {
        return result.content;
      } else {
        throw new Error(result.error || 'Failed to fetch resource');
      }
    } catch (error) {
      throw error instanceof Error ? error : new Error('Failed to fetch resource');
    }
  },

  /**
   * Ping an MCP server
   */
  async mcpPing(serverId: string): Promise<void> {
    try {
      const result = await window.__TAURI__.invoke('mcp_ping', { serverId: parseInt(serverId) });
      if (!result.success) {
        throw new Error(result.error || 'Failed to ping MCP server');
      }
    } catch (error) {
      throw error instanceof Error ? error : new Error('Failed to ping MCP server');
    }
  },

  /**
   * Get favorite MCP servers
   */
  async getMCPFavorites(): Promise<string[]> {
    try {
      // Check if running in Tauri environment
      if (typeof window === 'undefined' || !window.__TAURI__) {
        console.log('MCP favorites not available in browser environment');
        return [];
      }
      const result = await window.__TAURI__.invoke('get_mcp_favorites');
      return result as string[];
    } catch (error) {
      console.error('Failed to get MCP favorites:', error);
      return [];
    }
  },

  /**
   * Get metrics for an MCP server
   */
  async getMCPMetrics(serverId: string): Promise<any> {
    try {
      const result = await window.__TAURI__.invoke('get_mcp_metrics', { serverId: parseInt(serverId) });
      return result;
    } catch (error) {
      console.error('Failed to get MCP metrics:', error);
      return {
        requests: 0,
        responseTime: 0,
        uptime: 0
      };
    }
  },

  /**
   * Get workflow analytics for a session
   */
  async getSessionWorkflowAnalytics(sessionId: string): Promise<any> {
    try {
      const result = await window.__TAURI__.invoke('get_workflow_analytics', { sessionId });
      return result;
    } catch (error) {
      console.error('Failed to get workflow analytics:', error);
      return null;
    }
  },

  /**
   * Get configured MCP servers
   */
  async getMCPServers(): Promise<any[]> {
    try {
      const result = await window.__TAURI__.invoke('get_mcp_servers');
      return result || [];
    } catch (error) {
      console.error('Failed to get MCP servers:', error);
      return [];
    }
  },

  /**
   * Check MCP server health
   */
  async checkMCPServerHealth(serverName: string): Promise<{ connected: boolean; connecting?: boolean; error?: string }> {
    try {
      const result = await window.__TAURI__.invoke('check_mcp_server_health', { serverName });
      return result;
    } catch (error) {
      console.error('Failed to check MCP server health:', error);
      return { connected: false, error: error instanceof Error ? error.message : 'Health check failed' };
    }
  },

  /**
   * Get MCP server resources
   */
  async getMCPServerResources(serverName: string): Promise<any[]> {
    try {
      const result = await window.__TAURI__.invoke('get_mcp_server_resources', { serverName });
      return result || [];
    } catch (error) {
      console.error('Failed to get MCP server resources:', error);
      return [];
    }
  },

  /**
   * Get MCP server tools
   */
  async getMCPServerTools(serverName: string): Promise<any[]> {
    try {
      const result = await window.__TAURI__.invoke('get_mcp_server_tools', { serverName });
      return result || [];
    } catch (error) {
      console.error('Failed to get MCP server tools:', error);
      return [];
    }
  },

  /**
   * Reconnect to MCP server
   */
  async reconnectMCPServer(serverName: string): Promise<void> {
    try {
      await window.__TAURI__.invoke('reconnect_mcp_server', { serverName });
    } catch (error) {
      console.error('Failed to reconnect MCP server:', error);
      throw error;
    }
  },

  /**
   * Configure MCP server
   */
  async configureMCPServer(serverName: string, config: any): Promise<void> {
    try {
      await window.__TAURI__.invoke('configure_mcp_server', { serverName, config });
    } catch (error) {
      console.error('Failed to configure MCP server:', error);
      throw error;
    }
  },

  /**
   * Execute MCP tool
   */
  async executeMCPTool(toolName: string, args: any): Promise<any> {
    try {
      const result = await window.__TAURI__.invoke('execute_mcp_tool', { toolName, args });
      return result;
    } catch (error) {
      console.error('Failed to execute MCP tool:', error);
      throw error;
    }
  },

  /**
   * Read MCP resource
   */
  async readMCPResource(uri: string): Promise<any> {
    try {
      const result = await window.__TAURI__.invoke('read_mcp_resource', { uri });
      return result;
    } catch (error) {
      console.error('Failed to read MCP resource:', error);
      throw error;
    }
  },

  /**
   * Get aggregated MCP metrics for all servers
   */
  async getAllMCPMetrics(): Promise<Record<string, any>> {
    try {
      const result = await window.__TAURI__.invoke('get_all_mcp_metrics');
      return result || {};
    } catch (error) {
      console.error('Failed to get all MCP metrics:', error);
      return {};
    }
  },
};

export interface ModelUsageInfo {
  current_usage: number;
  total_limit: number;
  usage_percentage: number;
  current_model: string;
  primary_model: string;
  fallback_model: string;
  threshold_percentage: number;
  recent_usage: Array<{
    timestamp: string;
    model: string;
    tokens: number;
    project: string;
  }>;
}

// Context System Types

/**
 * Represents a discovered and indexed context file
 */
export interface ContextFile {
  id?: number;
  file_path: string;
  project_root: string;
  relative_path: string;
  content_hash: string;
  parsed_content?: ParsedContext;
  template_type?: string;
  parent_context_id?: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  last_accessed_at?: string;
  access_count: number;
}

/**
 * Represents parsed context content with structured data
 */
export interface ParsedContext {
  sections: ContextSection[];
  metadata: ContextMetadata;
  variables: Record<string, string>;
  inheritance: string[]; // Parent context references
}

/**
 * A section within a context file
 */
export interface ContextSection {
  title: string;
  content: string;
  section_type: string; // system_prompt, instructions, examples, etc.
  priority: number;
}

/**
 * Metadata extracted from context files
 */
export interface ContextMetadata {
  title?: string;
  description?: string;
  version?: string;
  author?: string;
  tags: string[];
  language?: string;
  framework?: string;
}

/**
 * Context template for different project types
 */
export interface ContextTemplate {
  id?: number;
  name: string;
  description?: string;
  category: string;
  template_content: string;
  schema_definition?: string;
  variables: TemplateVariable[];
  is_default: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

/**
 * Variable definition for context templates
 */
export interface TemplateVariable {
  name: string;
  description: string;
  var_type: string; // string, number, boolean, array
  default_value?: string;
  required: boolean;
}

/**
 * Context history entry for versioning
 */
export interface ContextHistory {
  id: number;
  context_id: number;
  version: number;
  content_hash: string;
  content: string;
  change_description?: string;
  changed_by?: string;
  created_at: string;
}

/**
 * Context inheritance relationship
 */
export interface ContextInheritance {
  id: number;
  child_context_id: number;
  parent_context_id: number;
  inheritance_type: string;
  priority: number;
  created_at: string;
}

// SuperClaude types
export interface SuperClaudeCommand {
  trigger: string;
  name: string;
  description: string;
  category: string;
  personas: string[];
  aliases: string[];
  examples: string[];
}

export interface SuperClaudePersona {
  trigger: string;
  name: string;
  description: string;
  keywords: string[];
  system_prompt: string;
  example_prompts: string[];
}

export interface ParsedCommand {
  original_input: string;
  command: string;
  parameters: string[];
  matched_command?: SuperClaudeCommand;
  suggested_commands: SuperClaudeCommand[];
  detected_personas: string[];
  enhanced_prompt: string;
}

export interface SuperClaudeContext {
  session_id: string;
  project_id: string;
  active_personas: string[];
  command_history: CommandHistoryEntry[];
  current_command?: string;
  last_updated: string;
}

export interface CommandHistoryEntry {
  id?: number;
  session_id?: string;
  command: string;
  trigger?: string;
  args?: string;
  personas?: string[];
  executed_at?: string;
  timestamp?: string; // Keep for backward compatibility
  parameters?: string[]; // Keep for backward compatibility  
  success: boolean;
  output?: string;
  result?: string; // Keep for backward compatibility
}

export interface SuperClaudeStats {
  total_commands_executed: number;
  commands_by_category: Record<string, number>;
  most_used_personas: Array<[string, number]>;
  recent_commands: CommandHistoryEntry[];
}
