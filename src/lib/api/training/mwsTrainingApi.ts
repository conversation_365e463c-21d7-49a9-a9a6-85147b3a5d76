/**
 * MWS Mock API for Training Module
 * Complete backend-ready API structure for all training components
 */

import { mwsMockData } from './mwsMockData';
import type {
  TrainingProgram,
  TrainingSchedule,
  Certification,
  BudgetAllocation,
  ProgressData,
  FeedbackResponse,
  Achievement,
  LearningPathway,
  SkillGap,
  TrainingNeed,
  ApiResponse,
  PaginatedResponse,
  BatchOperationResult,
  TrainingNotification,
  DashboardMetrics,
  LMSCourse,
  LeaderboardEntry,
  ReportTemplate
} from '@/types/training';

// API Base URL (will be replaced with actual backend URL)
const API_BASE_URL = (import.meta as any).env?.VITE_API_URL || '/api/v1/training';

// Simulated network delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Training Overview API
 */
export const trainingOverviewApi = {
  async getMetrics(): Promise<ApiResponse<DashboardMetrics>> {
    await delay(300);
    return {
      data: mwsMockData.dashboardMetrics,
      status: 'success',
      timestamp: new Date().toISOString()
    };
  },

  async getRecentActivities() {
    await delay(200);
    // Generate recent activities from various data sources
    const activities = [
      { type: 'enrollment', message: 'New enrollment in Safety Fundamentals', time: '2 hours ago' },
      { type: 'completion', message: 'John Doe completed Project Management', time: '4 hours ago' },
      { type: 'certification', message: 'Jane Smith earned PMP Certification', time: '1 day ago' }
    ];
    return { data: activities, status: 'success', timestamp: new Date().toISOString() };
  },

  async getUpcomingDeadlines() {
    await delay(200);
    const deadlines = mwsMockData.trainingNeeds
      .filter(n => n.deadline)
      .map(n => ({
        id: n.id,
        title: `Training deadline for ${n.employeeName}`,
        date: n.deadline,
        priority: n.priority
      }));
    return { data: deadlines, status: 'success', timestamp: new Date().toISOString() };
  }
};

/**
 * LMS Integration API
 */
export const lmsIntegrationApi = {
  async getConnectedSystems() {
    await delay(400);
    // Return mock LMS integrations
    return {
      data: [
        {
          id: 'lms-001',
          provider: 'Coursera',
          isActive: true,
          coursesImported: 125,
          lastSync: '2025-01-26T10:00:00Z'
        },
        {
          id: 'lms-002',
          provider: 'LinkedIn Learning',
          isActive: true,
          coursesImported: 85,
          lastSync: '2025-01-26T12:00:00Z'
        }
      ],
      status: 'success',
      timestamp: new Date().toISOString()
    };
  },

  async getCourses(): Promise<ApiResponse<LMSCourse[]>> {
    await delay(500);
    return {
      data: mwsMockData.lmsCourses,
      status: 'success',
      timestamp: new Date().toISOString()
    };
  },

  async syncCourse(courseId: string) {
    await delay(1000);
    return { success: true, courseId, syncedAt: new Date().toISOString() };
  },

  async connectLMS(config: any) {
    await delay(1500);
    return { 
      success: true, 
      lmsId: `lms_${Date.now()}`,
      connected: true 
    };
  }
};

/**
 * Training Schedule API
 */
export const scheduleApi = {
  async getSchedules(filters?: any): Promise<ApiResponse<TrainingSchedule[]>> {
    await delay(300);
    let schedules = [...mwsMockData.schedules];
    
    if (filters?.startDate) {
      schedules = schedules.filter(s => new Date(s.startDate) >= new Date(filters.startDate));
    }
    if (filters?.endDate) {
      schedules = schedules.filter(s => new Date(s.endDate) <= new Date(filters.endDate));
    }
    if (filters?.status) {
      schedules = schedules.filter(s => s.status === filters.status);
    }
    
    return {
      data: schedules,
      status: 'success',
      timestamp: new Date().toISOString()
    };
  },

  async createSchedule(schedule: Partial<TrainingSchedule>) {
    await delay(500);
    const newSchedule = {
      id: `schedule_${Date.now()}`,
      ...schedule,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    mwsMockData.schedules.push(newSchedule as TrainingSchedule);
    return newSchedule;
  },

  async updateSchedule(id: string, updates: Partial<TrainingSchedule>) {
    await delay(400);
    const index = mwsMockData.schedules.findIndex(s => s.id === id);
    if (index !== -1) {
      mwsMockData.schedules[index] = {
        ...mwsMockData.schedules[index],
        ...updates,
        updatedAt: new Date().toISOString()
      };
      return mwsMockData.schedules[index];
    }
    throw new Error('Schedule not found');
  },

  async deleteSchedule(id: string) {
    await delay(300);
    const index = mwsMockData.schedules.findIndex(s => s.id === id);
    if (index !== -1) {
      mwsMockData.schedules.splice(index, 1);
      return { success: true };
    }
    throw new Error('Schedule not found');
  }
};

/**
 * Certification API
 */
export const certificationApi = {
  async getCertifications(employeeId?: string): Promise<ApiResponse<Certification[]>> {
    await delay(400);
    let certs = [...mwsMockData.certifications];
    if (employeeId) {
      certs = certs.filter(c => c.employeeId === employeeId);
    }
    return {
      data: certs,
      status: 'success',
      timestamp: new Date().toISOString()
    };
  },

  async createCertification(cert: Partial<Certification>) {
    await delay(500);
    const newCert = {
      id: `cert_${Date.now()}`,
      ...cert,
      status: 'pending',
      createdAt: new Date().toISOString()
    } as Certification;
    mwsMockData.certifications.push(newCert);
    return newCert;
  },

  async updateCertification(id: string, updates: Partial<Certification>) {
    await delay(400);
    const index = mwsMockData.certifications.findIndex(c => c.id === id);
    if (index !== -1) {
      mwsMockData.certifications[index] = {
        ...mwsMockData.certifications[index],
        ...updates,
        updatedAt: new Date().toISOString()
      };
      return mwsMockData.certifications[index];
    }
    throw new Error('Certification not found');
  },

  async uploadCertificate(id: string, file: File) {
    await delay(1000);
    return {
      success: true,
      certificateUrl: URL.createObjectURL(file),
      uploadedAt: new Date().toISOString()
    };
  }
};

/**
 * Budget API
 */
export const budgetApi = {
  async getBudgetAllocations(department?: string): Promise<ApiResponse<BudgetAllocation[]>> {
    await delay(300);
    let allocations = [...mwsMockData.budgetAllocations];
    if (department) {
      allocations = allocations.filter(a => a.department === department);
    }
    return {
      data: allocations,
      status: 'success',
      timestamp: new Date().toISOString()
    };
  },

  async getTotalBudget() {
    await delay(200);
    const total = mwsMockData.budgetAllocations.reduce((sum, a) => sum + a.allocatedAmount, 0);
    const spent = mwsMockData.budgetAllocations.reduce((sum, a) => sum + a.spentAmount, 0);
    return {
      data: { total, spent, remaining: total - spent },
      status: 'success',
      timestamp: new Date().toISOString()
    };
  },

  async createAllocation(allocation: Partial<BudgetAllocation>) {
    await delay(500);
    const newAllocation = {
      id: `budget_${Date.now()}`,
      ...allocation,
      status: 'active'
    } as BudgetAllocation;
    mwsMockData.budgetAllocations.push(newAllocation);
    return newAllocation;
  },

  async updateAllocation(id: string, updates: Partial<BudgetAllocation>) {
    await delay(400);
    const index = mwsMockData.budgetAllocations.findIndex(a => a.id === id);
    if (index !== -1) {
      mwsMockData.budgetAllocations[index] = {
        ...mwsMockData.budgetAllocations[index],
        ...updates
      };
      return mwsMockData.budgetAllocations[index];
    }
    throw new Error('Allocation not found');
  },

  async getSpendingTrends() {
    await delay(500);
    return {
      data: mwsMockData.dashboardMetrics.trends,
      status: 'success',
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * Progress Tracker API
 */
export const progressApi = {
  async getProgressData(employeeId?: string): Promise<ApiResponse<ProgressData[]>> {
    await delay(400);
    let progress = [...mwsMockData.progressData];
    if (employeeId) {
      progress = progress.filter(p => p.employeeId === employeeId);
    }
    return {
      data: progress,
      status: 'success',
      timestamp: new Date().toISOString()
    };
  },

  async getDepartmentProgress(department: string) {
    await delay(500);
    const deptStat = mwsMockData.dashboardMetrics.departmentStats.find(d => d.department === department);
    return {
      data: deptStat,
      status: 'success',
      timestamp: new Date().toISOString()
    };
  },

  async updateProgress(progressId: string, updates: Partial<ProgressData>) {
    await delay(300);
    const index = mwsMockData.progressData.findIndex(p => p.id === progressId);
    if (index !== -1) {
      mwsMockData.progressData[index] = {
        ...mwsMockData.progressData[index],
        ...updates,
        lastAccessDate: new Date().toISOString()
      };
      return mwsMockData.progressData[index];
    }
    throw new Error('Progress record not found');
  }
};

/**
 * Feedback API
 */
export const feedbackApi = {
  async getFeedback(filters?: any): Promise<ApiResponse<FeedbackResponse[]>> {
    await delay(400);
    let feedback = [...mwsMockData.feedbackResponses];
    
    if (filters?.programId) {
      feedback = feedback.filter(f => f.programId === filters.programId);
    }
    if (filters?.rating) {
      feedback = feedback.filter(f => f.rating >= filters.rating);
    }
    
    return {
      data: feedback,
      status: 'success',
      timestamp: new Date().toISOString()
    };
  },

  async submitFeedback(feedback: Partial<FeedbackResponse>) {
    await delay(600);
    const newFeedback = {
      id: `feedback_${Date.now()}`,
      ...feedback,
      submittedDate: new Date().toISOString()
    } as FeedbackResponse;
    mwsMockData.feedbackResponses.push(newFeedback);
    return newFeedback;
  },

  async getAverageRatings() {
    await delay(300);
    const avgRating = mwsMockData.feedbackResponses.reduce((sum, f) => sum + f.rating, 0) / mwsMockData.feedbackResponses.length;
    return {
      data: { averageRating: avgRating, totalResponses: mwsMockData.feedbackResponses.length },
      status: 'success',
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * Gamification API
 */
export const gamificationApi = {
  async getAchievements(employeeId?: string): Promise<ApiResponse<Achievement[]>> {
    await delay(400);
    let achievements = [...mwsMockData.achievements];
    if (employeeId) {
      achievements = achievements.filter(a => a.unlockedBy.includes(employeeId));
    }
    return {
      data: achievements,
      status: 'success',
      timestamp: new Date().toISOString()
    };
  },

  async getLeaderboard(): Promise<ApiResponse<LeaderboardEntry[]>> {
    await delay(500);
    return {
      data: mwsMockData.leaderboard,
      status: 'success',
      timestamp: new Date().toISOString()
    };
  },

  async getUserPoints(employeeId: string) {
    await delay(300);
    const entry = mwsMockData.leaderboard.find(l => l.employeeId === employeeId);
    return {
      data: entry || { points: 0, badges: 0, rank: null },
      status: 'success',
      timestamp: new Date().toISOString()
    };
  },

  async awardAchievement(employeeId: string, achievementId: string) {
    await delay(500);
    const achievement = mwsMockData.achievements.find(a => a.id === achievementId);
    if (achievement) {
      if (!achievement.unlockedBy.includes(employeeId)) {
        achievement.unlockedBy.push(employeeId);
      }
    }
    return { success: true, achievementId, employeeId };
  }
};

/**
 * Reporting API
 */
export const reportingApi = {
  async getReportTemplates(): Promise<ApiResponse<ReportTemplate[]>> {
    await delay(400);
    return {
      data: mwsMockData.reportTemplates,
      status: 'success',
      timestamp: new Date().toISOString()
    };
  },

  async generateReport(templateId: string, params?: any) {
    await delay(2000); // Simulate longer processing time
    return {
      success: true,
      reportId: `report_${Date.now()}`,
      downloadUrl: `/reports/training_report_${Date.now()}.pdf`,
      generatedAt: new Date().toISOString()
    };
  },

  async getDashboardMetrics(): Promise<ApiResponse<DashboardMetrics>> {
    await delay(500);
    return {
      data: mwsMockData.dashboardMetrics,
      status: 'success',
      timestamp: new Date().toISOString()
    };
  },

  async exportData(format: 'csv' | 'excel' | 'pdf', data: any) {
    await delay(1500);
    return {
      success: true,
      downloadUrl: `/exports/training_export_${Date.now()}.${format}`,
      format,
      generatedAt: new Date().toISOString()
    };
  }
};

/**
 * Learning Pathways API
 */
export const pathwaysApi = {
  async getPathways(): Promise<ApiResponse<LearningPathway[]>> {
    await delay(400);
    return {
      data: mwsMockData.learningPathways,
      status: 'success',
      timestamp: new Date().toISOString()
    };
  },

  async getPathwayDetails(pathId: string) {
    await delay(500);
    const pathway = mwsMockData.learningPathways.find(p => p.id === pathId);
    if (!pathway) throw new Error('Pathway not found');
    
    const programs = mwsMockData.trainingPrograms.filter(prog => 
      pathway.programs.includes(prog.id)
    );
    
    return {
      data: { ...pathway, programs },
      status: 'success',
      timestamp: new Date().toISOString()
    };
  },

  async enrollInPathway(pathId: string, employeeId: string) {
    await delay(600);
    const pathway = mwsMockData.learningPathways.find(p => p.id === pathId);
    if (pathway) {
      pathway.enrolledEmployees += 1;
    }
    return {
      success: true,
      pathId,
      employeeId,
      enrolledAt: new Date().toISOString()
    };
  },

  async updateMilestone(pathId: string, milestoneName: string, completed: boolean) {
    await delay(400);
    const pathway = mwsMockData.learningPathways.find(p => p.id === pathId);
    if (pathway) {
      const milestone = pathway.milestones.find(m => m.name === milestoneName);
      if (milestone) {
        milestone.completed = completed;
        if (completed) {
          milestone.completionDate = new Date().toISOString();
        }
      }
    }
    return { success: true };
  }
};

/**
 * Skills Gap Analysis API
 */
export const skillsGapApi = {
  async getSkillGaps(department?: string): Promise<ApiResponse<SkillGap[]>> {
    await delay(600);
    let gaps = [...mwsMockData.skillGaps];
    if (department) {
      gaps = gaps.filter(g => g.department === department);
    }
    return {
      data: gaps,
      status: 'success',
      timestamp: new Date().toISOString()
    };
  },

  async analyzeGap(params: any) {
    await delay(1500);
    // Simulate gap analysis
    return {
      data: {
        department: params.department,
        totalGaps: mwsMockData.skillGaps.length,
        criticalGaps: mwsMockData.skillGaps.filter(g => g.priority === 'high').length,
        estimatedClosureTime: '6 months',
        recommendedPrograms: mwsMockData.trainingPrograms.slice(0, 3)
      },
      status: 'success',
      timestamp: new Date().toISOString()
    };
  },

  async createSkillAssessment(assessment: any) {
    await delay(800);
    return {
      success: true,
      assessmentId: `assess_${Date.now()}`,
      createdAt: new Date().toISOString()
    };
  },

  async getRecommendedTraining(gapId: string) {
    await delay(500);
    const gap = mwsMockData.skillGaps.find(g => g.id === gapId);
    if (!gap) throw new Error('Skill gap not found');
    
    const programs = mwsMockData.trainingPrograms.filter(p => 
      gap.recommendedPrograms.includes(p.id)
    );
    
    return {
      data: programs,
      status: 'success',
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * Training Needs API
 */
export const trainingNeedsApi = {
  async getTrainingNeeds(filters?: any): Promise<ApiResponse<TrainingNeed[]>> {
    await delay(400);
    let needs = [...mwsMockData.trainingNeeds];
    
    if (filters?.department) {
      needs = needs.filter(n => n.department === filters.department);
    }
    if (filters?.priority) {
      needs = needs.filter(n => n.priority === filters.priority);
    }
    if (filters?.status) {
      needs = needs.filter(n => n.status === filters.status);
    }
    
    return {
      data: needs,
      status: 'success',
      timestamp: new Date().toISOString()
    };
  },

  async createTrainingNeed(need: Partial<TrainingNeed>) {
    await delay(600);
    const newNeed = {
      id: `need_${Date.now()}`,
      ...need,
      status: 'pending',
      createdDate: new Date().toISOString(),
      lastUpdated: new Date().toISOString()
    } as TrainingNeed;
    mwsMockData.trainingNeeds.push(newNeed);
    return newNeed;
  },

  async updateTrainingNeed(id: string, updates: Partial<TrainingNeed>) {
    await delay(400);
    const index = mwsMockData.trainingNeeds.findIndex(n => n.id === id);
    if (index !== -1) {
      mwsMockData.trainingNeeds[index] = {
        ...mwsMockData.trainingNeeds[index],
        ...updates,
        lastUpdated: new Date().toISOString()
      };
      return mwsMockData.trainingNeeds[index];
    }
    throw new Error('Training need not found');
  },

  async approveNeed(id: string, approverType: 'manager' | 'hr') {
    await delay(500);
    const need = mwsMockData.trainingNeeds.find(n => n.id === id);
    if (need) {
      if (approverType === 'manager') {
        need.managerApproval = true;
      } else {
        need.hrApproval = true;
      }
      if (need.managerApproval && need.hrApproval) {
        need.status = 'in_progress';
      }
      need.lastUpdated = new Date().toISOString();
    }
    return need;
  }
};

/**
 * Batch Operations API
 */
export const batchApi = {
  async importTrainingData(file: File, type: string): Promise<BatchOperationResult> {
    await delay(2000);
    return {
      successful: Math.floor(Math.random() * 100) + 50,
      failed: Math.floor(Math.random() * 10),
      errors: [],
      results: []
    };
  },

  async bulkEnroll(employeeIds: string[], programId: string) {
    await delay(1500);
    return {
      successful: employeeIds.length,
      failed: 0,
      errors: [],
      results: employeeIds.map(id => ({ employeeId: id, programId, enrolled: true }))
    };
  },

  async bulkAssignCertifications(employeeIds: string[], certificationIds: string[]) {
    await delay(1800);
    const total = employeeIds.length * certificationIds.length;
    return {
      successful: total,
      failed: 0,
      errors: [],
      results: []
    };
  }
};

/**
 * Main Training API Export
 */
export const mwsTrainingApi = {
  overview: trainingOverviewApi,
  lms: lmsIntegrationApi,
  schedule: scheduleApi,
  certification: certificationApi,
  budget: budgetApi,
  progress: progressApi,
  feedback: feedbackApi,
  gamification: gamificationApi,
  reporting: reportingApi,
  pathways: pathwaysApi,
  skillsGap: skillsGapApi,
  trainingNeeds: trainingNeedsApi,
  batch: batchApi
};

export default mwsTrainingApi;