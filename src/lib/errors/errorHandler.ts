// Note: Toast notifications will be handled by the consuming components
// This keeps the error handler decoupled from specific toast implementations

/**
 * Standard error types for the application
 */
export enum ErrorType {
  NETWORK = 'NETWORK',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  SERVER = 'SERVER',
  UNKNOWN = 'UNKNOWN',
}

/**
 * Standard error interface
 */
export interface AppError {
  type: ErrorType;
  message: string;
  code?: string | number;
  details?: any;
  timestamp: Date;
  retryable: boolean;
}

/**
 * Error classification utility
 */
export class ErrorClassifier {
  static classify(error: any): AppError {
    const timestamp = new Date();
    
    // Network errors
    if (error?.code === 'NETWORK_ERROR' || error?.message?.includes('fetch')) {
      return {
        type: ErrorType.NETWORK,
        message: 'Network connection failed. Please check your internet connection.',
        code: error.code,
        details: error,
        timestamp,
        retryable: true,
      };
    }
    
    // HTTP status code errors
    if (error?.response?.status) {
      const status = error.response.status;
      
      if (status === 401) {
        return {
          type: ErrorType.AUTHENTICATION,
          message: 'Authentication required. Please log in again.',
          code: status,
          details: error,
          timestamp,
          retryable: false,
        };
      }
      
      if (status === 403) {
        return {
          type: ErrorType.AUTHORIZATION,
          message: 'You do not have permission to perform this action.',
          code: status,
          details: error,
          timestamp,
          retryable: false,
        };
      }
      
      if (status === 404) {
        return {
          type: ErrorType.NOT_FOUND,
          message: 'The requested resource was not found.',
          code: status,
          details: error,
          timestamp,
          retryable: false,
        };
      }
      
      if (status >= 400 && status < 500) {
        return {
          type: ErrorType.VALIDATION,
          message: error.response.data?.message || 'Invalid request. Please check your input.',
          code: status,
          details: error,
          timestamp,
          retryable: false,
        };
      }
      
      if (status >= 500) {
        return {
          type: ErrorType.SERVER,
          message: 'Server error occurred. Please try again later.',
          code: status,
          details: error,
          timestamp,
          retryable: true,
        };
      }
    }
    
    // Validation errors
    if (error?.name === 'ValidationError' || error?.type === 'validation') {
      return {
        type: ErrorType.VALIDATION,
        message: error.message || 'Validation failed',
        code: error.code,
        details: error,
        timestamp,
        retryable: false,
      };
    }
    
    // Default unknown error
    return {
      type: ErrorType.UNKNOWN,
      message: error?.message || 'An unexpected error occurred',
      code: error?.code,
      details: error,
      timestamp,
      retryable: false,
    };
  }
}

/**
 * Global error handler for React Query and other errors
 */
export class GlobalErrorHandler {
  private static errorHistory: AppError[] = [];
  private static maxHistorySize = 50;
  
  /**
   * Handle React Query errors globally
   */
  static handleQueryError(error: any): AppError {
    const appError = ErrorClassifier.classify(error);
    this.logError(appError);
    return appError;
  }
  
  /**
   * Handle mutation errors globally
   */
  static handleMutationError(error: any, context?: any): AppError {
    const appError = ErrorClassifier.classify(error);
    this.logError(appError);
    return appError;
  }
  
  /**
   * Log error to history and console
   */
  private static logError(error: AppError): void {
    // Add to error history
    this.errorHistory.unshift(error);
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory = this.errorHistory.slice(0, this.maxHistorySize);
    }
    
    // Log to console in development
    if ((import.meta as any).env?.MODE === 'development') {
    }
    
    // TODO: Send to error reporting service in production
    // if ((import.meta as any).env?.MODE === 'production') {
    //   sendToErrorReporting(error);
    // }
  }
  
  /**
   * Get user-friendly error message for display
   */
  static getDisplayMessage(error: AppError): string {
    return error.message;
  }
  
  /**
   * Get error history for debugging
   */
  static getErrorHistory(): AppError[] {
    return [...this.errorHistory];
  }
  
  /**
   * Clear error history
   */
  static clearErrorHistory(): void {
    this.errorHistory = [];
  }
  
  /**
   * Check if error is retryable
   */
  static isRetryable(error: any): boolean {
    const appError = ErrorClassifier.classify(error);
    return appError.retryable;
  }
}

/**
 * Utility functions for error handling
 */
export const errorUtils = {
  /**
   * Create a user-friendly error message
   */
  getUserMessage: (error: any): string => {
    const appError = ErrorClassifier.classify(error);
    return appError.message;
  },
  
  /**
   * Check if error should trigger a retry
   */
  shouldRetry: (error: any, attemptCount = 0): boolean => {
    const appError = ErrorClassifier.classify(error);
    return appError.retryable && attemptCount < 3;
  },
  
  /**
   * Get retry delay based on attempt count
   */
  getRetryDelay: (attemptCount: number): number => {
    return Math.min(1000 * Math.pow(2, attemptCount), 10000); // Exponential backoff, max 10s
  },
  
  /**
   * Format error for display
   */
  formatError: (error: any): { title: string; message: string; actions?: string[] } => {
    const appError = ErrorClassifier.classify(error);
    
    const actions: string[] = [];
    if (appError.retryable) {
      actions.push('Try again');
    }
    if (appError.type === ErrorType.NETWORK) {
      actions.push('Check connection');
    }
    
    return {
      title: `${appError.type} Error`,
      message: appError.message,
      actions: actions.length > 0 ? actions : undefined,
    };
  },
};