import { useCallback, useMemo } from 'react';
import { useTabContext } from '@/contexts/TabContext';
import { Tab } from '@/contexts/TabContext';

// Extend the TabType union type to include our new tab types
declare module '@/contexts/TabContext' {
  interface Tab {
    type: Tab['type'] | 'graduate-trainee-tracker' | 'elearning-design-board' | 'content-creator' | 'content-creators-hub' | 'lighthouse-lm' | 'second-brain';
  }
}

interface UseTabStateReturn {
  // State
  tabs: Tab[];
  activeTab: Tab | undefined;
  activeTabId: string | null;
  tabCount: number;
  chatTabCount: number;
  agentTabCount: number;
  
  // Operations
  createChatTab: (projectId?: string, title?: string) => string;
  createAgentTab: (agentRunId: string, agentName: string) => string;
  createAgentExecutionTab: (agent: any, tabId: string) => string;
  createProjectsTab: () => string | null;
  createUsageTab: () => string | null;
  createMCPTab: () => string | null;
  createMCPOverviewTab: () => string | null;
  createSettingsTab: () => string | null;
  createSettingsOverviewTab: () => string | null;
  createAgentsOverviewTab: () => string | null;
  createClaudeMdTab: () => string | null;
  createClaudeFileTab: (fileId: string, fileName: string) => string;
  createCreateAgentTab: () => string;
  createImportAgentTab: () => string;
  createTrainingBudgetTab: () => string | null;
  createTrainingDashboardTab: () => string | null;
  createLDProfessionalTNATab: () => string | null;
  createGraduateTraineeTrackerTab: () => string | null;
  createGraduateTraineeTab: () => string | null;
  createCCAgentsTab: () => string | null;
  createLighthouseLMHubTab: () => string | null;
  createDataCenterTab: () => string | null;
  createAnalyticsTab: () => string | null;
  createWorkflowDesignerTab: () => string | null;
  createLightbulbTab: () => string | null;
  createLightbulbGridTab: () => string | null;
  createFinancialDashboardTab: () => string | null;
  createELearningDesignBoardTab: () => string | null;
  createContentCreatorTab: () => string | null;
  createContentCreatorsHubTab: () => string | null;
  closeTab: (id: string, force?: boolean) => Promise<boolean>;
  closeCurrentTab: () => Promise<boolean>;
  switchToTab: (id: string) => void;
  switchToNextTab: () => void;
  switchToPreviousTab: () => void;
  switchToTabByIndex: (index: number) => void;
  updateTab: (id: string, updates: Partial<Tab>) => void;
  updateTabTitle: (id: string, title: string) => void;
  updateTabStatus: (id: string, status: Tab['status']) => void;
  markTabAsChanged: (id: string, hasChanges: boolean) => void;
  findTabBySessionId: (sessionId: string) => Tab | undefined;
  findTabByAgentRunId: (agentRunId: string) => Tab | undefined;
  findTabByType: (type: Tab['type']) => Tab | undefined;
  canAddTab: () => boolean;
}

export const useTabState = (): UseTabStateReturn => {
  const {
    tabs,
    activeTabId,
    addTab,
    removeTab,
    updateTab,
    setActiveTab,
    getTabById,
    getTabsByType
  } = useTabContext();

  const activeTab = useMemo(() => 
    activeTabId ? getTabById(activeTabId) : undefined,
    [activeTabId, getTabById]
  );

  const tabCount = tabs.length;
  const chatTabCount = useMemo(() => getTabsByType('chat').length, [getTabsByType]);
  const agentTabCount = useMemo(() => getTabsByType('agent').length, [getTabsByType]);

  const createChatTab = useCallback((projectId?: string, title?: string): string => {
    const tabTitle = title || `Chat ${chatTabCount + 1}`;
    return addTab({
      type: 'chat',
      title: tabTitle,
      sessionId: projectId,
      status: 'idle',
      hasUnsavedChanges: false,
      icon: 'message-square'
    });
  }, [addTab, chatTabCount]);

  const createAgentTab = useCallback((agentRunId: string, agentName: string): string => {
    // Check if tab already exists
    const existingTab = tabs.find(tab => tab.agentRunId === agentRunId);
    if (existingTab) {
      setActiveTab(existingTab.id);
      return existingTab.id;
    }

    return addTab({
      type: 'agent',
      title: agentName,
      agentRunId,
      status: 'running',
      hasUnsavedChanges: false,
      icon: 'bot'
    });
  }, [addTab, tabs, setActiveTab]);

  const createProjectsTab = useCallback((): string | null => {
    // Check if projects tab already exists (singleton)
    const existingTab = tabs.find(tab => tab.type === 'projects');
    if (existingTab) {
      setActiveTab(existingTab.id);
      return existingTab.id;
    }

    return addTab({
      type: 'projects',
      title: 'CC Projects',
      status: 'idle',
      hasUnsavedChanges: false,
      icon: 'folder'
    });
  }, [addTab, tabs, setActiveTab]);

  const createUsageTab = useCallback((): string | null => {
    // Check if usage tab already exists (singleton)
    const existingTab = tabs.find(tab => tab.type === 'usage');
    if (existingTab) {
      setActiveTab(existingTab.id);
      return existingTab.id;
    }

    return addTab({
      type: 'usage',
      title: 'Usage',
      status: 'idle',
      hasUnsavedChanges: false,
      icon: 'bar-chart'
    });
  }, [addTab, tabs, setActiveTab]);

  const createMCPTab = useCallback((): string | null => {
    // Check if MCP tab already exists (singleton)
    const existingTab = tabs.find(tab => tab.type === 'mcp');
    if (existingTab) {
      setActiveTab(existingTab.id);
      return existingTab.id;
    }

    return addTab({
      type: 'mcp',
      title: 'MCP Servers',
      status: 'idle',
      hasUnsavedChanges: false,
      icon: 'server'
    });
  }, [addTab, tabs, setActiveTab]);

  const createSettingsTab = useCallback((): string | null => {
    // Check if settings tab already exists (singleton)
    const existingTab = tabs.find(tab => tab.type === 'settings');
    if (existingTab) {
      setActiveTab(existingTab.id);
      return existingTab.id;
    }

    return addTab({
      type: 'settings',
      title: 'Settings',
      status: 'idle',
      hasUnsavedChanges: false,
      icon: 'settings'
    });
  }, [addTab, tabs, setActiveTab]);

  const createClaudeMdTab = useCallback((): string | null => {
    // Check if claude-md tab already exists (singleton)
    const existingTab = tabs.find(tab => tab.type === 'claude-md');
    if (existingTab) {
      setActiveTab(existingTab.id);
      return existingTab.id;
    }

    return addTab({
      type: 'claude-md',
      title: 'CLAUDE.md',
      status: 'idle',
      hasUnsavedChanges: false,
      icon: 'file-text'
    });
  }, [addTab, tabs, setActiveTab]);

  const createClaudeFileTab = useCallback((fileId: string, fileName: string): string => {
    // Check if tab already exists for this file
    const existingTab = tabs.find(tab => tab.type === 'claude-file' && tab.claudeFileId === fileId);
    if (existingTab) {
      setActiveTab(existingTab.id);
      return existingTab.id;
    }

    return addTab({
      type: 'claude-file',
      title: fileName,
      claudeFileId: fileId,
      status: 'idle',
      hasUnsavedChanges: false,
      icon: 'file-text'
    });
  }, [addTab, tabs, setActiveTab]);

  const createAgentExecutionTab = useCallback((agent: any, _tabId: string): string => {
    return addTab({
      type: 'agent-execution',
      title: `Run: ${agent.name}`,
      agentData: agent,
      status: 'idle',
      hasUnsavedChanges: false,
      icon: 'bot'
    });
  }, [addTab]);

  const createCreateAgentTab = useCallback((): string => {
    // Check if create agent tab already exists (singleton)
    const existingTab = tabs.find(tab => tab.type === 'create-agent');
    if (existingTab) {
      setActiveTab(existingTab.id);
      return existingTab.id;
    }

    return addTab({
      type: 'create-agent',
      title: 'Create Agent',
      status: 'idle',
      hasUnsavedChanges: false,
      icon: 'plus'
    });
  }, [addTab, tabs, setActiveTab]);

  const createImportAgentTab = useCallback((): string => {
    // Check if import agent tab already exists (singleton)
    const existingTab = tabs.find(tab => tab.type === 'import-agent');
    if (existingTab) {
      setActiveTab(existingTab.id);
      return existingTab.id;
    }

    return addTab({
      type: 'import-agent',
      title: 'Import Agent',
      status: 'idle',
      hasUnsavedChanges: false,
      icon: 'import'
    });
  }, [addTab, tabs, setActiveTab]);

  const createTrainingBudgetTab = useCallback((): string | null => {
    // Check if training budget tab already exists (singleton)
    const existingTab = tabs.find(tab => tab.type === 'training-budget');
    if (existingTab) {
      setActiveTab(existingTab.id);
      return existingTab.id;
    }

    return addTab({
      type: 'training-budget',
      title: 'Training Budget',
      status: 'idle',
      hasUnsavedChanges: false,
      icon: 'wallet'
    });
  }, [addTab, tabs, setActiveTab]);

  const createTrainingDashboardTab = useCallback((): string | null => {
    // Check if training dashboard tab already exists (singleton)
    const existingTab = tabs.find(tab => tab.type === 'training-dashboard');
    if (existingTab) {
      setActiveTab(existingTab.id);
      return existingTab.id;
    }

    return addTab({
      type: 'training-dashboard',
      title: 'Training Dashboard',
      status: 'idle',
      hasUnsavedChanges: false,
      icon: 'target'
    });
  }, [addTab, tabs, setActiveTab]);

  const createLDProfessionalTNATab = useCallback((): string | null => {
    // Check if LD Professional TNA tab already exists (singleton)
    const existingTab = tabs.find(tab => tab.type === 'ld-professional-tna');
    if (existingTab) {
      setActiveTab(existingTab.id);
      return existingTab.id;
    }

    return addTab({
      type: 'ld-professional-tna',
      title: 'LD Professional TNA',
      status: 'idle',
      hasUnsavedChanges: false,
      icon: 'target'
    });
  }, [addTab, tabs, setActiveTab]);

  const createGraduateTraineeTrackerTab = useCallback((): string | null => {
    // Check if graduate trainee tracker tab already exists (singleton)
    const existingTab = tabs.find(tab => tab.type === 'graduate-trainee-tracker');
    if (existingTab) {
      setActiveTab(existingTab.id);
      return existingTab.id;
    }

    return addTab({
      type: 'graduate-trainee-tracker',
      title: 'Graduate Trainee Hub',
      status: 'idle',
      hasUnsavedChanges: false,
      icon: 'users'
    });
  }, [addTab, tabs, setActiveTab]);

  const createGraduateTraineeTab = useCallback((): string | null => {
    // Check if graduate trainee tab already exists (singleton)
    const existingTab = tabs.find(tab => tab.type === 'graduate-trainee');
    if (existingTab) {
      setActiveTab(existingTab.id);
      return existingTab.id;
    }

    return addTab({
      type: 'graduate-trainee',
      title: 'Graduate Trainee Dashboard',
      status: 'idle',
      hasUnsavedChanges: false,
      icon: 'target'
    });
  }, [addTab, tabs, setActiveTab]);

  const createCCAgentsTab = useCallback((): string | null => {
    // Check if CC agents tab already exists (singleton)
    const existingTab = tabs.find(tab => tab.type === 'agents-full');
    if (existingTab) {
      setActiveTab(existingTab.id);
      return existingTab.id;
    }

    return addTab({
      type: 'agents-full',
      title: 'CC Agents',
      status: 'idle',
      hasUnsavedChanges: false,
      icon: 'bot'
    });
  }, [addTab, tabs, setActiveTab]);

  const createDataCenterTab = useCallback((): string | null => {
    // Check if data center tab already exists (singleton)
    const existingTab = tabs.find(tab => tab.type === 'data-center');
    if (existingTab) {
      setActiveTab(existingTab.id);
      return existingTab.id;
    }

    return addTab({
      type: 'data-center',
      title: 'Data Center',
      status: 'idle',
      hasUnsavedChanges: false,
      icon: 'database'
    });
  }, [addTab, tabs, setActiveTab]);

  const createAnalyticsTab = useCallback((): string | null => {
    // Check if analytics tab already exists (singleton)
    const existingTab = tabs.find(tab => tab.type === 'analytics');
    if (existingTab) {
      setActiveTab(existingTab.id);
      return existingTab.id;
    }

    return addTab({
      type: 'analytics',
      title: 'Analytics',
      status: 'idle',
      hasUnsavedChanges: false,
      icon: 'chart'
    });
  }, [addTab, tabs, setActiveTab]);

  const createWorkflowDesignerTab = useCallback((): string | null => {
    // Check if workflow designer tab already exists (singleton)
    const existingTab = tabs.find(tab => tab.type === 'workflow-designer');
    if (existingTab) {
      setActiveTab(existingTab.id);
      return existingTab.id;
    }

    return addTab({
      type: 'workflow-designer',
      title: 'Workflow Designer',
      status: 'idle',
      hasUnsavedChanges: false,
      icon: 'workflow'
    });
  }, [addTab, tabs, setActiveTab]);

  const createAgentsOverviewTab = useCallback((): string | null => {
    // Check if agents full tab already exists (singleton)
    const existingTab = tabs.find(tab => tab.type === 'agents-full');
    if (existingTab) {
      setActiveTab(existingTab.id);
      return existingTab.id;
    }

    return addTab({
      type: 'agents-full',
      title: 'CC Agents',
      status: 'idle',
      hasUnsavedChanges: false,
      icon: 'bot'
    });
  }, [addTab, tabs, setActiveTab]);

  const createMCPOverviewTab = useCallback((): string | null => {
    // Check if MCP overview tab already exists (singleton)
    const existingTab = tabs.find(tab => tab.type === 'mcp-overview');
    if (existingTab) {
      setActiveTab(existingTab.id);
      return existingTab.id;
    }

    return addTab({
      type: 'mcp-overview',
      title: 'MCP Servers',
      status: 'idle',
      hasUnsavedChanges: false,
      icon: 'server'
    });
  }, [addTab, tabs, setActiveTab]);

  const createSettingsOverviewTab = useCallback((): string | null => {
    // Check if settings overview tab already exists (singleton)
    const existingTab = tabs.find(tab => tab.type === 'settings-overview');
    if (existingTab) {
      setActiveTab(existingTab.id);
      return existingTab.id;
    }

    return addTab({
      type: 'settings-overview',
      title: 'Settings',
      status: 'idle',
      hasUnsavedChanges: false,
      icon: 'settings'
    });
  }, [addTab, tabs, setActiveTab]);

  const createLighthouseLMHubTab = useCallback((): string | null => {
    // Check if lighthouse-lm hub tab already exists (singleton)
    const existingTab = tabs.find(tab => tab.type === 'lighthouse-lm');
    if (existingTab) {
      setActiveTab(existingTab.id);
      return existingTab.id;
    }

    return addTab({
      type: 'lighthouse-lm',
      title: 'LighthouseLM',
      status: 'idle',
      hasUnsavedChanges: false,
      icon: 'lightbulb'
    });
  }, [addTab, tabs, setActiveTab]);

  const createLightbulbTab = useCallback((): string | null => {
    // Check if second-brain tab already exists (singleton)
    const existingTab = tabs.find(tab => tab.type === 'second-brain');
    if (existingTab) {
      setActiveTab(existingTab.id);
      return existingTab.id;
    }

    return addTab({
      type: 'second-brain',
      title: 'Lightbulb',
      status: 'idle',
      hasUnsavedChanges: false,
      icon: 'brain'
    });
  }, [addTab, tabs, setActiveTab]);

  const createLightbulbGridTab = useCallback((): string | null => {
    // Check if lightbulb-grid tab already exists (singleton)
    const existingTab = tabs.find(tab => tab.type === 'lightbulb-grid');
    if (existingTab) {
      setActiveTab(existingTab.id);
      return existingTab.id;
    }

    return addTab({
      type: 'lightbulb-grid',
      title: 'Lightbulb Grid',
      status: 'idle',
      hasUnsavedChanges: false,
      icon: 'grid-3x3'
    });
  }, [addTab, tabs, setActiveTab]);

  const createFinancialDashboardTab = useCallback((): string | null => {
    // Check if financial-dashboard tab already exists (singleton)
    const existingTab = tabs.find(tab => tab.type === 'financial-dashboard');
    if (existingTab) {
      setActiveTab(existingTab.id);
      return existingTab.id;
    }

    return addTab({
      type: 'financial-dashboard',
      title: 'Financial Dashboard',
      status: 'idle',
      hasUnsavedChanges: false,
      icon: 'trending-up'
    });
  }, [addTab, tabs, setActiveTab]);

  const createELearningDesignBoardTab = useCallback((): string | null => {
    // Check if elearning-design-board tab already exists (singleton)
    const existingTab = tabs.find(tab => tab.type === 'elearning-design-board');
    if (existingTab) {
      setActiveTab(existingTab.id);
      return existingTab.id;
    }

    return addTab({
      type: 'elearning-design-board',
      title: 'E-Learning Design Board',
      status: 'idle',
      hasUnsavedChanges: false,
      icon: 'layout'
    });
  }, [addTab, tabs, setActiveTab]);

  const createContentCreatorTab = useCallback((): string | null => {
    // Check if content-creator tab already exists (singleton)
    const existingTab = tabs.find(tab => tab.type === 'content-creator');
    if (existingTab) {
      setActiveTab(existingTab.id);
      return existingTab.id;
    }

    return addTab({
      type: 'content-creator',
      title: 'Content Creator',
      status: 'idle',
      hasUnsavedChanges: false,
      icon: 'edit'
    });
  }, [addTab, tabs, setActiveTab]);

  const createContentCreatorsHubTab = useCallback((): string | null => {
    // Check if content-creators-hub tab already exists (singleton)
    const existingTab = tabs.find(tab => tab.type === 'content-creators-hub');
    if (existingTab) {
      setActiveTab(existingTab.id);
      return existingTab.id;
    }

    return addTab({
      type: 'content-creators-hub',
      title: 'Content Creators Hub',
      status: 'idle',
      hasUnsavedChanges: false,
      icon: 'book-open'
    });
  }, [addTab, tabs, setActiveTab]);

  const closeTab = useCallback(async (id: string, force = false): Promise<boolean> => {
    const tab = getTabById(id);
    if (!tab) return true;

    // Check for unsaved changes
    if (!force && tab.hasUnsavedChanges) {
      // In a real implementation, you'd show a confirmation dialog here
      const confirmed = window.confirm(`Tab "${tab.title}" has unsaved changes. Close anyway?`);
      if (!confirmed) return false;
    }

    removeTab(id);
    return true;
  }, [getTabById, removeTab]);

  const closeCurrentTab = useCallback(async (): Promise<boolean> => {
    if (!activeTabId) return true;
    return closeTab(activeTabId);
  }, [activeTabId, closeTab]);

  const switchToNextTab = useCallback(() => {
    if (tabs.length === 0) return;
    
    const currentIndex = tabs.findIndex(tab => tab.id === activeTabId);
    const nextIndex = (currentIndex + 1) % tabs.length;
    setActiveTab(tabs[nextIndex].id);
  }, [tabs, activeTabId, setActiveTab]);

  const switchToPreviousTab = useCallback(() => {
    if (tabs.length === 0) return;
    
    const currentIndex = tabs.findIndex(tab => tab.id === activeTabId);
    const previousIndex = currentIndex === 0 ? tabs.length - 1 : currentIndex - 1;
    setActiveTab(tabs[previousIndex].id);
  }, [tabs, activeTabId, setActiveTab]);

  const switchToTabByIndex = useCallback((index: number) => {
    if (index >= 0 && index < tabs.length) {
      setActiveTab(tabs[index].id);
    }
  }, [tabs, setActiveTab]);

  const updateTabTitle = useCallback((id: string, title: string) => {
    updateTab(id, { title });
  }, [updateTab]);

  const updateTabStatus = useCallback((id: string, status: Tab['status']) => {
    updateTab(id, { status });
  }, [updateTab]);

  const markTabAsChanged = useCallback((id: string, hasChanges: boolean) => {
    updateTab(id, { hasUnsavedChanges: hasChanges });
  }, [updateTab]);

  const findTabBySessionId = useCallback((sessionId: string): Tab | undefined => {
    return tabs.find(tab => tab.type === 'chat' && tab.sessionId === sessionId);
  }, [tabs]);

  const findTabByAgentRunId = useCallback((agentRunId: string): Tab | undefined => {
    return tabs.find(tab => tab.type === 'agent' && tab.agentRunId === agentRunId);
  }, [tabs]);

  const findTabByType = useCallback((type: Tab['type']): Tab | undefined => {
    return tabs.find(tab => tab.type === type);
  }, [tabs]);

  const canAddTab = useCallback((): boolean => {
    return tabs.length < 20; // MAX_TABS from context
  }, [tabs.length]);

  return {
    // State
    tabs,
    activeTab,
    activeTabId,
    tabCount,
    chatTabCount,
    agentTabCount,
    
    // Operations
    createChatTab,
    createAgentTab,
    createAgentExecutionTab,
    createProjectsTab,
    createUsageTab,
    createMCPTab,
    createMCPOverviewTab,
    createSettingsTab,
    createSettingsOverviewTab,
    createAgentsOverviewTab,
    createClaudeMdTab,
    createClaudeFileTab,
    createCreateAgentTab,
    createImportAgentTab,
    createTrainingBudgetTab,
    createTrainingDashboardTab,
    createLDProfessionalTNATab,
    createGraduateTraineeTrackerTab,
    createGraduateTraineeTab,
    createCCAgentsTab,
    createLighthouseLMHubTab,
    createDataCenterTab,
    createAnalyticsTab,
    createWorkflowDesignerTab,
    createLightbulbTab,
    createLightbulbGridTab,
    createFinancialDashboardTab,
    createELearningDesignBoardTab,
    createContentCreatorTab,
    createContentCreatorsHubTab,
    closeTab,
    closeCurrentTab,
    switchToTab: setActiveTab,
    switchToNextTab,
    switchToPreviousTab,
    switchToTabByIndex,
    updateTab,
    updateTabTitle,
    updateTabStatus,
    markTabAsChanged,
    findTabBySessionId,
    findTabByAgentRunId,
    findTabByType,
    canAddTab
  };
};