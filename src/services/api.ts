import { CompetencyModel, SkillMatrix, CompetencyAssessmentRequest, AssessmentResponse } from '@/backend/types/competency';
import { Template, TemplateSearchFilters } from '@/backend/types/template';
import { ApiResponse, PaginatedResponse, QueryParams } from '@/backend/types/api';

const API_BASE_URL = (import.meta as any).env?.VITE_API_URL || 'http://localhost:3001/api';

class ApiService {
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${API_BASE_URL}${endpoint}`;
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  // Competency Model APIs
  async getCompetencyModels(params?: QueryParams): Promise<PaginatedResponse<CompetencyModel>> {
    const queryString = params ? new URLSearchParams(params as any).toString() : '';
    const endpoint = `/competency/models${queryString ? `?${queryString}` : ''}`;
    const response = await this.request<PaginatedResponse<CompetencyModel>>(endpoint);
    return response.data!;
  }

  async getCompetencyModel(id: string): Promise<ApiResponse<CompetencyModel>> {
    return this.request<CompetencyModel>(`/competency/models/${id}`);
  }

  async createCompetencyModel(model: Omit<CompetencyModel, 'id'>): Promise<ApiResponse<CompetencyModel>> {
    return this.request<CompetencyModel>('/competency/models', {
      method: 'POST',
      body: JSON.stringify(model),
    });
  }

  async updateCompetencyModel(id: string, model: Partial<CompetencyModel>): Promise<ApiResponse<CompetencyModel>> {
    return this.request<CompetencyModel>(`/competency/models/${id}`, {
      method: 'PUT',
      body: JSON.stringify(model),
    });
  }

  async deleteCompetencyModel(id: string): Promise<ApiResponse<void>> {
    return this.request<void>(`/competency/models/${id}`, {
      method: 'DELETE',
    });
  }

  // Skill Matrix APIs
  async getSkillMatrices(params?: QueryParams): Promise<PaginatedResponse<SkillMatrix>> {
    const queryString = params ? new URLSearchParams(params as any).toString() : '';
    const endpoint = `/competency/matrices${queryString ? `?${queryString}` : ''}`;
    const response = await this.request<PaginatedResponse<SkillMatrix>>(endpoint);
    return response.data!;
  }

  async getSkillMatrix(id: string): Promise<ApiResponse<SkillMatrix>> {
    return this.request<SkillMatrix>(`/competency/matrices/${id}`);
  }

  async createSkillMatrix(matrix: Omit<SkillMatrix, 'id'>): Promise<ApiResponse<SkillMatrix>> {
    return this.request<SkillMatrix>('/competency/matrices', {
      method: 'POST',
      body: JSON.stringify(matrix),
    });
  }

  async updateSkillMatrix(id: string, matrix: Partial<SkillMatrix>): Promise<ApiResponse<SkillMatrix>> {
    return this.request<SkillMatrix>(`/competency/matrices/${id}`, {
      method: 'PUT',
      body: JSON.stringify(matrix),
    });
  }

  async deleteSkillMatrix(id: string): Promise<ApiResponse<void>> {
    return this.request<void>(`/competency/matrices/${id}`, {
      method: 'DELETE',
    });
  }

  // Assessment APIs
  async createAssessment(assessment: CompetencyAssessmentRequest): Promise<ApiResponse<AssessmentResponse>> {
    return this.request<AssessmentResponse>('/competency/assessment', {
      method: 'POST',
      body: JSON.stringify(assessment),
    });
  }

  async getAssessmentResults(assessmentId: string): Promise<ApiResponse<AssessmentResponse>> {
    return this.request<AssessmentResponse>(`/competency/assessment/${assessmentId}`);
  }

  // Analytics APIs
  async getCompetencyAnalytics(params?: QueryParams): Promise<ApiResponse<any>> {
    const queryString = params ? new URLSearchParams(params as any).toString() : '';
    const endpoint = `/competency/analytics${queryString ? `?${queryString}` : ''}`;
    return this.request<any>(endpoint);
  }

  // Import/Export APIs
  async exportCompetencyModel(id: string, format: 'json' | 'excel' = 'json'): Promise<Blob> {
    const response = await fetch(`${API_BASE_URL}/competency/models/${id}/export?format=${format}`);
    if (!response.ok) {
      throw new Error(`Export failed: ${response.statusText}`);
    }
    return response.blob();
  }

  async importCompetencyModel(file: File): Promise<ApiResponse<CompetencyModel>> {
    const formData = new FormData();
    formData.append('file', file);

    return this.request<CompetencyModel>('/competency/models/import', {
      method: 'POST',
      body: formData,
      headers: {}, // Remove Content-Type to let browser set it for FormData
    });
  }

  async exportSkillMatrix(id: string, format: 'json' | 'excel' = 'json'): Promise<Blob> {
    const response = await fetch(`${API_BASE_URL}/competency/matrices/${id}/export?format=${format}`);
    if (!response.ok) {
      throw new Error(`Export failed: ${response.statusText}`);
    }
    return response.blob();
  }

  async importSkillMatrix(file: File): Promise<ApiResponse<SkillMatrix>> {
    const formData = new FormData();
    formData.append('file', file);

    return this.request<SkillMatrix>('/competency/matrices/import', {
      method: 'POST',
      body: formData,
      headers: {}, // Remove Content-Type to let browser set it for FormData
    });
  }

  // Template APIs
  async getTemplates(params?: QueryParams): Promise<PaginatedResponse<Template>> {
    const queryString = params ? new URLSearchParams(params as any).toString() : '';
    const endpoint = `/templates${queryString ? `?${queryString}` : ''}`;
    const response = await this.request<PaginatedResponse<Template>>(endpoint);
    return response.data!;
  }

  async getTemplate(id: string): Promise<ApiResponse<Template>> {
    return this.request<Template>(`/templates/${id}`);
  }

  async createTemplate(template: Omit<Template, 'id'>): Promise<ApiResponse<Template>> {
    return this.request<Template>('/templates', {
      method: 'POST',
      body: JSON.stringify(template),
    });
  }

  async updateTemplate(id: string, template: Partial<Template>): Promise<ApiResponse<Template>> {
    return this.request<Template>(`/templates/${id}`, {
      method: 'PUT',
      body: JSON.stringify(template),
    });
  }

  async deleteTemplate(id: string): Promise<ApiResponse<void>> {
    return this.request<void>(`/templates/${id}`, {
      method: 'DELETE',
    });
  }

  async searchTemplates(filters: TemplateSearchFilters): Promise<PaginatedResponse<Template>> {
    const response = await this.request<PaginatedResponse<Template>>('/templates/search', {
      method: 'POST',
      body: JSON.stringify(filters),
    });
    return response.data!;
  }

  async uploadTemplate(file: File, metadata?: any): Promise<ApiResponse<Template>> {
    const formData = new FormData();
    formData.append('file', file);
    if (metadata) {
      formData.append('metadata', JSON.stringify(metadata));
    }

    return this.request<Template>('/templates/upload', {
      method: 'POST',
      body: formData,
      headers: {}, // Remove Content-Type to let browser set it for FormData
    });
  }

  async downloadTemplate(id: string): Promise<Blob> {
    const response = await fetch(`${API_BASE_URL}/templates/${id}/download`);
    if (!response.ok) {
      throw new Error(`Download failed: ${response.statusText}`);
    }
    return response.blob();
  }

  async exportTemplate(id: string, format: 'json' | 'pdf' | 'docx' = 'json'): Promise<Blob> {
    const response = await fetch(`${API_BASE_URL}/templates/${id}/export?format=${format}`);
    if (!response.ok) {
      throw new Error(`Export failed: ${response.statusText}`);
    }
    return response.blob();
  }

  // Template Analytics
  async getTemplateAnalytics(id: string): Promise<ApiResponse<any>> {
    return this.request<any>(`/templates/${id}/analytics`);
  }

  async trackTemplateUsage(id: string, action: string): Promise<ApiResponse<void>> {
    return this.request<void>(`/templates/${id}/usage`, {
      method: 'POST',
      body: JSON.stringify({ action, timestamp: new Date().toISOString() }),
    });
  }

  // Template Collaboration
  async shareTemplate(id: string, shareData: any): Promise<ApiResponse<any>> {
    return this.request<any>(`/templates/${id}/share`, {
      method: 'POST',
      body: JSON.stringify(shareData),
    });
  }

  async cloneTemplate(id: string, cloneData: any): Promise<ApiResponse<Template>> {
    return this.request<Template>(`/templates/${id}/clone`, {
      method: 'POST',
      body: JSON.stringify(cloneData),
    });
  }

  async addTemplateComment(id: string, comment: any): Promise<ApiResponse<any>> {
    return this.request<any>(`/templates/${id}/comments`, {
      method: 'POST',
      body: JSON.stringify(comment),
    });
  }

  async rateTemplate(id: string, rating: number, review?: string): Promise<ApiResponse<any>> {
    return this.request<any>(`/templates/${id}/rate`, {
      method: 'POST',
      body: JSON.stringify({ rating, review }),
    });
  }
}

// Create and export a singleton instance
export const apiService = new ApiService();
export default apiService;