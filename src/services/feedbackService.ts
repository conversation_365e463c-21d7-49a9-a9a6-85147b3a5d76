interface FeedbackData {
  id: string;
  type: 'bug' | 'feature' | 'improvement' | 'general';
  title: string;
  description: string;
  rating?: number; // 1-5 stars
  category: string;
  userAgent: string;
  url: string;
  timestamp: number;
  userId?: string;
  email?: string;
  attachments?: File[];
  metadata: {
    version: string;
    platform: string;
    screenResolution: string;
    browserInfo: string;
  };
}

interface AnalyticsEvent {
  id: string;
  event: string;
  category: string;
  action: string;
  label?: string;
  value?: number;
  timestamp: number;
  userId?: string;
  sessionId: string;
  metadata: Record<string, any>;
}

interface UserSession {
  id: string;
  startTime: number;
  endTime?: number;
  pageViews: number;
  interactions: number;
  features: string[];
  duration: number;
}

class FeedbackService {
  private sessionId: string;
  private userId?: string;
  private currentSession: UserSession;
  private eventQueue: AnalyticsEvent[] = [];
  private isOnline = navigator.onLine;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.currentSession = this.initializeSession();
    this.setupEventListeners();
    this.startSessionTracking();
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Initialize user session
   */
  private initializeSession(): UserSession {
    return {
      id: this.sessionId,
      startTime: Date.now(),
      pageViews: 1,
      interactions: 0,
      features: [],
      duration: 0
    };
  }

  /**
   * Setup event listeners for automatic tracking
   */
  private setupEventListeners(): void {
    // Track online/offline status
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.flushEventQueue();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });

    // Track page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.trackEvent('session', 'page_hidden', 'visibility');
      } else {
        this.trackEvent('session', 'page_visible', 'visibility');
      }
    });

    // Track user interactions
    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      if (target.tagName === 'BUTTON' || target.closest('button')) {
        this.currentSession.interactions++;
        this.trackEvent('interaction', 'button_click', target.textContent || 'unknown');
      }
    });

    // Track errors
    window.addEventListener('error', (e) => {
      this.trackEvent('error', 'javascript_error', e.message, undefined, {
        filename: e.filename,
        lineno: e.lineno,
        colno: e.colno,
        stack: e.error?.stack
      });
    });

    // Track unhandled promise rejections
    window.addEventListener('unhandledrejection', (e) => {
      this.trackEvent('error', 'unhandled_promise_rejection', e.reason?.toString(), undefined, {
        reason: e.reason
      });
    });
  }

  /**
   * Start session tracking
   */
  private startSessionTracking(): void {
    // Update session duration every 30 seconds
    setInterval(() => {
      this.currentSession.duration = Date.now() - this.currentSession.startTime;
      this.saveSessionData();
    }, 30000);

    // Track session end on page unload
    window.addEventListener('beforeunload', () => {
      this.endSession();
    });
  }

  /**
   * Set user ID for tracking
   */
  setUserId(userId: string): void {
    this.userId = userId;
    this.trackEvent('user', 'login', 'authentication');
  }

  /**
   * Track analytics event
   */
  trackEvent(
    category: string,
    action: string,
    label?: string,
    value?: number,
    metadata: Record<string, any> = {}
  ): void {
    const event: AnalyticsEvent = {
      id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      event: `${category}_${action}`,
      category,
      action,
      label,
      value,
      timestamp: Date.now(),
      userId: this.userId,
      sessionId: this.sessionId,
      metadata: {
        ...metadata,
        url: window.location.href,
        userAgent: navigator.userAgent,
        referrer: document.referrer
      }
    };

    this.eventQueue.push(event);

    // Flush queue if online and has events
    if (this.isOnline && this.eventQueue.length >= 10) {
      this.flushEventQueue();
    }
  }

  /**
   * Track feature usage
   */
  trackFeatureUsage(featureName: string, action: string, metadata: Record<string, any> = {}): void {
    if (!this.currentSession.features.includes(featureName)) {
      this.currentSession.features.push(featureName);
    }

    this.trackEvent('feature', action, featureName, undefined, {
      ...metadata,
      sessionFeatures: this.currentSession.features
    });
  }

  /**
   * Track page view
   */
  trackPageView(pageName: string, metadata: Record<string, any> = {}): void {
    this.currentSession.pageViews++;
    this.trackEvent('navigation', 'page_view', pageName, undefined, {
      ...metadata,
      totalPageViews: this.currentSession.pageViews
    });
  }

  /**
   * Submit user feedback
   */
  async submitFeedback(feedback: Omit<FeedbackData, 'id' | 'timestamp' | 'userAgent' | 'url' | 'metadata'>): Promise<void> {
    const feedbackData: FeedbackData = {
      ...feedback,
      id: `feedback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: this.userId,
      metadata: {
        version: (import.meta as any).env?.VITE_APP_VERSION || '1.0.0',
        platform: navigator.platform,
        screenResolution: `${screen.width}x${screen.height}`,
        browserInfo: this.getBrowserInfo()
      }
    };

    try {
      if (this.isOnline) {
        await this.sendFeedback(feedbackData);
      } else {
        this.storeFeedbackOffline(feedbackData);
      }

      this.trackEvent('feedback', 'submitted', feedback.type, feedback.rating);
    } catch (error) {
      this.storeFeedbackOffline(feedbackData);
    }
  }

  /**
   * Get browser information
   */
  private getBrowserInfo(): string {
    const ua = navigator.userAgent;
    let browser = 'Unknown';

    if (ua.includes('Chrome')) browser = 'Chrome';
    else if (ua.includes('Firefox')) browser = 'Firefox';
    else if (ua.includes('Safari')) browser = 'Safari';
    else if (ua.includes('Edge')) browser = 'Edge';

    return browser;
  }

  /**
   * Send feedback to server
   */
  private async sendFeedback(feedback: FeedbackData): Promise<void> {
    const formData = new FormData();
    formData.append('feedback', JSON.stringify(feedback));

    if (feedback.attachments) {
      feedback.attachments.forEach((file, index) => {
        formData.append(`attachment_${index}`, file);
      });
    }

    const response = await fetch('/api/feedback', {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      throw new Error(`Failed to submit feedback: ${response.statusText}`);
    }
  }

  /**
   * Store feedback offline for later submission
   */
  private storeFeedbackOffline(feedback: FeedbackData): void {
    try {
      const offlineFeedback = JSON.parse(localStorage.getItem('offline_feedback') || '[]');
      offlineFeedback.push(feedback);
      localStorage.setItem('offline_feedback', JSON.stringify(offlineFeedback));
    } catch (error) {
    }
  }

  /**
   * Flush event queue to server
   */
  private async flushEventQueue(): void {
    if (this.eventQueue.length === 0 || !this.isOnline) {
      return;
    }

    // Temporarily disabled - analytics endpoint not implemented yet
    // This prevents 404 errors in the console
    // Check if we're in development mode
    const isDev = window.location.hostname === 'localhost' || 
                  window.location.hostname === '127.0.0.1' ||
                  window.location.hostname.includes('localhost');
    
    if (isDev) {
      // In development, just clear the queue without sending
      this.eventQueue = [];
      return;
    }

    const events = [...this.eventQueue];
    this.eventQueue = [];

    try {
      const response = await fetch('/api/analytics/events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ events })
      });
      
      // If endpoint doesn't exist, silently fail and don't retry
      if (response.status === 404) {
        return;
      }
      
      if (!response.ok) {
        throw new Error(`Analytics request failed: ${response.status}`);
      }
    } catch (error) {
      // Only log in development, silently fail in production
      if (window.location.hostname === 'localhost') {
      }
      // Don't re-add events to queue to prevent memory buildup
    }
  }

  /**
   * Save session data
   */
  private saveSessionData(): void {
    try {
      localStorage.setItem('current_session', JSON.stringify(this.currentSession));
    } catch (error) {
    }
  }

  /**
   * End current session
   */
  private endSession(): void {
    this.currentSession.endTime = Date.now();
    this.currentSession.duration = this.currentSession.endTime - this.currentSession.startTime;

    this.trackEvent('session', 'ended', 'session_end', this.currentSession.duration, {
      pageViews: this.currentSession.pageViews,
      interactions: this.currentSession.interactions,
      features: this.currentSession.features
    });

    this.flushEventQueue();
    this.saveSessionData();
  }

  /**
   * Get session analytics
   */
  getSessionAnalytics(): UserSession {
    return { ...this.currentSession };
  }

  /**
   * Get feedback statistics
   */
  getFeedbackStats(): {
    totalFeedback: number;
    averageRating: number;
    feedbackByType: Record<string, number>;
    recentFeedback: FeedbackData[];
  } {
    try {
      const offlineFeedback = JSON.parse(localStorage.getItem('offline_feedback') || '[]');
      const feedbackByType: Record<string, number> = {};
      let totalRating = 0;
      let ratedFeedback = 0;

      offlineFeedback.forEach((feedback: FeedbackData) => {
        feedbackByType[feedback.type] = (feedbackByType[feedback.type] || 0) + 1;
        if (feedback.rating) {
          totalRating += feedback.rating;
          ratedFeedback++;
        }
      });

      return {
        totalFeedback: offlineFeedback.length,
        averageRating: ratedFeedback > 0 ? totalRating / ratedFeedback : 0,
        feedbackByType,
        recentFeedback: offlineFeedback.slice(-10)
      };
    } catch (error) {
      return {
        totalFeedback: 0,
        averageRating: 0,
        feedbackByType: {},
        recentFeedback: []
      };
    }
  }

  /**
   * Export analytics data
   */
  exportAnalyticsData(): {
    session: UserSession;
    events: AnalyticsEvent[];
    feedback: FeedbackData[];
  } {
    try {
      const offlineFeedback = JSON.parse(localStorage.getItem('offline_feedback') || '[]');
      
      return {
        session: this.currentSession,
        events: this.eventQueue,
        feedback: offlineFeedback
      };
    } catch (error) {
      return {
        session: this.currentSession,
        events: [],
        feedback: []
      };
    }
  }

  /**
   * Clear analytics data
   */
  clearAnalyticsData(): void {
    try {
      localStorage.removeItem('offline_feedback');
      localStorage.removeItem('current_session');
      this.eventQueue = [];
      this.currentSession = this.initializeSession();
    } catch (error) {
    }
  }
}

export const feedbackService = new FeedbackService();