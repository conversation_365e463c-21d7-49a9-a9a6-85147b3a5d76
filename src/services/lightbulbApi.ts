// Backend API service for Lightbulb functionality
import express, { Request, Response } from 'express';
import dotenv from 'dotenv';
import cors from 'cors';

// Load environment variables
dotenv.config();

const app = express();
const PORT = (import.meta as any).env?.VITE_PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// In-memory storage (in a real app, this would be a database)
const savedContent: Array<{id: string, title: string, content: string, createdAt: Date}> = [];
const userCount = 13523; // Starting user count

// API Routes

// Get user count
app.get('/api/user-count', (req: Request, res: Response) => {
  res.json({ count: userCount });
});

// Get all saved content
app.get('/api/content', (req: Request, res: Response) => {
  res.json(savedContent);
});

// Save new content
app.post('/api/content', (req: Request, res: Response) => {
  const { title, content } = req.body;
  
  if (!content) {
    return res.status(400).json({ error: 'Content is required' });
  }
  
  const newContent = {
    id: Date.now().toString(),
    title: title || `Item ${savedContent.length + 1}`,
    content,
    createdAt: new Date()
  };
  
  savedContent.push(newContent);
  res.status(201).json(newContent);
});

// Delete content
app.delete('/api/content/:id', (req: Request, res: Response) => {
  const { id } = req.params;
  const index = savedContent.findIndex(item => item.id === id);
  
  if (index === -1) {
    return res.status(404).json({ error: 'Content not found' });
  }
  
  savedContent.splice(index, 1);
  res.status(204).send();
});

// AI chat endpoint (simulated)
app.post('/api/chat', async (req: Request, res: Response) => {
  const { message, contextIds } = req.body;
  
  if (!message) {
    return res.status(400).json({ error: 'Message is required' });
  }
  
  // In a real implementation, this would call an AI service like OpenAI or Anthropic
  // For now, we'll simulate a response
  
  // If context is provided, we would use it to inform the response
  let responseText = `I've processed your query: "${message}". `;
  
  if (contextIds && contextIds.length > 0) {
    const contextItems = savedContent.filter(item => contextIds.includes(item.id));
    if (contextItems.length > 0) {
      responseText += `Based on your saved content about "${contextItems[0].title}", I can tell you that `;
      responseText += "this is a simulated response. In a real implementation, this would connect to an AI service like GPT-4 or Claude to provide insights based on your saved content.";
    } else {
      responseText += "I couldn't find the specific content you referenced, but ";
      responseText += "this is a simulated response from the AI.";
    }
  } else {
    responseText += "This is a simulated response from the AI. In a real implementation, this would connect to an AI service like GPT-4 or Claude.";
  }
  
  // Simulate API delay
  setTimeout(() => {
    res.json({
      id: Date.now().toString(),
      message: responseText,
      timestamp: new Date()
    });
  }, 1000);
});

// Health check endpoint
app.get('/api/health', (req: Request, res: Response) => {
  res.json({ status: 'OK', timestamp: new Date() });
});

// Start server
app.listen(PORT, () => {
});

// Export for testing
export default app;