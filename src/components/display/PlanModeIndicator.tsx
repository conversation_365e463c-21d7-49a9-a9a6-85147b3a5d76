import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  GitBranch,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Loader2,
  Play,
  Pause,
  FileText,
  Eye,
  EyeOff
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

interface PlanStep {
  id: string;
  description: string;
  status: "pending" | "in_progress" | "completed" | "skipped";
  estimatedTime?: number;
  actualTime?: number;
}

interface PlanModeIndicatorProps {
  /**
   * Whether plan mode is currently active
   */
  isActive: boolean;
  /**
   * Current plan being executed
   */
  plan?: {
    title: string;
    steps: PlanStep[];
    createdAt: Date;
  };
  /**
   * Callback to toggle plan mode
   */
  onToggle?: () => void;
  /**
   * Callback to approve the plan
   */
  onApprove?: () => void;
  /**
   * Callback to reject the plan
   */
  onReject?: () => void;
  /**
   * Optional className for styling
   */
  className?: string;
  /**
   * Whether to show detailed view
   */
  showDetails?: boolean;
}

/**
 * PlanModeIndicator component displays the current state of plan mode
 * Shows plan progress, allows approval/rejection, and provides visual feedback
 */
export const PlanModeIndicator: React.FC<PlanModeIndicatorProps> = ({
  isActive,
  plan,
  onToggle,
  onApprove,
  onReject,
  className,
  showDetails = true
}) => {
  const [collapsed, setCollapsed] = useState(false);
  const [elapsedTime, setElapsedTime] = useState(0);

  // Update elapsed time every second when plan is active
  useEffect(() => {
    if (!isActive || !plan) return;

    const interval = setInterval(() => {
      setElapsedTime(Date.now() - plan.createdAt.getTime());
    }, 1000);

    return () => clearInterval(interval);
  }, [isActive, plan]);

  // Calculate plan progress
  const getProgress = () => {
    if (!plan) return 0;
    const completed = plan.steps.filter(s => s.status === "completed").length;
    return (completed / plan.steps.length) * 100;
  };

  // Format elapsed time
  const formatTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  // Get status icon
  const getStatusIcon = (status: PlanStep["status"]) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "in_progress":
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      case "skipped":
        return <XCircle className="h-4 w-4 text-gray-400" />;
      default:
        return <div className="h-4 w-4 rounded-full border-2 border-gray-300" />;
    }
  };

  // Get current step
  const getCurrentStep = () => {
    if (!plan) return null;
    return plan.steps.find(s => s.status === "in_progress") || 
           plan.steps.find(s => s.status === "pending");
  };

  if (!isActive && !plan) {
    return null;
  }

  return (
    <AnimatePresence>
      {isActive && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          className={cn("fixed top-4 right-4 z-50 max-w-md", className)}
        >
          <Card className="shadow-lg border-2 border-primary/20">
            <CardContent className="p-4">
              {/* Header */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <GitBranch className="h-5 w-5 text-primary" />
                  <h3 className="font-semibold">Plan Mode</h3>
                  <Badge variant="secondary" className="text-xs">
                    Active
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setCollapsed(!collapsed)}
                  >
                    {collapsed ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                  </Button>
                  {onToggle && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={onToggle}
                    >
                      <Pause className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>

              {!collapsed && (
                <>
                  {/* Plan Title and Timer */}
                  {plan && (
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium">{plan.title}</p>
                        <span className="text-xs text-muted-foreground">
                          {formatTime(elapsedTime)}
                        </span>
                      </div>

                      {/* Progress Bar */}
                      <div className="space-y-1">
                        <Progress value={getProgress()} className="h-2" />
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <span>
                            {plan.steps.filter(s => s.status === "completed").length} of {plan.steps.length} steps
                          </span>
                          <span>{Math.round(getProgress())}%</span>
                        </div>
                      </div>

                      {/* Current Step */}
                      {getCurrentStep() && (
                        <div className="p-2 bg-muted rounded-lg">
                          <div className="flex items-start gap-2">
                            {getStatusIcon(getCurrentStep()!.status)}
                            <div className="flex-1">
                              <p className="text-sm">{getCurrentStep()!.description}</p>
                              {getCurrentStep()!.estimatedTime && (
                                <p className="text-xs text-muted-foreground mt-1">
                                  Est. {getCurrentStep()!.estimatedTime}min
                                </p>
                              )}
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Steps List (if showDetails is true) */}
                      {showDetails && (
                        <div className="space-y-2 max-h-48 overflow-y-auto">
                          {plan.steps.map((step) => (
                            <div
                              key={step.id}
                              className={cn(
                                "flex items-start gap-2 p-2 rounded transition-all",
                                step.status === "in_progress" && "bg-blue-50 dark:bg-blue-900/20",
                                step.status === "completed" && "opacity-70"
                              )}
                            >
                              {getStatusIcon(step.status)}
                              <div className="flex-1 min-w-0">
                                <p className={cn(
                                  "text-xs truncate",
                                  step.status === "completed" && "line-through"
                                )}>
                                  {step.description}
                                </p>
                                {step.actualTime && (
                                  <p className="text-xs text-muted-foreground">
                                    {step.actualTime}min
                                  </p>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      )}

                      {/* Action Buttons */}
                      {(onApprove || onReject) && getProgress() === 0 && (
                        <div className="flex gap-2 pt-2 border-t">
                          {onApprove && (
                            <Button
                              size="sm"
                              className="flex-1"
                              onClick={onApprove}
                            >
                              <CheckCircle className="h-4 w-4 mr-1" />
                              Approve
                            </Button>
                          )}
                          {onReject && (
                            <Button
                              size="sm"
                              variant="outline"
                              className="flex-1"
                              onClick={onReject}
                            >
                              <XCircle className="h-4 w-4 mr-1" />
                              Reject
                            </Button>
                          )}
                        </div>
                      )}
                    </div>
                  )}

                  {/* No Plan Message */}
                  {!plan && (
                    <div className="text-center py-4">
                      <AlertTriangle className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
                      <p className="text-sm text-muted-foreground">
                        Plan mode is active but no plan is loaded
                      </p>
                    </div>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

/**
 * Minimal plan mode indicator for inline display
 */
export const PlanModeBadge: React.FC<{
  isActive: boolean;
  onClick?: () => void;
}> = ({ isActive, onClick }) => {
  if (!isActive) return null;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge
            variant="secondary"
            className="cursor-pointer animate-pulse"
            onClick={onClick}
          >
            <GitBranch className="h-3 w-3 mr-1" />
            Plan Mode
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <p>Claude is in plan mode - reviewing before execution</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};