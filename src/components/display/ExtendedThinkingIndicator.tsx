import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Brain, Sparkles } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';

interface ExtendedThinkingIndicatorProps {
  /**
   * Whether extended thinking mode is active
   */
  isActive: boolean;
  /**
   * Current thinking progress (0-100)
   */
  progress?: number;
  /**
   * Current thinking stage description
   */
  stage?: string;
  /**
   * Estimated remaining time in seconds
   */
  estimatedTime?: number;
  /**
   * Optional className for styling
   */
  className?: string;
}

/**
 * Component to display extended thinking mode status
 * Shows when <PERSON> is in deep thinking mode for complex problems
 */
export const ExtendedThinkingIndicator: React.FC<ExtendedThinkingIndicatorProps> = ({
  isActive,
  progress = 0,
  stage = 'Thinking deeply...',
  estimatedTime,
  className
}) => {
  const [dots, setDots] = useState('');
  const [sparkleIndex, setSparkleIndex] = useState(0);

  // Animate dots for loading effect
  useEffect(() => {
    if (!isActive) return;
    
    const interval = setInterval(() => {
      setDots(prev => {
        if (prev.length >= 3) return '';
        return prev + '.';
      });
    }, 500);

    return () => clearInterval(interval);
  }, [isActive]);

  // Animate sparkles
  useEffect(() => {
    if (!isActive) return;
    
    const interval = setInterval(() => {
      setSparkleIndex(prev => (prev + 1) % 3);
    }, 800);

    return () => clearInterval(interval);
  }, [isActive]);

  // Format time remaining
  const formatTime = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  return (
    <AnimatePresence>
      {isActive && (
        <motion.div
          initial={{ opacity: 0, y: -20, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -20, scale: 0.95 }}
          transition={{ duration: 0.3, ease: 'easeOut' }}
          className={cn(
            "fixed top-20 left-1/2 -translate-x-1/2 z-50",
            "bg-background/95 backdrop-blur-md border rounded-lg shadow-lg",
            "px-6 py-4 min-w-[320px] max-w-md",
            className
          )}
        >
          <div className="space-y-3">
            {/* Header */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Brain className="h-5 w-5 text-purple-500" />
                  <motion.div
                    className="absolute -top-1 -right-1"
                    animate={{
                      opacity: sparkleIndex === 0 ? 1 : 0,
                      scale: sparkleIndex === 0 ? 1 : 0.8
                    }}
                    transition={{ duration: 0.3 }}
                  >
                    <Sparkles className="h-3 w-3 text-yellow-500" />
                  </motion.div>
                  <motion.div
                    className="absolute -bottom-1 -right-1"
                    animate={{
                      opacity: sparkleIndex === 1 ? 1 : 0,
                      scale: sparkleIndex === 1 ? 1 : 0.8
                    }}
                    transition={{ duration: 0.3 }}
                  >
                    <Sparkles className="h-3 w-3 text-blue-500" />
                  </motion.div>
                  <motion.div
                    className="absolute -top-1 -left-1"
                    animate={{
                      opacity: sparkleIndex === 2 ? 1 : 0,
                      scale: sparkleIndex === 2 ? 1 : 0.8
                    }}
                    transition={{ duration: 0.3 }}
                  >
                    <Sparkles className="h-3 w-3 text-purple-500" />
                  </motion.div>
                </div>
                <div>
                  <h3 className="text-sm font-semibold">Extended Thinking</h3>
                  <p className="text-xs text-muted-foreground">
                    Analyzing complex problem{dots}
                  </p>
                </div>
              </div>
              {estimatedTime && (
                <div className="text-xs text-muted-foreground">
                  ~{formatTime(estimatedTime)}
                </div>
              )}
            </div>

            {/* Stage description */}
            <div className="text-xs text-muted-foreground">
              {stage}
            </div>

            {/* Progress bar */}
            <div className="space-y-1">
              <Progress value={progress} className="h-2" />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>Progress</span>
                <span>{Math.round(progress)}%</span>
              </div>
            </div>

            {/* Thinking stages */}
            <div className="grid grid-cols-3 gap-2 text-xs">
              <div className={cn(
                "text-center py-1 px-2 rounded",
                progress >= 0 ? "bg-purple-500/10 text-purple-500" : "bg-muted text-muted-foreground"
              )}>
                Analyzing
              </div>
              <div className={cn(
                "text-center py-1 px-2 rounded",
                progress >= 33 ? "bg-purple-500/10 text-purple-500" : "bg-muted text-muted-foreground"
              )}>
                Reasoning
              </div>
              <div className={cn(
                "text-center py-1 px-2 rounded",
                progress >= 66 ? "bg-purple-500/10 text-purple-500" : "bg-muted text-muted-foreground"
              )}>
                Synthesizing
              </div>
            </div>

            {/* Animated thinking indicator */}
            <div className="flex justify-center">
              <div className="flex gap-1">
                {[0, 1, 2, 3, 4].map((i) => (
                  <motion.div
                    key={i}
                    className="w-2 h-2 bg-purple-500 rounded-full"
                    animate={{
                      y: [0, -8, 0],
                      opacity: [0.3, 1, 0.3]
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      delay: i * 0.15,
                      ease: "easeInOut"
                    }}
                  />
                ))}
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};