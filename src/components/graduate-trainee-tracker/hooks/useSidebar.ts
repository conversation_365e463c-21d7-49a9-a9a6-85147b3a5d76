import { useState, useCallback, useEffect } from 'react';

export interface SidebarState {
  isOpen: boolean;
  isCollapsed: boolean;
  activeSection: string | null;
  pinnedSections: string[];
  width: number;
  position: 'left' | 'right';
}

export interface SidebarActions {
  open: () => void;
  close: () => void;
  toggle: () => void;
  collapse: () => void;
  expand: () => void;
  toggleCollapse: () => void;
  setActiveSection: (section: string | null) => void;
  pinSection: (section: string) => void;
  unpinSection: (section: string) => void;
  togglePinSection: (section: string) => void;
  setWidth: (width: number) => void;
  setPosition: (position: 'left' | 'right') => void;
  reset: () => void;
}

export interface UseSidebarOptions {
  defaultOpen?: boolean;
  defaultCollapsed?: boolean;
  defaultActiveSection?: string | null;
  defaultPinnedSections?: string[];
  defaultWidth?: number;
  defaultPosition?: 'left' | 'right';
  persistState?: boolean;
  storageKey?: string;
  minWidth?: number;
  maxWidth?: number;
  breakpoint?: number; // Screen width breakpoint for auto-collapse
}

const DEFAULT_OPTIONS: Required<UseSidebarOptions> = {
  defaultOpen: true,
  defaultCollapsed: false,
  defaultActiveSection: null,
  defaultPinnedSections: [],
  defaultWidth: 280,
  defaultPosition: 'left',
  persistState: true,
  storageKey: 'graduate-tracker-sidebar',
  minWidth: 200,
  maxWidth: 400,
  breakpoint: 768
};

export const useSidebar = (options: UseSidebarOptions = {}) => {
  const config = { ...DEFAULT_OPTIONS, ...options };

  // Initialize state from localStorage if persistence is enabled
  const getInitialState = (): SidebarState => {
    if (config.persistState && typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem(config.storageKey);
        if (stored) {
          const parsedState = JSON.parse(stored);
          return {
            isOpen: parsedState.isOpen ?? config.defaultOpen,
            isCollapsed: parsedState.isCollapsed ?? config.defaultCollapsed,
            activeSection: parsedState.activeSection ?? config.defaultActiveSection,
            pinnedSections: parsedState.pinnedSections ?? config.defaultPinnedSections,
            width: Math.max(config.minWidth, Math.min(config.maxWidth, parsedState.width ?? config.defaultWidth)),
            position: parsedState.position ?? config.defaultPosition
          };
        }
      } catch (error) {
        console.warn('Failed to parse sidebar state from localStorage:', error);
      }
    }

    return {
      isOpen: config.defaultOpen,
      isCollapsed: config.defaultCollapsed,
      activeSection: config.defaultActiveSection,
      pinnedSections: [...config.defaultPinnedSections],
      width: config.defaultWidth,
      position: config.defaultPosition
    };
  };

  const [state, setState] = useState<SidebarState>(getInitialState);

  // Persist state to localStorage
  const persistState = useCallback((newState: SidebarState) => {
    if (config.persistState && typeof window !== 'undefined') {
      try {
        localStorage.setItem(config.storageKey, JSON.stringify(newState));
      } catch (error) {
        console.warn('Failed to persist sidebar state to localStorage:', error);
      }
    }
  }, [config.persistState, config.storageKey]);

  // Update state and persist
  const updateState = useCallback((updates: Partial<SidebarState>) => {
    setState(prevState => {
      const newState = { ...prevState, ...updates };
      persistState(newState);
      return newState;
    });
  }, [persistState]);

  // Handle responsive behavior
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleResize = () => {
      const shouldCollapse = window.innerWidth < config.breakpoint;
      if (shouldCollapse && !state.isCollapsed) {
        updateState({ isCollapsed: true });
      } else if (!shouldCollapse && state.isCollapsed && window.innerWidth >= config.breakpoint + 100) {
        // Add some hysteresis to prevent flickering
        updateState({ isCollapsed: false });
      }
    };

    handleResize(); // Check initial size
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [config.breakpoint, state.isCollapsed, updateState]);

  // Actions
  const actions: SidebarActions = {
    open: useCallback(() => {
      updateState({ isOpen: true });
    }, [updateState]),

    close: useCallback(() => {
      updateState({ isOpen: false });
    }, [updateState]),

    toggle: useCallback(() => {
      updateState({ isOpen: !state.isOpen });
    }, [state.isOpen, updateState]),

    collapse: useCallback(() => {
      updateState({ isCollapsed: true });
    }, [updateState]),

    expand: useCallback(() => {
      updateState({ isCollapsed: false });
    }, [updateState]),

    toggleCollapse: useCallback(() => {
      updateState({ isCollapsed: !state.isCollapsed });
    }, [state.isCollapsed, updateState]),

    setActiveSection: useCallback((section: string | null) => {
      updateState({ activeSection: section });
    }, [updateState]),

    pinSection: useCallback((section: string) => {
      if (!state.pinnedSections.includes(section)) {
        updateState({ pinnedSections: [...state.pinnedSections, section] });
      }
    }, [state.pinnedSections, updateState]),

    unpinSection: useCallback((section: string) => {
      updateState({ 
        pinnedSections: state.pinnedSections.filter(s => s !== section) 
      });
    }, [state.pinnedSections, updateState]),

    togglePinSection: useCallback((section: string) => {
      if (state.pinnedSections.includes(section)) {
        updateState({ 
          pinnedSections: state.pinnedSections.filter(s => s !== section) 
        });
      } else {
        updateState({ pinnedSections: [...state.pinnedSections, section] });
      }
    }, [state.pinnedSections, updateState]),

    setWidth: useCallback((width: number) => {
      const clampedWidth = Math.max(config.minWidth, Math.min(config.maxWidth, width));
      updateState({ width: clampedWidth });
    }, [config.minWidth, config.maxWidth, updateState]),

    setPosition: useCallback((position: 'left' | 'right') => {
      updateState({ position });
    }, [updateState]),

    reset: useCallback(() => {
      const resetState: SidebarState = {
        isOpen: config.defaultOpen,
        isCollapsed: config.defaultCollapsed,
        activeSection: config.defaultActiveSection,
        pinnedSections: [...config.defaultPinnedSections],
        width: config.defaultWidth,
        position: config.defaultPosition
      };
      setState(resetState);
      persistState(resetState);
    }, [config, persistState])
  };

  // Computed properties
  const computed = {
    isVisible: state.isOpen,
    effectiveWidth: state.isCollapsed ? 60 : state.width, // Collapsed width is typically icon-only
    isMobile: typeof window !== 'undefined' ? window.innerWidth < config.breakpoint : false,
    shouldShowLabels: !state.isCollapsed,
    shouldShowTooltips: state.isCollapsed,
    isPinned: (section: string) => state.pinnedSections.includes(section),
    isActive: (section: string) => state.activeSection === section,
    canResize: !state.isCollapsed && state.isOpen,
    sidebarClasses: [
      'sidebar',
      state.isOpen ? 'sidebar--open' : 'sidebar--closed',
      state.isCollapsed ? 'sidebar--collapsed' : 'sidebar--expanded',
      `sidebar--${state.position}`
    ].join(' ')
  };

  // Keyboard shortcuts
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + B to toggle sidebar
      if ((event.ctrlKey || event.metaKey) && event.key === 'b') {
        event.preventDefault();
        actions.toggle();
      }
      
      // Ctrl/Cmd + Shift + B to toggle collapse
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'B') {
        event.preventDefault();
        actions.toggleCollapse();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [actions]);

  return {
    // State
    ...state,
    
    // Actions
    ...actions,
    
    // Computed properties
    ...computed,
    
    // Configuration
    config: {
      minWidth: config.minWidth,
      maxWidth: config.maxWidth,
      breakpoint: config.breakpoint
    }
  };
};

export default useSidebar;