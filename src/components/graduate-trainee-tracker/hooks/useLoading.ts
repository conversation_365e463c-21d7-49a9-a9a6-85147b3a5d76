import { useState, useCallback, useRef, useEffect } from 'react';

export interface LoadingState {
  isLoading: boolean;
  loadingKeys: Set<string>;
  progress: number;
  message: string;
  error: string | null;
  startTime: number | null;
  duration: number;
}

export interface LoadingOptions {
  key?: string;
  message?: string;
  showProgress?: boolean;
  minDuration?: number;
  maxDuration?: number;
  onStart?: () => void;
  onComplete?: () => void;
  onError?: (error: string) => void;
}

export interface UseLoadingOptions {
  defaultMessage?: string;
  globalKey?: string;
  persistState?: boolean;
  storageKey?: string;
  debounceDelay?: number;
  showSpinner?: boolean;
  showProgress?: boolean;
  trackDuration?: boolean;
}

const DEFAULT_OPTIONS: Required<UseLoadingOptions> = {
  defaultMessage: 'Loading...',
  globalKey: 'global',
  persistState: false,
  storageKey: 'graduate-tracker-loading',
  debounceDelay: 100,
  showSpinner: true,
  showProgress: false,
  trackDuration: true
};

export const useLoading = (options: UseLoadingOptions = {}) => {
  const config = { ...DEFAULT_OPTIONS, ...options };
  const timeoutsRef = useRef<Map<string, NodeJS.Timeout>>(new Map());
  const progressIntervalsRef = useRef<Map<string, NodeJS.Timeout>>(new Map());
  const loadingStartTimes = useRef<Map<string, number>>(new Map());

  // Initialize state
  const getInitialState = (): LoadingState => {
    if (config.persistState && typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem(config.storageKey);
        if (stored) {
          const parsedState = JSON.parse(stored);
          return {
            isLoading: parsedState.isLoading ?? false,
            loadingKeys: new Set(parsedState.loadingKeys ?? []),
            progress: parsedState.progress ?? 0,
            message: parsedState.message ?? config.defaultMessage,
            error: parsedState.error ?? null,
            startTime: parsedState.startTime ?? null,
            duration: parsedState.duration ?? 0
          };
        }
      } catch (error) {
        console.warn('Failed to parse loading state from localStorage:', error);
      }
    }

    return {
      isLoading: false,
      loadingKeys: new Set(),
      progress: 0,
      message: config.defaultMessage,
      error: null,
      startTime: null,
      duration: 0
    };
  };

  const [state, setState] = useState<LoadingState>(getInitialState);

  // Persist state to localStorage
  const persistState = useCallback((newState: LoadingState) => {
    if (config.persistState && typeof window !== 'undefined') {
      try {
        const stateToStore = {
          ...newState,
          loadingKeys: Array.from(newState.loadingKeys)
        };
        localStorage.setItem(config.storageKey, JSON.stringify(stateToStore));
      } catch (error) {
        console.warn('Failed to persist loading state to localStorage:', error);
      }
    }
  }, [config.persistState, config.storageKey]);

  // Update state and persist
  const updateState = useCallback((updates: Partial<LoadingState> | ((prev: LoadingState) => LoadingState)) => {
    setState(prevState => {
      const newState = typeof updates === 'function' ? updates(prevState) : { ...prevState, ...updates };
      persistState(newState);
      return newState;
    });
  }, [persistState]);

  // Clear timeout for a specific key
  const clearLoadingTimeout = useCallback((key: string) => {
    const timeout = timeoutsRef.current.get(key);
    if (timeout) {
      clearTimeout(timeout);
      timeoutsRef.current.delete(key);
    }
  }, []);

  // Clear progress interval for a specific key
  const clearProgressInterval = useCallback((key: string) => {
    const interval = progressIntervalsRef.current.get(key);
    if (interval) {
      clearInterval(interval);
      progressIntervalsRef.current.delete(key);
    }
  }, []);

  // Start loading for a specific key
  const startLoading = useCallback((loadingOptions: LoadingOptions = {}) => {
    const key = loadingOptions.key || config.globalKey;
    const message = loadingOptions.message || config.defaultMessage;
    const startTime = Date.now();

    // Store start time
    loadingStartTimes.current.set(key, startTime);

    // Clear any existing timeouts/intervals
    clearLoadingTimeout(key);
    clearProgressInterval(key);

    // Debounce loading state to prevent flashing
    const timeout = setTimeout(() => {
      updateState(prevState => {
        const newLoadingKeys = new Set(prevState.loadingKeys);
        newLoadingKeys.add(key);
        
        return {
          ...prevState,
          isLoading: true,
          loadingKeys: newLoadingKeys,
          message: newLoadingKeys.size === 1 ? message : prevState.message,
          error: null,
          startTime: prevState.startTime || startTime,
          progress: loadingOptions.showProgress ? 0 : prevState.progress
        };
      });

      // Start progress simulation if enabled
      if (loadingOptions.showProgress) {
        let progress = 0;
        const progressInterval = setInterval(() => {
          progress += Math.random() * 15;
          if (progress >= 90) {
            progress = 90; // Stop at 90% until completion
            clearProgressInterval(key);
          }
          
          updateState(prevState => ({
            ...prevState,
            progress: Math.min(progress, 90)
          }));
        }, 200);
        
        progressIntervalsRef.current.set(key, progressInterval);
      }

      if (loadingOptions.onStart) {
        loadingOptions.onStart();
      }
    }, config.debounceDelay);

    timeoutsRef.current.set(key, timeout);

    return key;
  }, [config, clearLoadingTimeout, clearProgressInterval, updateState]);

  // Stop loading for a specific key
  const stopLoading = useCallback((key?: string, loadingOptions: LoadingOptions = {}) => {
    const loadingKey = key || config.globalKey;
    const startTime = loadingStartTimes.current.get(loadingKey);
    const duration = startTime ? Date.now() - startTime : 0;

    // Clear timeouts and intervals
    clearLoadingTimeout(loadingKey);
    clearProgressInterval(loadingKey);
    loadingStartTimes.current.delete(loadingKey);

    const minDuration = loadingOptions.minDuration || 0;
    const actualDelay = Math.max(0, minDuration - duration);

    const completeLoading = () => {
      updateState(prevState => {
        const newLoadingKeys = new Set(prevState.loadingKeys);
        newLoadingKeys.delete(loadingKey);
        
        const isStillLoading = newLoadingKeys.size > 0;
        
        return {
          ...prevState,
          isLoading: isStillLoading,
          loadingKeys: newLoadingKeys,
          progress: loadingOptions.showProgress ? 100 : (isStillLoading ? prevState.progress : 0),
          startTime: isStillLoading ? prevState.startTime : null,
          duration: config.trackDuration ? duration : 0
        };
      });

      if (loadingOptions.onComplete) {
        loadingOptions.onComplete();
      }
    };

    if (actualDelay > 0) {
      setTimeout(completeLoading, actualDelay);
    } else {
      completeLoading();
    }
  }, [config, clearLoadingTimeout, clearProgressInterval, updateState]);

  // Set loading error
  const setError = useCallback((error: string, key?: string, loadingOptions: LoadingOptions = {}) => {
    const loadingKey = key || config.globalKey;
    
    clearLoadingTimeout(loadingKey);
    clearProgressInterval(loadingKey);
    loadingStartTimes.current.delete(loadingKey);

    updateState(prevState => {
      const newLoadingKeys = new Set(prevState.loadingKeys);
      newLoadingKeys.delete(loadingKey);
      
      return {
        ...prevState,
        isLoading: newLoadingKeys.size > 0,
        loadingKeys: newLoadingKeys,
        error,
        progress: 0,
        startTime: newLoadingKeys.size > 0 ? prevState.startTime : null
      };
    });

    if (loadingOptions.onError) {
      loadingOptions.onError(error);
    }
  }, [config, clearLoadingTimeout, clearProgressInterval, updateState]);

  // Clear error
  const clearError = useCallback(() => {
    updateState({ error: null });
  }, [updateState]);

  // Set progress manually
  const setProgress = useCallback((progress: number, key?: string) => {
    const loadingKey = key || config.globalKey;
    if (state.loadingKeys.has(loadingKey)) {
      updateState({ progress: Math.max(0, Math.min(100, progress)) });
    }
  }, [config.globalKey, state.loadingKeys, updateState]);

  // Set loading message
  const setMessage = useCallback((message: string) => {
    updateState({ message });
  }, [updateState]);

  // Check if specific key is loading
  const isLoadingKey = useCallback((key: string) => {
    return state.loadingKeys.has(key);
  }, [state.loadingKeys]);

  // Get loading duration for a specific key
  const getLoadingDuration = useCallback((key?: string) => {
    const loadingKey = key || config.globalKey;
    const startTime = loadingStartTimes.current.get(loadingKey);
    return startTime ? Date.now() - startTime : 0;
  }, [config.globalKey]);

  // Wrap async function with loading state
  const withLoading = useCallback(<T extends (...args: any[]) => Promise<any>>(
    asyncFn: T,
    loadingOptions: LoadingOptions = {}
  ): T => {
    return (async (...args: Parameters<T>) => {
      const key = startLoading(loadingOptions);
      try {
        const result = await asyncFn(...args);
        stopLoading(key, loadingOptions);
        return result;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'An error occurred';
        setError(errorMessage, key, loadingOptions);
        throw error;
      }
    }) as T;
  }, [startLoading, stopLoading, setError]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Clear all timeouts
      timeoutsRef.current.forEach(timeout => clearTimeout(timeout));
      timeoutsRef.current.clear();
      
      // Clear all intervals
      progressIntervalsRef.current.forEach(interval => clearInterval(interval));
      progressIntervalsRef.current.clear();
      
      // Clear start times
      loadingStartTimes.current.clear();
    };
  }, []);

  // Computed properties
  const computed = {
    hasError: !!state.error,
    loadingCount: state.loadingKeys.size,
    loadingKeysList: Array.from(state.loadingKeys),
    isGlobalLoading: state.loadingKeys.has(config.globalKey),
    progressPercentage: `${Math.round(state.progress)}%`,
    formattedDuration: state.duration > 0 ? `${(state.duration / 1000).toFixed(1)}s` : '',
    loadingClasses: [
      'loading',
      state.isLoading ? 'loading--active' : 'loading--inactive',
      state.error ? 'loading--error' : '',
      config.showSpinner ? 'loading--spinner' : '',
      config.showProgress ? 'loading--progress' : ''
    ].filter(Boolean).join(' ')
  };

  return {
    // State
    ...state,
    
    // Actions
    startLoading,
    stopLoading,
    setError,
    clearError,
    setProgress,
    setMessage,
    
    // Utilities
    isLoadingKey,
    getLoadingDuration,
    withLoading,
    
    // Computed properties
    ...computed,
    
    // Configuration
    config
  };
};

export default useLoading;