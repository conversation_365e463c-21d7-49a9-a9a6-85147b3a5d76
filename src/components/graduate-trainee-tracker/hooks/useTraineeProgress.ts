import { useState, useEffect, useCallback, useMemo } from 'react';
import { Trainee, QuarterlyReview, Competency, TraineeStatus, ReviewStatus, CompetencyLevel } from '../types';
import { fetchTrainees, fetchReviews, fetchCompetencies } from '../mockApi';

interface ProgressMetrics {
  overallProgress: number;
  competencyProgress: number;
  reviewProgress: number;
  milestoneProgress: number;
  timeInProgram: number;
  expectedCompletionDate: Date | null;
  isOnTrack: boolean;
  riskLevel: 'low' | 'medium' | 'high';
}

interface CompetencyProgress {
  competencyId: string;
  name: string;
  category: string;
  currentLevel: CompetencyLevel;
  targetLevel: CompetencyLevel;
  progress: number;
  lastAssessed: Date | null;
}

interface ReviewProgress {
  reviewId: string;
  quarter: number;
  year: number;
  status: ReviewStatus;
  dueDate: Date;
  completedDate: Date | null;
  overdue: boolean;
  daysOverdue: number;
}

interface UseTraineeProgressOptions {
  traineeId?: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

interface UseTraineeProgressReturn {
  trainee: Trainee | null;
  progressMetrics: ProgressMetrics | null;
  competencyProgress: CompetencyProgress[];
  reviewProgress: ReviewProgress[];
  loading: boolean;
  error: string | null;
  refreshProgress: () => Promise<void>;
  calculateOverallProgress: () => number;
  getCompetencyGaps: () => CompetencyProgress[];
  getOverdueReviews: () => ReviewProgress[];
  predictCompletionDate: () => Date | null;
  assessRiskLevel: () => 'low' | 'medium' | 'high';
}

export const useTraineeProgress = (options: UseTraineeProgressOptions = {}): UseTraineeProgressReturn => {
  const { traineeId, autoRefresh = false, refreshInterval = 300000 } = options; // 5 minutes default

  const [trainee, setTrainee] = useState<Trainee | null>(null);
  const [reviews, setReviews] = useState<QuarterlyReview[]>([]);
  const [competencies, setCompetencies] = useState<Competency[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchProgressData = useCallback(async () => {
    if (!traineeId) return;

    setLoading(true);
    setError(null);

    try {
      const [traineesData, reviewsData, competenciesData] = await Promise.all([
        fetchTrainees(),
        fetchReviews(),
        fetchCompetencies()
      ]);

      const currentTrainee = traineesData.find(t => t.id === traineeId);
      if (!currentTrainee) {
        throw new Error('Trainee not found');
      }

      const traineeReviews = reviewsData.filter(r => r.traineeId === traineeId);
      
      setTrainee(currentTrainee);
      setReviews(traineeReviews);
      setCompetencies(competenciesData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch progress data');
    } finally {
      setLoading(false);
    }
  }, [traineeId]);

  const refreshProgress = useCallback(() => fetchProgressData(), [fetchProgressData]);

  // Calculate competency progress
  const competencyProgress = useMemo((): CompetencyProgress[] => {
    if (!trainee || !competencies.length) return [];

    return competencies.map(competency => {
      const skillAssessment = trainee.skillsMatrix?.find(sa => sa.competencyId === competency.id);
      const currentLevel = skillAssessment?.currentLevel || CompetencyLevel.BEGINNER;
      const targetLevel = skillAssessment?.targetLevel || CompetencyLevel.PROFICIENT;
      const lastAssessed = skillAssessment?.assessmentDate ? new Date(skillAssessment.assessmentDate) : null;
      
      const progress = (currentLevel / targetLevel) * 100;

      return {
        competencyId: competency.id,
        name: competency.name,
        category: competency.category,
        currentLevel,
        targetLevel,
        progress: Math.min(progress, 100),
        lastAssessed: lastAssessed ? new Date(lastAssessed) : null
      };
    });
  }, [trainee, competencies]);

  // Calculate review progress
  const reviewProgress = useMemo((): ReviewProgress[] => {
    if (!reviews.length) return [];

    return reviews.map(review => {
      const dueDate = new Date(review.dueDate);
      const completedDate = review.completedDate ? new Date(review.completedDate) : null;
      const now = new Date();
      const overdue = !completedDate && now > dueDate;
      const daysOverdue = overdue ? Math.floor((now.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24)) : 0;

      return {
        reviewId: review.id,
        quarter: review.quarter,
        year: review.year,
        status: review.status,
        dueDate,
        completedDate,
        overdue,
        daysOverdue
      };
    });
  }, [reviews]);

  // Calculate overall progress metrics
  const progressMetrics = useMemo((): ProgressMetrics | null => {
    if (!trainee) return null;

    const competencyAvg = competencyProgress.length > 0 
      ? competencyProgress.reduce((sum, cp) => sum + cp.progress, 0) / competencyProgress.length 
      : 0;

    const completedReviews = reviewProgress.filter(rp => rp.status === ReviewStatus.COMPLETED).length;
    const totalReviews = reviewProgress.length;
    const reviewProgressPercent = totalReviews > 0 ? (completedReviews / totalReviews) * 100 : 0;

    const startDate = new Date(trainee.startDate);
    const now = new Date();
    const timeInProgram = Math.floor((now.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

    // Assume 24 months program duration
    const programDuration = 24 * 30; // 24 months in days
    const milestoneProgress = Math.min((timeInProgram / programDuration) * 100, 100);

    const overallProgress = (competencyAvg + reviewProgressPercent + milestoneProgress) / 3;

    const expectedCompletionDate = new Date(startDate);
    expectedCompletionDate.setMonth(expectedCompletionDate.getMonth() + 24);

    const isOnTrack = overallProgress >= (milestoneProgress * 0.8); // Should be at least 80% of expected progress
    
    let riskLevel: 'low' | 'medium' | 'high' = 'low';
    const overdueReviews = reviewProgress.filter(rp => rp.overdue).length;
    const competencyGaps = competencyProgress.filter(cp => cp.progress < 50).length;
    
    if (overdueReviews > 2 || competencyGaps > 3 || !isOnTrack) {
      riskLevel = 'high';
    } else if (overdueReviews > 0 || competencyGaps > 1) {
      riskLevel = 'medium';
    }

    return {
      overallProgress,
      competencyProgress: competencyAvg,
      reviewProgress: reviewProgressPercent,
      milestoneProgress,
      timeInProgram,
      expectedCompletionDate,
      isOnTrack,
      riskLevel
    };
  }, [trainee, competencyProgress, reviewProgress]);

  const calculateOverallProgress = useCallback((): number => {
    return progressMetrics?.overallProgress || 0;
  }, [progressMetrics]);

  const getCompetencyGaps = useCallback((): CompetencyProgress[] => {
    return competencyProgress.filter(cp => cp.progress < 70); // Less than 70% progress
  }, [competencyProgress]);

  const getOverdueReviews = useCallback((): ReviewProgress[] => {
    return reviewProgress.filter(rp => rp.overdue);
  }, [reviewProgress]);

  const predictCompletionDate = useCallback((): Date | null => {
    if (!trainee || !progressMetrics) return null;

    const currentProgress = progressMetrics.overallProgress;
    if (currentProgress === 0) return null;

    const startDate = new Date(trainee.startDate);
    const now = new Date();
    const timeElapsed = now.getTime() - startDate.getTime();
    const projectedTotalTime = (timeElapsed / currentProgress) * 100;
    
    const predictedCompletion = new Date(startDate.getTime() + projectedTotalTime);
    return predictedCompletion;
  }, [trainee, progressMetrics]);

  const assessRiskLevel = useCallback((): 'low' | 'medium' | 'high' => {
    return progressMetrics?.riskLevel || 'low';
  }, [progressMetrics]);

  // Auto-refresh effect
  useEffect(() => {
    if (autoRefresh && traineeId) {
      const interval = setInterval(fetchProgressData, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, traineeId, refreshInterval, fetchProgressData]);

  // Initial data fetch
  useEffect(() => {
    if (traineeId) {
      fetchProgressData();
    }
  }, [traineeId, fetchProgressData]);

  return {
    trainee,
    progressMetrics,
    competencyProgress,
    reviewProgress,
    loading,
    error,
    refreshProgress,
    calculateOverallProgress,
    getCompetencyGaps,
    getOverdueReviews,
    predictCompletionDate,
    assessRiskLevel
  };
};