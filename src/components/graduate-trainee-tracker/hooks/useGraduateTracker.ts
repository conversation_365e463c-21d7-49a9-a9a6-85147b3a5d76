import { useState, useEffect, useCallback, useReducer } from 'react';
import { 
  GraduateTraineeTrackerState, 
  FilterState, 
  PaginationState, 
  User, 
  Trainee, 
  TrainingProgram, 
  QuarterlyReview, 
  ApprovalWorkflow, 
  Notification, 
  Competency,
  TraineeStatus,
  DateRange
} from '../types';
import { 
  fetchTrainees, 
  fetchPrograms, 
  fetchReviews, 
  fetchWorkflows, 
  fetchNotifications, 
  fetchCompetencies 
} from '../mockApi';

// Action types for the reducer
type Action = 
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_TRAINEES'; payload: Trainee[] }
  | { type: 'SET_PROGRAMS'; payload: TrainingProgram[] }
  | { type: 'SET_REVIEWS'; payload: QuarterlyReview[] }
  | { type: 'SET_WORKFLOWS'; payload: ApprovalWorkflow[] }
  | { type: 'SET_NOTIFICATIONS'; payload: Notification[] }
  | { type: 'SET_COMPETENCIES'; payload: Competency[] }
  | { type: 'SET_CURRENT_USER'; payload: User | null }
  | { type: 'SET_SELECTED_TRAINEE'; payload: Trainee | null }
  | { type: 'SET_SELECTED_PROGRAM'; payload: TrainingProgram | null }
  | { type: 'SET_FILTERS'; payload: Partial<FilterState> }
  | { type: 'SET_PAGINATION'; payload: Partial<PaginationState> }
  | { type: 'RESET_FILTERS' }
  | { type: 'RESET_STATE' };

// Initial state
const initialState: GraduateTraineeTrackerState = {
  currentUser: null,
  trainees: [],
  programs: [],
  reviews: [],
  workflows: [],
  notifications: [],
  competencies: [],
  loading: false,
  error: null,
  selectedTrainee: null,
  selectedProgram: null,
  filters: {
    status: [],
    department: [],
    program: [],
    mentor: [],
    dateRange: {
      startDate: '',
      endDate: ''
    }
  },
  pagination: {
    page: 1,
    pageSize: 10,
    total: 0
  }
};

// Reducer function
function graduateTrackerReducer(state: GraduateTraineeTrackerState, action: Action): GraduateTraineeTrackerState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'SET_TRAINEES':
      return { ...state, trainees: action.payload };
    case 'SET_PROGRAMS':
      return { ...state, programs: action.payload };
    case 'SET_REVIEWS':
      return { ...state, reviews: action.payload };
    case 'SET_WORKFLOWS':
      return { ...state, workflows: action.payload };
    case 'SET_NOTIFICATIONS':
      return { ...state, notifications: action.payload };
    case 'SET_COMPETENCIES':
      return { ...state, competencies: action.payload };
    case 'SET_CURRENT_USER':
      return { ...state, currentUser: action.payload };
    case 'SET_SELECTED_TRAINEE':
      return { ...state, selectedTrainee: action.payload };
    case 'SET_SELECTED_PROGRAM':
      return { ...state, selectedProgram: action.payload };
    case 'SET_FILTERS':
      return { 
        ...state, 
        filters: { ...state.filters, ...action.payload },
        pagination: { ...state.pagination, page: 1 } // Reset to first page when filters change
      };
    case 'SET_PAGINATION':
      return { 
        ...state, 
        pagination: { ...state.pagination, ...action.payload }
      };
    case 'RESET_FILTERS':
      return {
        ...state,
        filters: initialState.filters,
        pagination: { ...state.pagination, page: 1 }
      };
    case 'RESET_STATE':
      return initialState;
    default:
      return state;
  }
}

export interface UseGraduateTrackerReturn {
  // State
  state: GraduateTraineeTrackerState;
  
  // Actions
  setCurrentUser: (user: User | null) => void;
  setSelectedTrainee: (trainee: Trainee | null) => void;
  setSelectedProgram: (program: TrainingProgram | null) => void;
  updateFilters: (filters: Partial<FilterState>) => void;
  updatePagination: (pagination: Partial<PaginationState>) => void;
  resetFilters: () => void;
  resetState: () => void;
  
  // Data loading
  loadAllData: () => Promise<void>;
  loadTrainees: () => Promise<void>;
  loadPrograms: () => Promise<void>;
  loadReviews: () => Promise<void>;
  loadWorkflows: () => Promise<void>;
  loadNotifications: () => Promise<void>;
  loadCompetencies: () => Promise<void>;
  
  // Computed values
  filteredTrainees: Trainee[];
  paginatedTrainees: Trainee[];
  totalPages: number;
  
  // Utility functions
  getTraineeById: (id: string) => Trainee | undefined;
  getProgramById: (id: string) => TrainingProgram | undefined;
  getReviewById: (id: string) => QuarterlyReview | undefined;
  getWorkflowById: (id: string) => ApprovalWorkflow | undefined;
  getNotificationById: (id: string) => Notification | undefined;
  getCompetencyById: (id: string) => Competency | undefined;
}

export const useGraduateTracker = (): UseGraduateTrackerReturn => {
  const [state, dispatch] = useReducer(graduateTrackerReducer, initialState);

  // Action creators
  const setCurrentUser = useCallback((user: User | null) => {
    dispatch({ type: 'SET_CURRENT_USER', payload: user });
  }, []);

  const setSelectedTrainee = useCallback((trainee: Trainee | null) => {
    dispatch({ type: 'SET_SELECTED_TRAINEE', payload: trainee });
  }, []);

  const setSelectedProgram = useCallback((program: TrainingProgram | null) => {
    dispatch({ type: 'SET_SELECTED_PROGRAM', payload: program });
  }, []);

  const updateFilters = useCallback((filters: Partial<FilterState>) => {
    dispatch({ type: 'SET_FILTERS', payload: filters });
  }, []);

  const updatePagination = useCallback((pagination: Partial<PaginationState>) => {
    dispatch({ type: 'SET_PAGINATION', payload: pagination });
  }, []);

  const resetFilters = useCallback(() => {
    dispatch({ type: 'RESET_FILTERS' });
  }, []);

  const resetState = useCallback(() => {
    dispatch({ type: 'RESET_STATE' });
  }, []);

  // Data loading functions
  const loadTrainees = useCallback(async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      const trainees = await fetchTrainees();
      dispatch({ type: 'SET_TRAINEES', payload: trainees });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to load trainees' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, []);

  const loadPrograms = useCallback(async () => {
    try {
      const programs = await fetchPrograms();
      dispatch({ type: 'SET_PROGRAMS', payload: programs });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to load programs' });
    }
  }, []);

  const loadReviews = useCallback(async () => {
    try {
      const reviews = await fetchReviews();
      dispatch({ type: 'SET_REVIEWS', payload: reviews });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to load reviews' });
    }
  }, []);

  const loadWorkflows = useCallback(async () => {
    try {
      const workflows = await fetchWorkflows();
      dispatch({ type: 'SET_WORKFLOWS', payload: workflows });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to load workflows' });
    }
  }, []);

  const loadNotifications = useCallback(async () => {
    try {
      const notifications = await fetchNotifications();
      dispatch({ type: 'SET_NOTIFICATIONS', payload: notifications });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to load notifications' });
    }
  }, []);

  const loadCompetencies = useCallback(async () => {
    try {
      const competencies = await fetchCompetencies();
      dispatch({ type: 'SET_COMPETENCIES', payload: competencies });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to load competencies' });
    }
  }, []);

  const loadAllData = useCallback(async () => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });
    
    try {
      await Promise.all([
        loadTrainees(),
        loadPrograms(),
        loadReviews(),
        loadWorkflows(),
        loadNotifications(),
        loadCompetencies()
      ]);
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to load data' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [loadTrainees, loadPrograms, loadReviews, loadWorkflows, loadNotifications, loadCompetencies]);

  // Computed values
  const filteredTrainees = state.trainees.filter(trainee => {
    const { status, department, program, mentor, dateRange } = state.filters;
    
    // Filter by status
    if (status.length > 0 && !status.includes(trainee.status)) {
      return false;
    }
    
    // Filter by department
    if (department.length > 0 && !department.includes(trainee.department)) {
      return false;
    }
    
    // Filter by program
    if (program.length > 0 && !program.includes(trainee.programId)) {
      return false;
    }
    
    // Filter by mentor
    if (mentor.length > 0 && !mentor.includes(trainee.mentorId)) {
      return false;
    }
    
    // Filter by date range
    if (dateRange.startDate && new Date(trainee.startDate) < new Date(dateRange.startDate)) {
      return false;
    }
    
    if (dateRange.endDate && new Date(trainee.endDate) > new Date(dateRange.endDate)) {
      return false;
    }
    
    return true;
  });

  const totalPages = Math.ceil(filteredTrainees.length / state.pagination.pageSize);
  
  const paginatedTrainees = filteredTrainees.slice(
    (state.pagination.page - 1) * state.pagination.pageSize,
    state.pagination.page * state.pagination.pageSize
  );

  // Update pagination total when filtered trainees change
  useEffect(() => {
    dispatch({ 
      type: 'SET_PAGINATION', 
      payload: { total: filteredTrainees.length } 
    });
  }, [filteredTrainees.length]);

  // Utility functions
  const getTraineeById = useCallback((id: string): Trainee | undefined => {
    return state.trainees.find(trainee => trainee.id === id);
  }, [state.trainees]);

  const getProgramById = useCallback((id: string): TrainingProgram | undefined => {
    return state.programs.find(program => program.id === id);
  }, [state.programs]);

  const getReviewById = useCallback((id: string): QuarterlyReview | undefined => {
    return state.reviews.find(review => review.id === id);
  }, [state.reviews]);

  const getWorkflowById = useCallback((id: string): ApprovalWorkflow | undefined => {
    return state.workflows.find(workflow => workflow.id === id);
  }, [state.workflows]);

  const getNotificationById = useCallback((id: string): Notification | undefined => {
    return state.notifications.find(notification => notification.id === id);
  }, [state.notifications]);

  const getCompetencyById = useCallback((id: string): Competency | undefined => {
    return state.competencies.find(competency => competency.id === id);
  }, [state.competencies]);

  return {
    state,
    setCurrentUser,
    setSelectedTrainee,
    setSelectedProgram,
    updateFilters,
    updatePagination,
    resetFilters,
    resetState,
    loadAllData,
    loadTrainees,
    loadPrograms,
    loadReviews,
    loadWorkflows,
    loadNotifications,
    loadCompetencies,
    filteredTrainees,
    paginatedTrainees,
    totalPages,
    getTraineeById,
    getProgramById,
    getReviewById,
    getWorkflowById,
    getNotificationById,
    getCompetencyById
  };
};

export default useGraduateTracker;