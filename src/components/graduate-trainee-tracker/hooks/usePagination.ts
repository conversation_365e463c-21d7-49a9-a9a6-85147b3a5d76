import { useState, useCallback, useMemo, useEffect } from 'react';
import { PaginationState } from '../types';

export interface UsePaginationReturn {
  // Pagination state
  pagination: PaginationState;
  
  // Pagination actions
  setPage: (page: number) => void;
  setPageSize: (pageSize: number) => void;
  setTotal: (total: number) => void;
  nextPage: () => void;
  previousPage: () => void;
  goToFirstPage: () => void;
  goToLastPage: () => void;
  updatePagination: (updates: Partial<PaginationState>) => void;
  resetPagination: () => void;
  
  // Computed values
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  startIndex: number;
  endIndex: number;
  currentPageItems: number;
  
  // Pagination utilities
  getPageNumbers: () => number[];
  getVisiblePageNumbers: (maxVisible?: number) => (number | 'ellipsis')[];
  getPaginationInfo: () => string;
  
  // Data slicing
  paginateData: <T>(data: T[]) => T[];
  
  // Page size options
  pageSizeOptions: number[];
}

const DEFAULT_PAGE_SIZE = 10;
const DEFAULT_PAGE_SIZE_OPTIONS = [5, 10, 20, 50, 100];

const initialPagination: PaginationState = {
  page: 1,
  pageSize: DEFAULT_PAGE_SIZE,
  total: 0
};

export interface UsePaginationOptions {
  initialPage?: number;
  initialPageSize?: number;
  pageSizeOptions?: number[];
  onPageChange?: (page: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
}

export const usePagination = (options: UsePaginationOptions = {}): UsePaginationReturn => {
  const {
    initialPage = 1,
    initialPageSize = DEFAULT_PAGE_SIZE,
    pageSizeOptions = DEFAULT_PAGE_SIZE_OPTIONS,
    onPageChange,
    onPageSizeChange
  } = options;

  const [pagination, setPagination] = useState<PaginationState>({
    page: initialPage,
    pageSize: initialPageSize,
    total: 0
  });

  // Pagination actions
  const setPage = useCallback((page: number) => {
    setPagination(prev => {
      const totalPages = Math.ceil(prev.total / prev.pageSize);
      const validPage = Math.max(1, Math.min(page, totalPages || 1));
      
      if (validPage !== prev.page) {
        onPageChange?.(validPage);
        return { ...prev, page: validPage };
      }
      
      return prev;
    });
  }, [onPageChange]);

  const setPageSize = useCallback((pageSize: number) => {
    setPagination(prev => {
      const newPageSize = Math.max(1, pageSize);
      const totalPages = Math.ceil(prev.total / newPageSize);
      const newPage = Math.min(prev.page, totalPages || 1);
      
      onPageSizeChange?.(newPageSize);
      
      return {
        ...prev,
        pageSize: newPageSize,
        page: newPage
      };
    });
  }, [onPageSizeChange]);

  const setTotal = useCallback((total: number) => {
    setPagination(prev => {
      const newTotal = Math.max(0, total);
      const totalPages = Math.ceil(newTotal / prev.pageSize);
      const validPage = totalPages > 0 ? Math.min(prev.page, totalPages) : 1;
      
      return {
        ...prev,
        total: newTotal,
        page: validPage
      };
    });
  }, []);

  const nextPage = useCallback(() => {
    setPage(pagination.page + 1);
  }, [pagination.page, setPage]);

  const previousPage = useCallback(() => {
    setPage(pagination.page - 1);
  }, [pagination.page, setPage]);

  const goToFirstPage = useCallback(() => {
    setPage(1);
  }, [setPage]);

  const goToLastPage = useCallback(() => {
    const totalPages = Math.ceil(pagination.total / pagination.pageSize);
    setPage(totalPages || 1);
  }, [pagination.total, pagination.pageSize, setPage]);

  const updatePagination = useCallback((updates: Partial<PaginationState>) => {
    setPagination(prev => ({ ...prev, ...updates }));
  }, []);

  const resetPagination = useCallback(() => {
    setPagination({
      page: initialPage,
      pageSize: initialPageSize,
      total: 0
    });
  }, [initialPage, initialPageSize]);

  // Computed values
  const totalPages = useMemo(() => {
    return Math.ceil(pagination.total / pagination.pageSize) || 1;
  }, [pagination.total, pagination.pageSize]);

  const hasNextPage = useMemo(() => {
    return pagination.page < totalPages;
  }, [pagination.page, totalPages]);

  const hasPreviousPage = useMemo(() => {
    return pagination.page > 1;
  }, [pagination.page]);

  const startIndex = useMemo(() => {
    return (pagination.page - 1) * pagination.pageSize;
  }, [pagination.page, pagination.pageSize]);

  const endIndex = useMemo(() => {
    return Math.min(startIndex + pagination.pageSize - 1, pagination.total - 1);
  }, [startIndex, pagination.pageSize, pagination.total]);

  const currentPageItems = useMemo(() => {
    return Math.min(pagination.pageSize, pagination.total - startIndex);
  }, [pagination.pageSize, pagination.total, startIndex]);

  // Pagination utilities
  const getPageNumbers = useCallback((): number[] => {
    return Array.from({ length: totalPages }, (_, i) => i + 1);
  }, [totalPages]);

  const getVisiblePageNumbers = useCallback((maxVisible: number = 7): (number | 'ellipsis')[] => {
    if (totalPages <= maxVisible) {
      return getPageNumbers();
    }

    const current = pagination.page;
    const pages: (number | 'ellipsis')[] = [];
    
    // Always show first page
    pages.push(1);
    
    // Calculate range around current page
    const rangeSize = Math.floor((maxVisible - 3) / 2); // -3 for first, last, and current
    let rangeStart = Math.max(2, current - rangeSize);
    let rangeEnd = Math.min(totalPages - 1, current + rangeSize);
    
    // Adjust range if it's too close to the edges
    if (rangeStart <= 3) {
      rangeStart = 2;
      rangeEnd = Math.min(totalPages - 1, maxVisible - 2);
    }
    
    if (rangeEnd >= totalPages - 2) {
      rangeEnd = totalPages - 1;
      rangeStart = Math.max(2, totalPages - maxVisible + 2);
    }
    
    // Add ellipsis before range if needed
    if (rangeStart > 2) {
      pages.push('ellipsis');
    }
    
    // Add range pages
    for (let i = rangeStart; i <= rangeEnd; i++) {
      pages.push(i);
    }
    
    // Add ellipsis after range if needed
    if (rangeEnd < totalPages - 1) {
      pages.push('ellipsis');
    }
    
    // Always show last page (if more than 1 page)
    if (totalPages > 1) {
      pages.push(totalPages);
    }
    
    return pages;
  }, [pagination.page, totalPages, getPageNumbers]);

  const getPaginationInfo = useCallback((): string => {
    if (pagination.total === 0) {
      return 'No items';
    }
    
    const start = startIndex + 1;
    const end = endIndex + 1;
    
    if (pagination.total === 1) {
      return '1 item';
    }
    
    return `${start}-${end} of ${pagination.total} items`;
  }, [pagination.total, startIndex, endIndex]);

  // Data slicing
  const paginateData = useCallback(<T>(data: T[]): T[] => {
    const start = startIndex;
    const end = start + pagination.pageSize;
    return data.slice(start, end);
  }, [startIndex, pagination.pageSize]);

  // Auto-adjust page when total changes
  useEffect(() => {
    if (pagination.total > 0) {
      const maxPage = Math.ceil(pagination.total / pagination.pageSize);
      if (pagination.page > maxPage) {
        setPage(maxPage);
      }
    }
  }, [pagination.total, pagination.pageSize, pagination.page, setPage]);

  return {
    pagination,
    setPage,
    setPageSize,
    setTotal,
    nextPage,
    previousPage,
    goToFirstPage,
    goToLastPage,
    updatePagination,
    resetPagination,
    totalPages,
    hasNextPage,
    hasPreviousPage,
    startIndex,
    endIndex,
    currentPageItems,
    getPageNumbers,
    getVisiblePageNumbers,
    getPaginationInfo,
    paginateData,
    pageSizeOptions
  };
};

export default usePagination;