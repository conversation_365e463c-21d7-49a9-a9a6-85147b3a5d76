import { useState, useEffect, useCallback } from 'react';
import { ApprovalWorkflow, ApprovalStatus, StageStatus, ApproverType } from '../types';
import { fetchWorkflows, submitApprovalDecision } from '../mockApi';

export interface WorkflowFilters {
  search?: string;
  status?: ApprovalStatus;
  programId?: string;
  traineeId?: string;
  approverId?: string;
  approverType?: ApproverType;
}

export interface UseWorkflowsReturn {
  workflows: ApprovalWorkflow[];
  filteredWorkflows: ApprovalWorkflow[];
  loading: boolean;
  error: string | null;
  fetchWorkflows: () => Promise<void>;
  submitDecision: (workflowId: string, stageId: string, decision: 'approve' | 'reject', comments?: string) => Promise<ApprovalWorkflow | null>;
  filters: WorkflowFilters;
  setFilters: (filters: WorkflowFilters) => void;
  searchWorkflows: (query: string) => void;
  filterByStatus: (status: ApprovalStatus | undefined) => void;
  filterByApprover: (approverId: string | undefined) => void;
  getWorkflowById: (id: string) => ApprovalWorkflow | undefined;
  getWorkflowsByTrainee: (traineeId: string) => ApprovalWorkflow[];
  getWorkflowsByApprover: (approverId: string) => ApprovalWorkflow[];
  getPendingWorkflows: () => ApprovalWorkflow[];
  getMyPendingApprovals: (userId: string) => ApprovalWorkflow[];
  getWorkflowStats: () => {
    total: number;
    pending: number;
    inProgress: number;
    approved: number;
    rejected: number;
  };
}

export const useWorkflows = (): UseWorkflowsReturn => {
  const [workflows, setWorkflows] = useState<ApprovalWorkflow[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<WorkflowFilters>({});

  // Fetch workflows from API
  const fetchWorkflowsData = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await fetchWorkflows();
      setWorkflows(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch workflows';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  // Submit approval decision
  const submitDecision = useCallback(async (
    workflowId: string, 
    stageId: string, 
    decision: 'approve' | 'reject', 
    comments?: string
  ): Promise<ApprovalWorkflow | null> => {
    setLoading(true);
    setError(null);
    try {
      const updatedWorkflow = await submitApprovalDecision(workflowId, stageId, decision, comments);
      setWorkflows(prev => prev.map(workflow => 
        workflow.id === workflowId ? updatedWorkflow : workflow
      ));
      return updatedWorkflow;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to submit decision';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Filter workflows based on current filters
  const filteredWorkflows = workflows.filter(workflow => {
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      const matchesSearch = 
        workflow.traineeId.toLowerCase().includes(searchLower) ||
        workflow.programId.toLowerCase().includes(searchLower) ||
        workflow.stages.some(stage => 
          stage.stageName.toLowerCase().includes(searchLower) ||
          stage.comments?.toLowerCase().includes(searchLower)
        );
      if (!matchesSearch) return false;
    }

    if (filters.status && workflow.status !== filters.status) {
      return false;
    }

    if (filters.programId && workflow.programId !== filters.programId) {
      return false;
    }

    if (filters.traineeId && workflow.traineeId !== filters.traineeId) {
      return false;
    }

    if (filters.approverId) {
      const hasApprover = workflow.stages.some(stage => stage.approverId === filters.approverId);
      if (!hasApprover) return false;
    }

    if (filters.approverType) {
      const hasApproverType = workflow.stages.some(stage => stage.approverType === filters.approverType);
      if (!hasApproverType) return false;
    }

    return true;
  });

  // Search workflows by query
  const searchWorkflows = useCallback((query: string) => {
    setFilters(prev => ({ ...prev, search: query }));
  }, []);

  // Filter by status
  const filterByStatus = useCallback((status: ApprovalStatus | undefined) => {
    setFilters(prev => ({ ...prev, status }));
  }, []);

  // Filter by approver
  const filterByApprover = useCallback((approverId: string | undefined) => {
    setFilters(prev => ({ ...prev, approverId }));
  }, []);

  // Get workflow by ID
  const getWorkflowById = useCallback((id: string) => {
    return workflows.find(workflow => workflow.id === id);
  }, [workflows]);

  // Get workflows by trainee
  const getWorkflowsByTrainee = useCallback((traineeId: string) => {
    return workflows.filter(workflow => workflow.traineeId === traineeId);
  }, [workflows]);

  // Get workflows by approver
  const getWorkflowsByApprover = useCallback((approverId: string) => {
    return workflows.filter(workflow => 
      workflow.stages.some(stage => stage.approverId === approverId)
    );
  }, [workflows]);

  // Get pending workflows
  const getPendingWorkflows = useCallback(() => {
    return workflows.filter(workflow => 
      workflow.status === ApprovalStatus.PENDING || 
      workflow.status === ApprovalStatus.IN_PROGRESS
    );
  }, [workflows]);

  // Get pending approvals for a specific user
  const getMyPendingApprovals = useCallback((userId: string) => {
    return workflows.filter(workflow => {
      // Find the current stage that needs approval from this user
      const currentStage = workflow.stages.find(stage => 
        stage.stageNumber === workflow.currentStage && 
        stage.approverId === userId &&
        stage.status === StageStatus.PENDING
      );
      return currentStage !== undefined;
    });
  }, [workflows]);

  // Get workflow statistics
  const getWorkflowStats = useCallback(() => {
    const stats = {
      total: workflows.length,
      pending: 0,
      inProgress: 0,
      approved: 0,
      rejected: 0
    };

    workflows.forEach(workflow => {
      switch (workflow.status) {
        case ApprovalStatus.PENDING:
          stats.pending++;
          break;
        case ApprovalStatus.IN_PROGRESS:
          stats.inProgress++;
          break;
        case ApprovalStatus.APPROVED:
          stats.approved++;
          break;
        case ApprovalStatus.REJECTED:
          stats.rejected++;
          break;
      }
    });

    return stats;
  }, [workflows]);

  // Load workflows on mount
  useEffect(() => {
    fetchWorkflowsData();
  }, [fetchWorkflowsData]);

  return {
    workflows,
    filteredWorkflows,
    loading,
    error,
    fetchWorkflows: fetchWorkflowsData,
    submitDecision,
    filters,
    setFilters,
    searchWorkflows,
    filterByStatus,
    filterByApprover,
    getWorkflowById,
    getWorkflowsByTrainee,
    getWorkflowsByApprover,
    getPendingWorkflows,
    getMyPendingApprovals,
    getWorkflowStats
  };
};

export default useWorkflows;