import { useState, useEffect, useCallback } from 'react';
import { TrainingProgram, ProgramStatus } from '../types';
import { fetchPrograms, createProgram, updateProgram } from '../mockApi';

export interface ProgramFilters {
  search?: string;
  status?: ProgramStatus;
  createdBy?: string;
}

export interface UseProgramsReturn {
  programs: TrainingProgram[];
  filteredPrograms: TrainingProgram[];
  loading: boolean;
  error: string | null;
  fetchPrograms: () => Promise<void>;
  createProgram: (program: Omit<TrainingProgram, 'id' | 'createdAt' | 'updatedAt'>) => Promise<TrainingProgram | null>;
  updateProgram: (id: string, updates: Partial<TrainingProgram>) => Promise<TrainingProgram | null>;
  filters: ProgramFilters;
  setFilters: (filters: ProgramFilters) => void;
  searchPrograms: (query: string) => void;
  filterByStatus: (status: ProgramStatus | undefined) => void;
  getProgramById: (id: string) => TrainingProgram | undefined;
  getActivePrograms: () => TrainingProgram[];
  getProgramStats: () => {
    total: number;
    active: number;
    draft: number;
    archived: number;
  };
}

export const usePrograms = (): UseProgramsReturn => {
  const [programs, setPrograms] = useState<TrainingProgram[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<ProgramFilters>({});

  // Fetch programs from API
  const fetchProgramsData = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await fetchPrograms();
      setPrograms(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch programs';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  // Create new program
  const createNewProgram = useCallback(async (programData: Omit<TrainingProgram, 'id' | 'createdAt' | 'updatedAt'>): Promise<TrainingProgram | null> => {
    setLoading(true);
    setError(null);
    try {
      const newProgram = await createProgram(programData);
      setPrograms(prev => [...prev, newProgram]);
      return newProgram;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create program';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Update existing program
  const updateExistingProgram = useCallback(async (id: string, updates: Partial<TrainingProgram>): Promise<TrainingProgram | null> => {
    setLoading(true);
    setError(null);
    try {
      const updatedProgram = await updateProgram(id, updates);
      setPrograms(prev => prev.map(program => 
        program.id === id ? updatedProgram : program
      ));
      return updatedProgram;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update program';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Filter programs based on current filters
  const filteredPrograms = programs.filter(program => {
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      const matchesSearch = 
        program.title.toLowerCase().includes(searchLower) ||
        program.description.toLowerCase().includes(searchLower) ||
        program.objectives.some(obj => obj.toLowerCase().includes(searchLower));
      if (!matchesSearch) return false;
    }

    if (filters.status && program.status !== filters.status) {
      return false;
    }

    if (filters.createdBy && program.createdBy !== filters.createdBy) {
      return false;
    }

    return true;
  });

  // Search programs by query
  const searchPrograms = useCallback((query: string) => {
    setFilters(prev => ({ ...prev, search: query }));
  }, []);

  // Filter by status
  const filterByStatus = useCallback((status: ProgramStatus | undefined) => {
    setFilters(prev => ({ ...prev, status }));
  }, []);

  // Get program by ID
  const getProgramById = useCallback((id: string) => {
    return programs.find(program => program.id === id);
  }, [programs]);

  // Get active programs
  const getActivePrograms = useCallback(() => {
    return programs.filter(program => program.status === ProgramStatus.ACTIVE);
  }, [programs]);

  // Get program statistics
  const getProgramStats = useCallback(() => {
    const stats = {
      total: programs.length,
      active: 0,
      draft: 0,
      archived: 0
    };

    programs.forEach(program => {
      switch (program.status) {
        case ProgramStatus.ACTIVE:
          stats.active++;
          break;
        case ProgramStatus.DRAFT:
          stats.draft++;
          break;
        case ProgramStatus.ARCHIVED:
          stats.archived++;
          break;
      }
    });

    return stats;
  }, [programs]);

  // Load programs on mount
  useEffect(() => {
    fetchProgramsData();
  }, [fetchProgramsData]);

  return {
    programs,
    filteredPrograms,
    loading,
    error,
    fetchPrograms: fetchProgramsData,
    createProgram: createNewProgram,
    updateProgram: updateExistingProgram,
    filters,
    setFilters,
    searchPrograms,
    filterByStatus,
    getProgramById,
    getActivePrograms,
    getProgramStats
  };
};

export default usePrograms;