import { useState, useCallback, useEffect, useRef } from 'react';

export interface Toast {
  id: string;
  title?: string;
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
  persistent?: boolean;
  action?: {
    label: string;
    onClick: () => void;
  };
  icon?: string;
  position?: ToastPosition;
  createdAt: number;
  dismissedAt?: number;
}

export type ToastPosition = 
  | 'top-left' 
  | 'top-center' 
  | 'top-right' 
  | 'bottom-left' 
  | 'bottom-center' 
  | 'bottom-right';

export interface ToastOptions {
  title?: string;
  type?: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
  persistent?: boolean;
  action?: {
    label: string;
    onClick: () => void;
  };
  icon?: string;
  position?: ToastPosition;
}

export interface UseToastOptions {
  defaultPosition?: ToastPosition;
  defaultDuration?: number;
  maxToasts?: number;
  pauseOnHover?: boolean;
  pauseOnFocusLoss?: boolean;
  newestOnTop?: boolean;
  preventDuplicates?: boolean;
  closeButton?: boolean;
  progressBar?: boolean;
}

const DEFAULT_OPTIONS: Required<UseToastOptions> = {
  defaultPosition: 'top-right',
  defaultDuration: 5000,
  maxToasts: 5,
  pauseOnHover: true,
  pauseOnFocusLoss: true,
  newestOnTop: true,
  preventDuplicates: true,
  closeButton: true,
  progressBar: true
};

const DEFAULT_DURATIONS = {
  success: 4000,
  info: 5000,
  warning: 6000,
  error: 0 // Persistent by default
};

export const useToast = (options: UseToastOptions = {}) => {
  const config = { ...DEFAULT_OPTIONS, ...options };
  const [toasts, setToasts] = useState<Toast[]>([]);
  const timeoutsRef = useRef<Map<string, NodeJS.Timeout>>(new Map());
  const pausedRef = useRef<Set<string>>(new Set());

  // Generate unique ID for toasts
  const generateId = useCallback(() => {
    return `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  // Clear timeout for a specific toast
  const clearToastTimeout = useCallback((id: string) => {
    const timeout = timeoutsRef.current.get(id);
    if (timeout) {
      clearTimeout(timeout);
      timeoutsRef.current.delete(id);
    }
  }, []);

  // Set timeout for auto-dismissal
  const setToastTimeout = useCallback((id: string, duration: number) => {
    if (duration <= 0) return; // Persistent toast

    const timeout = setTimeout(() => {
      if (!pausedRef.current.has(id)) {
        dismiss(id);
      }
    }, duration);

    timeoutsRef.current.set(id, timeout);
  }, []);

  // Add a new toast
  const show = useCallback((message: string, toastOptions: ToastOptions = {}) => {
    const id = generateId();
    const type = toastOptions.type || 'info';
    const duration = toastOptions.duration !== undefined 
      ? toastOptions.duration 
      : toastOptions.persistent 
        ? 0 
        : DEFAULT_DURATIONS[type] || config.defaultDuration;

    const newToast: Toast = {
      id,
      title: toastOptions.title,
      message,
      type,
      duration,
      persistent: toastOptions.persistent || duration === 0,
      action: toastOptions.action,
      icon: toastOptions.icon,
      position: toastOptions.position || config.defaultPosition,
      createdAt: Date.now()
    };

    // Check for duplicates
    if (config.preventDuplicates) {
      const isDuplicate = toasts.some(toast => 
        toast.message === message && 
        toast.type === type && 
        !toast.dismissedAt
      );
      if (isDuplicate) return id;
    }

    setToasts(prevToasts => {
      const updatedToasts = config.newestOnTop 
        ? [newToast, ...prevToasts] 
        : [...prevToasts, newToast];

      // Limit number of toasts
      if (updatedToasts.length > config.maxToasts) {
        const toastsToRemove = config.newestOnTop 
          ? updatedToasts.slice(config.maxToasts)
          : updatedToasts.slice(0, updatedToasts.length - config.maxToasts);
        
        toastsToRemove.forEach(toast => clearToastTimeout(toast.id));
        
        return config.newestOnTop 
          ? updatedToasts.slice(0, config.maxToasts)
          : updatedToasts.slice(-config.maxToasts);
      }

      return updatedToasts;
    });

    // Set auto-dismiss timeout
    if (!newToast.persistent) {
      setToastTimeout(id, duration);
    }

    return id;
  }, [toasts, config, generateId, clearToastTimeout, setToastTimeout]);

  // Dismiss a specific toast
  const dismiss = useCallback((id: string) => {
    clearToastTimeout(id);
    pausedRef.current.delete(id);
    
    setToasts(prevToasts => 
      prevToasts.map(toast => 
        toast.id === id 
          ? { ...toast, dismissedAt: Date.now() }
          : toast
      )
    );

    // Remove from DOM after animation
    setTimeout(() => {
      setToasts(prevToasts => prevToasts.filter(toast => toast.id !== id));
    }, 300); // Animation duration
  }, [clearToastTimeout]);

  // Dismiss all toasts
  const dismissAll = useCallback(() => {
    toasts.forEach(toast => {
      if (!toast.dismissedAt) {
        clearToastTimeout(toast.id);
        pausedRef.current.delete(toast.id);
      }
    });

    setToasts(prevToasts => 
      prevToasts.map(toast => 
        toast.dismissedAt 
          ? toast 
          : { ...toast, dismissedAt: Date.now() }
      )
    );

    // Clear all after animation
    setTimeout(() => {
      setToasts([]);
    }, 300);
  }, [toasts, clearToastTimeout]);

  // Pause auto-dismiss for a toast
  const pause = useCallback((id: string) => {
    pausedRef.current.add(id);
    clearToastTimeout(id);
  }, [clearToastTimeout]);

  // Resume auto-dismiss for a toast
  const resume = useCallback((id: string) => {
    pausedRef.current.delete(id);
    const toast = toasts.find(t => t.id === id);
    if (toast && !toast.persistent && !toast.dismissedAt) {
      const remainingTime = toast.duration! - (Date.now() - toast.createdAt);
      if (remainingTime > 0) {
        setToastTimeout(id, remainingTime);
      } else {
        dismiss(id);
      }
    }
  }, [toasts, setToastTimeout, dismiss]);

  // Convenience methods for different toast types
  const success = useCallback((message: string, options: Omit<ToastOptions, 'type'> = {}) => {
    return show(message, { ...options, type: 'success' });
  }, [show]);

  const error = useCallback((message: string, options: Omit<ToastOptions, 'type'> = {}) => {
    return show(message, { ...options, type: 'error' });
  }, [show]);

  const warning = useCallback((message: string, options: Omit<ToastOptions, 'type'> = {}) => {
    return show(message, { ...options, type: 'warning' });
  }, [show]);

  const info = useCallback((message: string, options: Omit<ToastOptions, 'type'> = {}) => {
    return show(message, { ...options, type: 'info' });
  }, [show]);

  // Handle focus events for pause on focus loss
  useEffect(() => {
    if (!config.pauseOnFocusLoss || typeof window === 'undefined') return;

    const handleFocus = () => {
      toasts.forEach(toast => {
        if (!toast.persistent && !toast.dismissedAt) {
          resume(toast.id);
        }
      });
    };

    const handleBlur = () => {
      toasts.forEach(toast => {
        if (!toast.persistent && !toast.dismissedAt) {
          pause(toast.id);
        }
      });
    };

    window.addEventListener('focus', handleFocus);
    window.addEventListener('blur', handleBlur);

    return () => {
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('blur', handleBlur);
    };
  }, [config.pauseOnFocusLoss, toasts, pause, resume]);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      timeoutsRef.current.forEach(timeout => clearTimeout(timeout));
      timeoutsRef.current.clear();
    };
  }, []);

  // Group toasts by position
  const toastsByPosition = toasts.reduce((acc, toast) => {
    const position = toast.position || config.defaultPosition;
    if (!acc[position]) {
      acc[position] = [];
    }
    acc[position].push(toast);
    return acc;
  }, {} as Record<ToastPosition, Toast[]>);

  // Toast event handlers
  const handleMouseEnter = useCallback((id: string) => {
    if (config.pauseOnHover) {
      pause(id);
    }
  }, [config.pauseOnHover, pause]);

  const handleMouseLeave = useCallback((id: string) => {
    if (config.pauseOnHover) {
      resume(id);
    }
  }, [config.pauseOnHover, resume]);

  // Get progress percentage for a toast
  const getProgress = useCallback((toast: Toast) => {
    if (toast.persistent || toast.dismissedAt || pausedRef.current.has(toast.id)) {
      return 100;
    }
    
    const elapsed = Date.now() - toast.createdAt;
    const progress = (elapsed / toast.duration!) * 100;
    return Math.min(100, Math.max(0, progress));
  }, []);

  return {
    // State
    toasts,
    toastsByPosition,
    
    // Actions
    show,
    dismiss,
    dismissAll,
    pause,
    resume,
    
    // Convenience methods
    success,
    error,
    warning,
    info,
    
    // Event handlers
    handleMouseEnter,
    handleMouseLeave,
    
    // Utilities
    getProgress,
    
    // Configuration
    config
  };
};

export default useToast;