// Data fetching hooks
export { useTrainees } from './useTrainees';
export { usePrograms } from './usePrograms';
export { useReviews } from './useReviews';
export { useWorkflows } from './useWorkflows';
export { useNotifications } from './useNotifications';
export { useCompetencies } from './useCompetencies';

// State management hooks
export { useGraduateTracker } from './useGraduateTracker';
export { useFilters } from './useFilters';
export { usePagination } from './usePagination';
export { useSorting } from './useSorting';

// Business logic hooks
export { useTraineeProgress } from './useTraineeProgress';
export { useProgramAnalytics } from './useProgramAnalytics';
export { useReviewScheduling } from './useReviewScheduling';
export { useApprovalProcess } from './useApprovalProcess';
export { useSkillsMatrix } from './useSkillsMatrix';

// UI/UX hooks
export { default as useLoading } from './useLoading';