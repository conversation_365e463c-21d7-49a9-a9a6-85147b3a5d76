import { useState, useEffect, useCallback } from 'react';
import { Notification, NotificationType, NotificationPriority } from '../types';
import { fetchNotifications, markNotificationAsRead, createNotification } from '../mockApi';

export interface NotificationFilters {
  search?: string;
  type?: NotificationType;
  priority?: NotificationPriority;
  isRead?: boolean;
  userId?: string;
}

export interface UseNotificationsReturn {
  notifications: Notification[];
  filteredNotifications: Notification[];
  unreadCount: number;
  loading: boolean;
  error: string | null;
  fetchNotifications: () => Promise<void>;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: (userId: string) => Promise<void>;
  createNotification: (notificationData: Omit<Notification, 'id' | 'createdAt' | 'isRead'>) => Promise<Notification | null>;
  filters: NotificationFilters;
  setFilters: (filters: NotificationFilters) => void;
  searchNotifications: (query: string) => void;
  filterByType: (type: NotificationType | undefined) => void;
  filterByPriority: (priority: NotificationPriority | undefined) => void;
  filterByReadStatus: (isRead: boolean | undefined) => void;
  getNotificationById: (id: string) => Notification | undefined;
  getNotificationsByUser: (userId: string) => Notification[];
  getUnreadNotifications: (userId: string) => Notification[];
  getNotificationsByType: (type: NotificationType) => Notification[];
  getNotificationStats: (userId?: string) => {
    total: number;
    unread: number;
    byType: Record<NotificationType, number>;
    byPriority: Record<NotificationPriority, number>;
  };
}

export const useNotifications = (userId?: string): UseNotificationsReturn => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<NotificationFilters>({ userId });

  // Fetch notifications from API
  const fetchNotificationsData = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await fetchNotifications();
      setNotifications(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch notifications';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  // Mark notification as read
  const markAsRead = useCallback(async (notificationId: string) => {
    setLoading(true);
    setError(null);
    try {
      const updatedNotification = await markNotificationAsRead(notificationId);
      setNotifications(prev => prev.map(notification => 
        notification.id === notificationId ? updatedNotification : notification
      ));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to mark notification as read';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  // Mark all notifications as read for a user
  const markAllAsRead = useCallback(async (targetUserId: string) => {
    setLoading(true);
    setError(null);
    try {
      const userNotifications = notifications.filter(n => n.userId === targetUserId && !n.isRead);
      const promises = userNotifications.map(n => markNotificationAsRead(n.id));
      const updatedNotifications = await Promise.all(promises);
      
      setNotifications(prev => prev.map(notification => {
        const updated = updatedNotifications.find(u => u.id === notification.id);
        return updated ? updated : notification;
      }));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to mark all notifications as read';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [notifications]);

  // Create new notification
  const createNotificationData = useCallback(async (
    notificationData: Omit<Notification, 'id' | 'createdAt' | 'isRead'>
  ): Promise<Notification | null> => {
    setLoading(true);
    setError(null);
    try {
      const newNotification = await createNotification(notificationData);
      setNotifications(prev => [newNotification, ...prev]);
      return newNotification;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create notification';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Filter notifications based on current filters
  const filteredNotifications = notifications.filter(notification => {
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      const matchesSearch = 
        notification.title.toLowerCase().includes(searchLower) ||
        notification.message.toLowerCase().includes(searchLower);
      if (!matchesSearch) return false;
    }

    if (filters.type && notification.type !== filters.type) {
      return false;
    }

    if (filters.priority && notification.priority !== filters.priority) {
      return false;
    }

    if (filters.isRead !== undefined && notification.isRead !== filters.isRead) {
      return false;
    }

    if (filters.userId && notification.userId !== filters.userId) {
      return false;
    }

    return true;
  });

  // Calculate unread count
  const unreadCount = filteredNotifications.filter(n => !n.isRead).length;

  // Search notifications by query
  const searchNotifications = useCallback((query: string) => {
    setFilters(prev => ({ ...prev, search: query }));
  }, []);

  // Filter by type
  const filterByType = useCallback((type: NotificationType | undefined) => {
    setFilters(prev => ({ ...prev, type }));
  }, []);

  // Filter by priority
  const filterByPriority = useCallback((priority: NotificationPriority | undefined) => {
    setFilters(prev => ({ ...prev, priority }));
  }, []);

  // Filter by read status
  const filterByReadStatus = useCallback((isRead: boolean | undefined) => {
    setFilters(prev => ({ ...prev, isRead }));
  }, []);

  // Get notification by ID
  const getNotificationById = useCallback((id: string) => {
    return notifications.find(notification => notification.id === id);
  }, [notifications]);

  // Get notifications by user
  const getNotificationsByUser = useCallback((targetUserId: string) => {
    return notifications.filter(notification => notification.userId === targetUserId);
  }, [notifications]);

  // Get unread notifications for a user
  const getUnreadNotifications = useCallback((targetUserId: string) => {
    return notifications.filter(notification => 
      notification.userId === targetUserId && !notification.isRead
    );
  }, [notifications]);

  // Get notifications by type
  const getNotificationsByType = useCallback((type: NotificationType) => {
    return notifications.filter(notification => notification.type === type);
  }, [notifications]);

  // Get notification statistics
  const getNotificationStats = useCallback((targetUserId?: string) => {
    const relevantNotifications = targetUserId 
      ? notifications.filter(n => n.userId === targetUserId)
      : notifications;

    const stats = {
      total: relevantNotifications.length,
      unread: relevantNotifications.filter(n => !n.isRead).length,
      byType: {
        [NotificationType.REVIEW_DUE]: 0,
        [NotificationType.REVIEW_REMINDER]: 0,
        [NotificationType.APPROVAL_REQUEST]: 0,
        [NotificationType.MILESTONE_REACHED]: 0,
        [NotificationType.MILESTONE_ACHIEVED]: 0,
        [NotificationType.DEADLINE_APPROACHING]: 0,
        [NotificationType.PROGRAM_UPDATE]: 0,
        [NotificationType.SYSTEM_ALERT]: 0
      } as Record<NotificationType, number>,
      byPriority: {
        [NotificationPriority.LOW]: 0,
        [NotificationPriority.MEDIUM]: 0,
        [NotificationPriority.HIGH]: 0,
        [NotificationPriority.URGENT]: 0
      } as Record<NotificationPriority, number>
    };

    relevantNotifications.forEach(notification => {
      stats.byType[notification.type]++;
      stats.byPriority[notification.priority]++;
    });

    return stats;
  }, [notifications]);

  // Update filters when userId prop changes
  useEffect(() => {
    setFilters(prev => ({ ...prev, userId }));
  }, [userId]);

  // Load notifications on mount
  useEffect(() => {
    fetchNotificationsData();
  }, [fetchNotificationsData]);

  return {
    notifications,
    filteredNotifications,
    unreadCount,
    loading,
    error,
    fetchNotifications: fetchNotificationsData,
    markAsRead,
    markAllAsRead,
    createNotification: createNotificationData,
    filters,
    setFilters,
    searchNotifications,
    filterByType,
    filterByPriority,
    filterByReadStatus,
    getNotificationById,
    getNotificationsByUser,
    getUnreadNotifications,
    getNotificationsByType,
    getNotificationStats
  };
};

export default useNotifications;