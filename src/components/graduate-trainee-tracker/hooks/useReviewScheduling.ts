import { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  QuarterlyReview, 
  Trainee, 
  User, 
  ReviewStatus, 
  ReviewType,
  AssessorType 
} from '../types';
import { 
  fetchReviews, 
  fetchTrainees, 
  fetchUsers,
  createReview,
  updateReview 
} from '../mockApi';

interface SchedulingConflict {
  reviewId: string;
  conflictType: 'time_overlap' | 'assessor_unavailable' | 'trainee_unavailable';
  conflictingReviewId?: string;
  message: string;
}

interface ReviewSlot {
  date: string;
  time: string;
  available: boolean;
  conflictReason?: string;
}

interface SchedulingOptions {
  preferredDays: string[]; // ['monday', 'tuesday', etc.]
  preferredTimes: string[]; // ['09:00', '14:00', etc.]
  duration: number; // in minutes
  bufferTime: number; // in minutes
  maxReviewsPerDay: number;
}

interface BulkSchedulingRequest {
  traineeIds: string[];
  reviewType: ReviewType;
  quarter: number;
  year: number;
  startDate: string;
  endDate: string;
  schedulingOptions: SchedulingOptions;
}

interface UseReviewSchedulingOptions {
  autoLoadUpcoming?: boolean;
  lookAheadDays?: number;
  defaultDuration?: number;
}

interface UseReviewSchedulingReturn {
  reviews: QuarterlyReview[];
  upcomingReviews: QuarterlyReview[];
  overdueReviews: QuarterlyReview[];
  conflicts: SchedulingConflict[];
  loading: boolean;
  error: string | null;
  scheduleReview: (reviewData: Omit<QuarterlyReview, 'id' | 'createdAt' | 'updatedAt'>) => Promise<QuarterlyReview | null>;
  rescheduleReview: (reviewId: string, newDate: string) => Promise<boolean>;
  cancelReview: (reviewId: string, reason: string) => Promise<boolean>;
  findAvailableSlots: (traineeId: string, assessorId: string, startDate: string, endDate: string) => Promise<ReviewSlot[]>;
  checkSchedulingConflicts: (reviewData: Partial<QuarterlyReview>) => SchedulingConflict[];
  bulkScheduleReviews: (request: BulkSchedulingRequest) => Promise<{ scheduled: QuarterlyReview[]; conflicts: SchedulingConflict[] }>;
  getReviewCalendar: (month: number, year: number) => Promise<{ date: string; reviews: QuarterlyReview[] }[]>;
  suggestOptimalSchedule: (traineeIds: string[], constraints: SchedulingOptions) => Promise<{ traineeId: string; suggestedDate: string; suggestedTime: string }[]>;
  refreshSchedule: () => Promise<void>;
}

export const useReviewScheduling = (options: UseReviewSchedulingOptions = {}): UseReviewSchedulingReturn => {
  const { autoLoadUpcoming = true, lookAheadDays = 30, defaultDuration = 60 } = options;

  const [reviews, setReviews] = useState<QuarterlyReview[]>([]);
  const [trainees, setTrainees] = useState<Trainee[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchSchedulingData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const [reviewsData, traineesData, usersData] = await Promise.all([
        fetchReviews(),
        fetchTrainees(),
        fetchUsers()
      ]);

      setReviews(reviewsData);
      setTrainees(traineesData);
      setUsers(usersData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch scheduling data');
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshSchedule = useCallback(() => fetchSchedulingData(), [fetchSchedulingData]);

  // Calculate upcoming reviews
  const upcomingReviews = useMemo(() => {
    const now = new Date();
    const lookAheadDate = new Date();
    lookAheadDate.setDate(now.getDate() + lookAheadDays);

    return reviews.filter(review => {
      const scheduledDate = new Date(review.scheduledDate);
      return scheduledDate >= now && 
             scheduledDate <= lookAheadDate && 
             review.status !== ReviewStatus.COMPLETED && 
             review.status !== ReviewStatus.CANCELLED;
    }).sort((a, b) => new Date(a.scheduledDate).getTime() - new Date(b.scheduledDate).getTime());
  }, [reviews, lookAheadDays]);

  // Calculate overdue reviews
  const overdueReviews = useMemo(() => {
    const now = new Date();
    return reviews.filter(review => {
      const dueDate = new Date(review.dueDate);
      return dueDate < now && 
             review.status !== ReviewStatus.COMPLETED && 
             review.status !== ReviewStatus.CANCELLED;
    }).sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime());
  }, [reviews]);

  // Detect scheduling conflicts
  const conflicts = useMemo((): SchedulingConflict[] => {
    const detectedConflicts: SchedulingConflict[] = [];
    
    reviews.forEach(review => {
      if (review.status === ReviewStatus.CANCELLED || review.status === ReviewStatus.COMPLETED) {
        return;
      }

      const reviewDate = new Date(review.scheduledDate);
      const reviewEndTime = new Date(reviewDate.getTime() + defaultDuration * 60000);

      // Check for time overlaps with other reviews
      const overlappingReviews = reviews.filter(otherReview => {
        if (otherReview.id === review.id || 
            otherReview.status === ReviewStatus.CANCELLED || 
            otherReview.status === ReviewStatus.COMPLETED) {
          return false;
        }

        const otherDate = new Date(otherReview.scheduledDate);
        const otherEndTime = new Date(otherDate.getTime() + defaultDuration * 60000);

        // Check if same assessor or trainee has overlapping reviews
        const sameAssessor = otherReview.reviewerId === review.reviewerId;
        const sameTrainee = otherReview.traineeId === review.traineeId;
        
        if (sameAssessor || sameTrainee) {
          return (reviewDate < otherEndTime && reviewEndTime > otherDate);
        }

        return false;
      });

      overlappingReviews.forEach(conflictingReview => {
        const conflictType = conflictingReview.reviewerId === review.reviewerId 
          ? 'assessor_unavailable' 
          : 'trainee_unavailable';
        
        detectedConflicts.push({
          reviewId: review.id,
          conflictType,
          conflictingReviewId: conflictingReview.id,
          message: `${conflictType === 'assessor_unavailable' ? 'Assessor' : 'Trainee'} has another review scheduled at the same time`
        });
      });
    });

    return detectedConflicts;
  }, [reviews, defaultDuration]);

  const scheduleReview = useCallback(async (reviewData: Omit<QuarterlyReview, 'id' | 'createdAt' | 'updatedAt'>): Promise<QuarterlyReview | null> => {
    try {
      // Check for conflicts before scheduling
      const reviewConflicts = checkSchedulingConflicts(reviewData);
      if (reviewConflicts.length > 0) {
        setError(`Cannot schedule review due to conflicts: ${reviewConflicts.map(c => c.message).join(', ')}`);
        return null;
      }

      const newReview = await createReview(reviewData);
      if (newReview) {
        setReviews(prev => [...prev, newReview]);
      }
      return newReview;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to schedule review');
      return null;
    }
  }, []);

  const rescheduleReview = useCallback(async (reviewId: string, newDate: string): Promise<boolean> => {
    try {
      const review = reviews.find(r => r.id === reviewId);
      if (!review) {
        setError('Review not found');
        return false;
      }

      // Check conflicts for new date
      const updatedReview = { ...review, scheduledDate: newDate };
      const reviewConflicts = checkSchedulingConflicts(updatedReview);
      if (reviewConflicts.length > 0) {
        setError(`Cannot reschedule due to conflicts: ${reviewConflicts.map(c => c.message).join(', ')}`);
        return false;
      }

      const updated = await updateReview(reviewId, { scheduledDate: newDate });
      if (updated) {
        setReviews(prev => prev.map(r => r.id === reviewId ? updated : r));
        return true;
      }
      return false;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to reschedule review');
      return false;
    }
  }, [reviews]);

  const cancelReview = useCallback(async (reviewId: string, reason: string): Promise<boolean> => {
    try {
      const updated = await updateReview(reviewId, { 
        status: ReviewStatus.CANCELLED,
        // Note: In a real implementation, you'd want to add a cancellation reason field
      });
      if (updated) {
        setReviews(prev => prev.map(r => r.id === reviewId ? updated : r));
        return true;
      }
      return false;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to cancel review');
      return false;
    }
  }, []);

  const findAvailableSlots = useCallback(async (
    traineeId: string, 
    assessorId: string, 
    startDate: string, 
    endDate: string
  ): Promise<ReviewSlot[]> => {
    const slots: ReviewSlot[] = [];
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    // Generate time slots for each day
    const workingHours = ['09:00', '10:00', '11:00', '14:00', '15:00', '16:00'];
    
    for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
      // Skip weekends
      if (date.getDay() === 0 || date.getDay() === 6) continue;
      
      const dateStr = date.toISOString().split('T')[0];
      
      workingHours.forEach(time => {
        const slotDateTime = `${dateStr}T${time}:00`;
        const slotDate = new Date(slotDateTime);
        const slotEndTime = new Date(slotDate.getTime() + defaultDuration * 60000);
        
        // Check for conflicts
        const hasConflict = reviews.some(review => {
          if (review.status === ReviewStatus.CANCELLED || review.status === ReviewStatus.COMPLETED) {
            return false;
          }
          
          const reviewDate = new Date(review.scheduledDate);
          const reviewEndTime = new Date(reviewDate.getTime() + defaultDuration * 60000);
          
          const sameAssessor = review.reviewerId === assessorId;
          const sameTrainee = review.traineeId === traineeId;
          
          if (sameAssessor || sameTrainee) {
            return (slotDate < reviewEndTime && slotEndTime > reviewDate);
          }
          
          return false;
        });
        
        slots.push({
          date: dateStr,
          time,
          available: !hasConflict,
          conflictReason: hasConflict ? 'Time slot already booked' : undefined
        });
      });
    }
    
    return slots;
  }, [reviews, defaultDuration]);

  const checkSchedulingConflicts = useCallback((reviewData: Partial<QuarterlyReview>): SchedulingConflict[] => {
    if (!reviewData.scheduledDate || !reviewData.reviewerId || !reviewData.traineeId) {
      return [];
    }

    const detectedConflicts: SchedulingConflict[] = [];
    const reviewDate = new Date(reviewData.scheduledDate);
    const reviewEndTime = new Date(reviewDate.getTime() + defaultDuration * 60000);

    reviews.forEach(existingReview => {
      if (existingReview.id === reviewData.id || 
          existingReview.status === ReviewStatus.CANCELLED || 
          existingReview.status === ReviewStatus.COMPLETED) {
        return;
      }

      const existingDate = new Date(existingReview.scheduledDate);
      const existingEndTime = new Date(existingDate.getTime() + defaultDuration * 60000);

      const sameAssessor = existingReview.reviewerId === reviewData.reviewerId;
      const sameTrainee = existingReview.traineeId === reviewData.traineeId;
      
      if ((sameAssessor || sameTrainee) && 
          (reviewDate < existingEndTime && reviewEndTime > existingDate)) {
        
        detectedConflicts.push({
          reviewId: reviewData.id || 'new',
          conflictType: sameAssessor ? 'assessor_unavailable' : 'trainee_unavailable',
          conflictingReviewId: existingReview.id,
          message: `${sameAssessor ? 'Assessor' : 'Trainee'} has another review scheduled at ${existingDate.toLocaleString()}`
        });
      }
    });

    return detectedConflicts;
  }, [reviews, defaultDuration]);

  const bulkScheduleReviews = useCallback(async (request: BulkSchedulingRequest): Promise<{ scheduled: QuarterlyReview[]; conflicts: SchedulingConflict[] }> => {
    const scheduled: QuarterlyReview[] = [];
    const allConflicts: SchedulingConflict[] = [];
    
    for (const traineeId of request.traineeIds) {
      const trainee = trainees.find(t => t.id === traineeId);
      if (!trainee) continue;
      
      // Find available mentor/assessor
      const mentor = users.find(u => u.id === trainee.mentorId);
      if (!mentor) continue;
      
      // Find available slot
      const availableSlots = await findAvailableSlots(
        traineeId, 
        mentor.id, 
        request.startDate, 
        request.endDate
      );
      
      const availableSlot = availableSlots.find(slot => slot.available);
      if (!availableSlot) {
        allConflicts.push({
          reviewId: 'bulk-' + traineeId,
          conflictType: 'time_overlap',
          message: `No available slots found for trainee ${trainee.firstName} ${trainee.lastName}`
        });
        continue;
      }
      
      // Schedule the review
      const reviewData: Omit<QuarterlyReview, 'id' | 'createdAt' | 'updatedAt'> = {
        traineeId,
        reviewerId: mentor.id,
        type: request.reviewType,
        quarter: request.quarter,
        year: request.year,
        reviewPeriod: {
          quarter: request.quarter,
          year: request.year,
          startDate: request.startDate,
          endDate: request.endDate
        },
        scheduledDate: `${availableSlot.date}T${availableSlot.time}:00`,
        dueDate: request.endDate,
        status: ReviewStatus.SCHEDULED,
        overallRating: 0,
        feedback: [],
        actionItems: [],
        nextReviewDate: ''
      };
      
      try {
        const newReview = await createReview(reviewData);
        if (newReview) {
          scheduled.push(newReview);
        }
      } catch (err) {
        allConflicts.push({
          reviewId: 'bulk-' + traineeId,
          conflictType: 'time_overlap',
          message: `Failed to schedule review for ${trainee.firstName} ${trainee.lastName}: ${err instanceof Error ? err.message : 'Unknown error'}`
        });
      }
    }
    
    // Update local state
    setReviews(prev => [...prev, ...scheduled]);
    
    return { scheduled, conflicts: allConflicts };
  }, [trainees, users, findAvailableSlots]);

  const getReviewCalendar = useCallback(async (month: number, year: number): Promise<{ date: string; reviews: QuarterlyReview[] }[]> => {
    const calendar: { date: string; reviews: QuarterlyReview[] }[] = [];
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0);
    
    for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
      const dateStr = date.toISOString().split('T')[0];
      const dayReviews = reviews.filter(review => {
        const reviewDate = new Date(review.scheduledDate);
        return reviewDate.toISOString().split('T')[0] === dateStr;
      });
      
      calendar.push({
        date: dateStr,
        reviews: dayReviews
      });
    }
    
    return calendar;
  }, [reviews]);

  const suggestOptimalSchedule = useCallback(async (
    traineeIds: string[], 
    constraints: SchedulingOptions
  ): Promise<{ traineeId: string; suggestedDate: string; suggestedTime: string }[]> => {
    const suggestions: { traineeId: string; suggestedDate: string; suggestedTime: string }[] = [];
    
    for (const traineeId of traineeIds) {
      const trainee = trainees.find(t => t.id === traineeId);
      if (!trainee) continue;
      
      const mentor = users.find(u => u.id === trainee.mentorId);
      if (!mentor) continue;
      
      // Find optimal slot based on constraints
      const today = new Date();
      const searchEndDate = new Date();
      searchEndDate.setDate(today.getDate() + 30); // Search within 30 days
      
      const availableSlots = await findAvailableSlots(
        traineeId,
        mentor.id,
        today.toISOString().split('T')[0],
        searchEndDate.toISOString().split('T')[0]
      );
      
      // Filter by preferred days and times
      const preferredSlots = availableSlots.filter(slot => {
        const slotDate = new Date(slot.date);
        const dayName = slotDate.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
        
        const matchesDay = constraints.preferredDays.length === 0 || 
                          constraints.preferredDays.includes(dayName);
        const matchesTime = constraints.preferredTimes.length === 0 || 
                           constraints.preferredTimes.includes(slot.time);
        
        return slot.available && matchesDay && matchesTime;
      });
      
      const optimalSlot = preferredSlots[0]; // Take the first available preferred slot
      if (optimalSlot) {
        suggestions.push({
          traineeId,
          suggestedDate: optimalSlot.date,
          suggestedTime: optimalSlot.time
        });
      }
    }
    
    return suggestions;
  }, [trainees, users, findAvailableSlots]);

  // Initial data fetch
  useEffect(() => {
    fetchSchedulingData();
  }, [fetchSchedulingData]);

  return {
    reviews,
    upcomingReviews,
    overdueReviews,
    conflicts,
    loading,
    error,
    scheduleReview,
    rescheduleReview,
    cancelReview,
    findAvailableSlots,
    checkSchedulingConflicts,
    bulkScheduleReviews,
    getReviewCalendar,
    suggestOptimalSchedule,
    refreshSchedule
  };
};