import { useState, useCallback, useMemo } from 'react';
import { 
  FilterState, 
  TraineeStatus, 
  DateR<PERSON><PERSON>,
  Trainee,
  TrainingProgram,
  User
} from '../types';

export interface UseFiltersReturn {
  // Filter state
  filters: FilterState;
  
  // Filter actions
  setStatusFilter: (status: TraineeStatus[]) => void;
  setDepartmentFilter: (departments: string[]) => void;
  setProgramFilter: (programs: string[]) => void;
  setMentorFilter: (mentors: string[]) => void;
  setDateRangeFilter: (dateRange: DateRange) => void;
  updateFilters: (updates: Partial<FilterState>) => void;
  resetFilters: () => void;
  clearFilter: (filterType: keyof FilterState) => void;
  
  // Filter utilities
  hasActiveFilters: boolean;
  activeFilterCount: number;
  getFilterSummary: () => string[];
  
  // Filter application
  applyFilters: <T extends Trainee>(items: T[]) => T[];
  
  // Quick filters
  setQuickFilter: (type: 'active' | 'pending' | 'completed' | 'all') => void;
  
  // Advanced filters
  setAdvancedFilters: (filters: {
    minRating?: number;
    maxRating?: number;
    hasReviews?: boolean;
    overdue?: boolean;
    recentlyUpdated?: boolean;
  }) => void;
}

const initialFilters: FilterState = {
  status: [],
  department: [],
  program: [],
  mentor: [],
  dateRange: {
    startDate: '',
    endDate: ''
  }
};

export const useFilters = (): UseFiltersReturn => {
  const [filters, setFilters] = useState<FilterState>(initialFilters);
  const [advancedFilters, setAdvancedFiltersState] = useState({
    minRating: undefined as number | undefined,
    maxRating: undefined as number | undefined,
    hasReviews: undefined as boolean | undefined,
    overdue: undefined as boolean | undefined,
    recentlyUpdated: undefined as boolean | undefined
  });

  // Filter actions
  const setStatusFilter = useCallback((status: TraineeStatus[]) => {
    setFilters(prev => ({ ...prev, status }));
  }, []);

  const setDepartmentFilter = useCallback((departments: string[]) => {
    setFilters(prev => ({ ...prev, department: departments }));
  }, []);

  const setProgramFilter = useCallback((programs: string[]) => {
    setFilters(prev => ({ ...prev, program: programs }));
  }, []);

  const setMentorFilter = useCallback((mentors: string[]) => {
    setFilters(prev => ({ ...prev, mentor: mentors }));
  }, []);

  const setDateRangeFilter = useCallback((dateRange: DateRange) => {
    setFilters(prev => ({ ...prev, dateRange }));
  }, []);

  const updateFilters = useCallback((updates: Partial<FilterState>) => {
    setFilters(prev => ({ ...prev, ...updates }));
  }, []);

  const resetFilters = useCallback(() => {
    setFilters(initialFilters);
    setAdvancedFiltersState({
      minRating: undefined,
      maxRating: undefined,
      hasReviews: undefined,
      overdue: undefined,
      recentlyUpdated: undefined
    });
  }, []);

  const clearFilter = useCallback((filterType: keyof FilterState) => {
    setFilters(prev => {
      const newFilters = { ...prev };
      if (filterType === 'dateRange') {
        newFilters.dateRange = { startDate: '', endDate: '' };
      } else {
        (newFilters[filterType] as string[]) = [];
      }
      return newFilters;
    });
  }, []);

  // Quick filters
  const setQuickFilter = useCallback((type: 'active' | 'pending' | 'completed' | 'all') => {
    switch (type) {
      case 'active':
        setStatusFilter([TraineeStatus.ACTIVE]);
        break;
      case 'pending':
        setStatusFilter([TraineeStatus.PENDING]);
        break;
      case 'completed':
        setStatusFilter([TraineeStatus.COMPLETED]);
        break;
      case 'all':
        setStatusFilter([]);
        break;
    }
  }, [setStatusFilter]);

  // Advanced filters
  const setAdvancedFilters = useCallback((newAdvancedFilters: {
    minRating?: number;
    maxRating?: number;
    hasReviews?: boolean;
    overdue?: boolean;
    recentlyUpdated?: boolean;
  }) => {
    setAdvancedFiltersState(prev => ({ ...prev, ...newAdvancedFilters }));
  }, []);

  // Computed values
  const hasActiveFilters = useMemo(() => {
    return (
      filters.status.length > 0 ||
      filters.department.length > 0 ||
      filters.program.length > 0 ||
      filters.mentor.length > 0 ||
      filters.dateRange.startDate !== '' ||
      filters.dateRange.endDate !== '' ||
      advancedFilters.minRating !== undefined ||
      advancedFilters.maxRating !== undefined ||
      advancedFilters.hasReviews !== undefined ||
      advancedFilters.overdue !== undefined ||
      advancedFilters.recentlyUpdated !== undefined
    );
  }, [filters, advancedFilters]);

  const activeFilterCount = useMemo(() => {
    let count = 0;
    if (filters.status.length > 0) count++;
    if (filters.department.length > 0) count++;
    if (filters.program.length > 0) count++;
    if (filters.mentor.length > 0) count++;
    if (filters.dateRange.startDate !== '' || filters.dateRange.endDate !== '') count++;
    if (advancedFilters.minRating !== undefined) count++;
    if (advancedFilters.maxRating !== undefined) count++;
    if (advancedFilters.hasReviews !== undefined) count++;
    if (advancedFilters.overdue !== undefined) count++;
    if (advancedFilters.recentlyUpdated !== undefined) count++;
    return count;
  }, [filters, advancedFilters]);

  const getFilterSummary = useCallback((): string[] => {
    const summary: string[] = [];
    
    if (filters.status.length > 0) {
      summary.push(`Status: ${filters.status.join(', ')}`);
    }
    
    if (filters.department.length > 0) {
      summary.push(`Department: ${filters.department.join(', ')}`);
    }
    
    if (filters.program.length > 0) {
      summary.push(`Program: ${filters.program.length} selected`);
    }
    
    if (filters.mentor.length > 0) {
      summary.push(`Mentor: ${filters.mentor.length} selected`);
    }
    
    if (filters.dateRange.startDate || filters.dateRange.endDate) {
      const start = filters.dateRange.startDate || 'Any';
      const end = filters.dateRange.endDate || 'Any';
      summary.push(`Date Range: ${start} - ${end}`);
    }
    
    if (advancedFilters.minRating !== undefined || advancedFilters.maxRating !== undefined) {
      const min = advancedFilters.minRating ?? 'Any';
      const max = advancedFilters.maxRating ?? 'Any';
      summary.push(`Rating: ${min} - ${max}`);
    }
    
    if (advancedFilters.hasReviews !== undefined) {
      summary.push(`Has Reviews: ${advancedFilters.hasReviews ? 'Yes' : 'No'}`);
    }
    
    if (advancedFilters.overdue !== undefined) {
      summary.push(`Overdue: ${advancedFilters.overdue ? 'Yes' : 'No'}`);
    }
    
    if (advancedFilters.recentlyUpdated !== undefined) {
      summary.push(`Recently Updated: ${advancedFilters.recentlyUpdated ? 'Yes' : 'No'}`);
    }
    
    return summary;
  }, [filters, advancedFilters]);

  // Filter application
  const applyFilters = useCallback(<T extends Trainee>(items: T[]): T[] => {
    return items.filter(item => {
      // Status filter
      if (filters.status.length > 0 && !filters.status.includes(item.status)) {
        return false;
      }
      
      // Department filter
      if (filters.department.length > 0 && !filters.department.includes(item.department)) {
        return false;
      }
      
      // Program filter
      if (filters.program.length > 0 && !filters.program.includes(item.programId)) {
        return false;
      }
      
      // Mentor filter
      if (filters.mentor.length > 0 && !filters.mentor.includes(item.mentorId)) {
        return false;
      }
      
      // Date range filter
      if (filters.dateRange.startDate && new Date(item.startDate) < new Date(filters.dateRange.startDate)) {
        return false;
      }
      
      if (filters.dateRange.endDate && new Date(item.endDate) > new Date(filters.dateRange.endDate)) {
        return false;
      }
      
      // Advanced filters
      if (advancedFilters.hasReviews !== undefined) {
        const hasReviews = item.reviews && item.reviews.length > 0;
        if (advancedFilters.hasReviews !== hasReviews) {
          return false;
        }
      }
      
      if (advancedFilters.overdue !== undefined) {
        const now = new Date();
        const isOverdue = item.reviews?.some(review => 
          review.dueDate && new Date(review.dueDate) < now && review.status !== 'completed'
        ) || false;
        if (advancedFilters.overdue !== isOverdue) {
          return false;
        }
      }
      
      if (advancedFilters.recentlyUpdated !== undefined) {
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        const isRecentlyUpdated = new Date(item.updatedAt) > weekAgo;
        if (advancedFilters.recentlyUpdated !== isRecentlyUpdated) {
          return false;
        }
      }
      
      // Rating filters (if reviews exist)
      if (item.reviews && item.reviews.length > 0) {
        const avgRating = item.reviews.reduce((sum, review) => sum + review.overallRating, 0) / item.reviews.length;
        
        if (advancedFilters.minRating !== undefined && avgRating < advancedFilters.minRating) {
          return false;
        }
        
        if (advancedFilters.maxRating !== undefined && avgRating > advancedFilters.maxRating) {
          return false;
        }
      }
      
      return true;
    });
  }, [filters, advancedFilters]);

  return {
    filters,
    setStatusFilter,
    setDepartmentFilter,
    setProgramFilter,
    setMentorFilter,
    setDateRangeFilter,
    updateFilters,
    resetFilters,
    clearFilter,
    hasActiveFilters,
    activeFilterCount,
    getFilterSummary,
    applyFilters,
    setQuickFilter,
    setAdvancedFilters
  };
};

export default useFilters;