import { useState, useEffect, useCallback } from 'react';
import { Competency, CompetencyLevel } from '../types';
import { fetchCompetencies } from '../mockApi';

export interface UseCompetenciesReturn {
  competencies: Competency[];
  loading: boolean;
  error: string | null;
  searchTerm: string;
  levelFilter: CompetencyLevel | 'all';
  categoryFilter: string;
  
  // Actions
  setSearchTerm: (term: string) => void;
  setLevelFilter: (level: CompetencyLevel | 'all') => void;
  setCategoryFilter: (category: string) => void;
  refreshCompetencies: () => Promise<void>;
  
  // Computed values
  filteredCompetencies: Competency[];
  competencyCategories: string[];
  competencyStats: {
    total: number;
    byLevel: Record<CompetencyLevel, number>;
    byCategory: Record<string, number>;
  };
  
  // Utility functions
  getCompetencyById: (id: string) => Competency | undefined;
  getCompetenciesByCategory: (category: string) => Competency[];
  getCompetenciesByLevel: (level: CompetencyLevel) => Competency[];
}

export const useCompetencies = (): UseCompetenciesReturn => {
  const [competencies, setCompetencies] = useState<Competency[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [levelFilter, setLevelFilter] = useState<CompetencyLevel | 'all'>('all');
  const [categoryFilter, setCategoryFilter] = useState('all');

  const loadCompetencies = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await fetchCompetencies();
      setCompetencies(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load competencies');
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshCompetencies = useCallback(async () => {
    await loadCompetencies();
  }, [loadCompetencies]);

  useEffect(() => {
    loadCompetencies();
  }, [loadCompetencies]);

  // Filter competencies based on search term, level, and category
  const filteredCompetencies = competencies.filter(competency => {
    const matchesSearch = searchTerm === '' || 
      competency.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      competency.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      competency.category.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesLevel = levelFilter === 'all' || competency.level === levelFilter;
    
    const matchesCategory = categoryFilter === 'all' || competency.category === categoryFilter;
    
    return matchesSearch && matchesLevel && matchesCategory;
  });

  // Get unique categories
  const competencyCategories = Array.from(
    new Set(competencies.map(competency => competency.category))
  ).sort();

  // Calculate statistics
  const competencyStats = {
    total: competencies.length,
    byLevel: competencies.reduce((acc, competency) => {
      acc[competency.level] = (acc[competency.level] || 0) + 1;
      return acc;
    }, {
      [CompetencyLevel.BEGINNER]: 0,
      [CompetencyLevel.DEVELOPING]: 0,
      [CompetencyLevel.PROFICIENT]: 0,
      [CompetencyLevel.ADVANCED]: 0,
      [CompetencyLevel.EXPERT]: 0
    } as Record<CompetencyLevel, number>),
    byCategory: competencies.reduce((acc, competency) => {
      acc[competency.category] = (acc[competency.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>)
  };

  // Utility functions
  const getCompetencyById = useCallback((id: string): Competency | undefined => {
    return competencies.find(competency => competency.id === id);
  }, [competencies]);

  const getCompetenciesByCategory = useCallback((category: string): Competency[] => {
    return competencies.filter(competency => competency.category === category);
  }, [competencies]);

  const getCompetenciesByLevel = useCallback((level: CompetencyLevel): Competency[] => {
    return competencies.filter(competency => competency.level === level);
  }, [competencies]);

  return {
    competencies,
    loading,
    error,
    searchTerm,
    levelFilter,
    categoryFilter,
    setSearchTerm,
    setLevelFilter,
    setCategoryFilter,
    refreshCompetencies,
    filteredCompetencies,
    competencyCategories,
    competencyStats,
    getCompetencyById,
    getCompetenciesByCategory,
    getCompetenciesByLevel
  };
};

export default useCompetencies;