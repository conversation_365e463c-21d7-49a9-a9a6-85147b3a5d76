import { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  ApprovalWorkflow, 
  Trainee, 
  User, 
  ApprovalStatus, 
  UserRole 
} from '../types';
import { 
  fetchWorkflows, 
  fetchTrainees, 
  fetchUsers,
  submitApprovalDecision 
} from '../mockApi';

interface ApprovalStep {
  id: string;
  stepNumber: number;
  approverRole: UserRole;
  approverId?: string;
  status: ApprovalStatus;
  approvedAt?: string;
  rejectedAt?: string;
  comments?: string;
  required: boolean;
}

interface ApprovalRequest {
  id: string;
  type: 'program_completion' | 'extension_request' | 'early_graduation' | 'program_change' | 'leave_request';
  traineeId: string;
  requestedBy: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  dueDate: string;
  attachments?: string[];
  metadata?: Record<string, any>;
  steps: ApprovalStep[];
  currentStep: number;
  overallStatus: ApprovalStatus;
  createdAt: string;
  updatedAt: string;
}

interface ApprovalAction {
  action: 'approve' | 'reject' | 'request_changes' | 'delegate';
  comments?: string;
  delegateTo?: string;
  attachments?: string[];
}

interface BulkApprovalRequest {
  requestIds: string[];
  action: 'approve' | 'reject';
  comments?: string;
}

interface ApprovalMetrics {
  totalRequests: number;
  pendingRequests: number;
  approvedRequests: number;
  rejectedRequests: number;
  averageProcessingTime: number; // in days
  overdueRequests: number;
  requestsByType: Record<string, number>;
  requestsByPriority: Record<string, number>;
}

interface UseApprovalProcessOptions {
  userId?: string;
  autoLoadPending?: boolean;
  includeCompleted?: boolean;
  filterByRole?: UserRole;
}

interface UseApprovalProcessReturn {
  workflows: ApprovalWorkflow[];
  requests: ApprovalRequest[];
  pendingRequests: ApprovalRequest[];
  myPendingApprovals: ApprovalRequest[];
  overdueRequests: ApprovalRequest[];
  metrics: ApprovalMetrics;
  loading: boolean;
  error: string | null;
  submitApprovalRequest: (request: Omit<ApprovalRequest, 'id' | 'createdAt' | 'updatedAt' | 'currentStep' | 'overallStatus'>) => Promise<ApprovalRequest | null>;
  processApproval: (requestId: string, action: ApprovalAction) => Promise<boolean>;
  bulkProcessApprovals: (bulkRequest: BulkApprovalRequest) => Promise<{ processed: string[]; failed: string[] }>;
  delegateApproval: (requestId: string, delegateTo: string, comments?: string) => Promise<boolean>;
  escalateRequest: (requestId: string, reason: string) => Promise<boolean>;
  withdrawRequest: (requestId: string, reason: string) => Promise<boolean>;
  getApprovalHistory: (requestId: string) => Promise<ApprovalStep[]>;
  getRequestsByStatus: (status: ApprovalStatus) => ApprovalRequest[];
  getRequestsByType: (type: string) => ApprovalRequest[];
  searchRequests: (query: string) => ApprovalRequest[];
  refreshApprovals: () => Promise<void>;
}

export const useApprovalProcess = (options: UseApprovalProcessOptions = {}): UseApprovalProcessReturn => {
  const { userId, autoLoadPending = true, includeCompleted = false, filterByRole } = options;

  const [workflows, setWorkflows] = useState<ApprovalWorkflow[]>([]);
  const [requests, setRequests] = useState<ApprovalRequest[]>([]);
  const [trainees, setTrainees] = useState<Trainee[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchApprovalData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const [workflowsData, traineesData, usersData] = await Promise.all([
        fetchWorkflows(),
        fetchTrainees(),
        fetchUsers()
      ]);

      setWorkflows(workflowsData);
      setTrainees(traineesData);
      setUsers(usersData);

      // Convert workflows to approval requests format
      const approvalRequests: ApprovalRequest[] = workflowsData.map(workflow => ({
        id: workflow.id,
        type: 'program_completion', // Default type, would be determined by workflow context
        traineeId: workflow.traineeId,
        requestedBy: workflow.initiatedBy,
        title: `${workflow.type} - ${workflow.traineeId}`,
        description: workflow.description || `${workflow.type} approval request`,
        priority: 'medium', // Default priority
        dueDate: workflow.dueDate,
        steps: workflow.steps.map((step, index) => ({
          id: step.id,
          stepNumber: index + 1,
          approverRole: step.approverRole,
          approverId: step.approverId,
          status: step.status,
          approvedAt: step.approvedAt,
          rejectedAt: step.rejectedAt,
          comments: step.comments,
          required: step.required
        })),
        currentStep: workflow.currentStep,
        overallStatus: workflow.status,
        createdAt: workflow.createdAt,
        updatedAt: workflow.updatedAt
      }));

      setRequests(includeCompleted ? approvalRequests : approvalRequests.filter(r => r.overallStatus === ApprovalStatus.PENDING));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch approval data');
    } finally {
      setLoading(false);
    }
  }, [includeCompleted]);

  const refreshApprovals = useCallback(() => fetchApprovalData(), [fetchApprovalData]);

  // Calculate pending requests
  const pendingRequests = useMemo(() => {
    return requests.filter(request => request.overallStatus === ApprovalStatus.PENDING);
  }, [requests]);

  // Calculate user's pending approvals
  const myPendingApprovals = useMemo(() => {
    if (!userId) return [];
    
    return requests.filter(request => {
      if (request.overallStatus !== ApprovalStatus.PENDING) return false;
      
      const currentStep = request.steps[request.currentStep - 1];
      if (!currentStep) return false;
      
      // Check if user is the assigned approver or has the required role
      return currentStep.approverId === userId || 
             (filterByRole && currentStep.approverRole === filterByRole);
    });
  }, [requests, userId, filterByRole]);

  // Calculate overdue requests
  const overdueRequests = useMemo(() => {
    const now = new Date();
    return requests.filter(request => {
      const dueDate = new Date(request.dueDate);
      return dueDate < now && request.overallStatus === ApprovalStatus.PENDING;
    });
  }, [requests]);

  // Calculate metrics
  const metrics = useMemo((): ApprovalMetrics => {
    const totalRequests = requests.length;
    const pendingCount = requests.filter(r => r.overallStatus === ApprovalStatus.PENDING).length;
    const approvedCount = requests.filter(r => r.overallStatus === ApprovalStatus.APPROVED).length;
    const rejectedCount = requests.filter(r => r.overallStatus === ApprovalStatus.REJECTED).length;
    const overdueCount = overdueRequests.length;

    // Calculate average processing time for completed requests
    const completedRequests = requests.filter(r => r.overallStatus !== ApprovalStatus.PENDING);
    const totalProcessingTime = completedRequests.reduce((sum, request) => {
      const created = new Date(request.createdAt);
      const updated = new Date(request.updatedAt);
      return sum + (updated.getTime() - created.getTime());
    }, 0);
    const averageProcessingTime = completedRequests.length > 0 
      ? totalProcessingTime / completedRequests.length / (1000 * 60 * 60 * 24) // Convert to days
      : 0;

    // Group by type
    const requestsByType = requests.reduce((acc, request) => {
      acc[request.type] = (acc[request.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Group by priority
    const requestsByPriority = requests.reduce((acc, request) => {
      acc[request.priority] = (acc[request.priority] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalRequests,
      pendingRequests: pendingCount,
      approvedRequests: approvedCount,
      rejectedRequests: rejectedCount,
      averageProcessingTime,
      overdueRequests: overdueCount,
      requestsByType,
      requestsByPriority
    };
  }, [requests, overdueRequests]);

  const submitApprovalRequest = useCallback(async (
    requestData: Omit<ApprovalRequest, 'id' | 'createdAt' | 'updatedAt' | 'currentStep' | 'overallStatus'>
  ): Promise<ApprovalRequest | null> => {
    try {
      // Note: This would need to be implemented in the actual API
      // For now, we'll simulate creating a workflow
      const newWorkflow: ApprovalWorkflow = {
        id: `workflow-${Date.now()}`,
        programId: requestData.traineeId, // Using traineeId as programId for now
        traineeId: requestData.traineeId,
        currentStage: 1,
        stages: requestData.steps.map((step, index) => ({
          id: step.id,
          stageNumber: index + 1,
          approverRole: step.approverRole,
          approverId: step.approverId,
          status: step.status,
          approvedAt: step.approvedAt,
          rejectedAt: step.rejectedAt,
          comments: step.comments,
          required: step.required
        })),
        status: ApprovalStatus.PENDING,
        initiatedBy: requestData.requestedBy,
        initiatedAt: new Date().toISOString()
      };

      const newRequest: ApprovalRequest = {
        ...requestData,
        id: newWorkflow.id,
        currentStep: 1,
        overallStatus: ApprovalStatus.PENDING,
        createdAt: newWorkflow.initiatedAt,
        updatedAt: newWorkflow.initiatedAt
      };
      
      setRequests(prev => [...prev, newRequest]);
      setWorkflows(prev => [...prev, newWorkflow]);
      return newRequest;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to submit approval request');
      return null;
    }
  }, []);

  const processApproval = useCallback(async (requestId: string, action: ApprovalAction): Promise<boolean> => {
    try {
      const request = requests.find(r => r.id === requestId);
      if (!request) {
        setError('Request not found');
        return false;
      }

      const currentStep = request.steps[request.currentStep - 1];
      if (!currentStep) {
        setError('Invalid current step');
        return false;
      }

      // Update current step
      const updatedStep = {
        ...currentStep,
        status: action.action === 'approve' ? ApprovalStatus.APPROVED : 
                action.action === 'reject' ? ApprovalStatus.REJECTED : ApprovalStatus.PENDING,
        approvedAt: action.action === 'approve' ? new Date().toISOString() : currentStep.approvedAt,
        rejectedAt: action.action === 'reject' ? new Date().toISOString() : currentStep.rejectedAt,
        comments: action.comments || currentStep.comments
      };

      const updatedSteps = request.steps.map((step, index) => 
        index === request.currentStep - 1 ? updatedStep : step
      );

      // Determine next step and overall status
      let nextStep = request.currentStep;
      let overallStatus = request.overallStatus;

      if (action.action === 'approve') {
        // Move to next step if there are more steps
        const remainingSteps = updatedSteps.slice(request.currentStep);
        const nextRequiredStep = remainingSteps.find(step => step.required && step.status === ApprovalStatus.PENDING);
        
        if (nextRequiredStep) {
          nextStep = updatedSteps.findIndex(step => step.id === nextRequiredStep.id) + 1;
        } else {
          // All required steps completed
          overallStatus = ApprovalStatus.APPROVED;
        }
      } else if (action.action === 'reject') {
        overallStatus = ApprovalStatus.REJECTED;
      }

      // Update workflow
      // Note: This would need to be implemented in the actual API
      // For now, we'll simulate updating a workflow
      const updatedWorkflow = workflows.find(w => w.id === requestId);
      if (!updatedWorkflow) {
        setError('Workflow not found');
        return false;
      }

      const updatedWorkflowData = {
        ...updatedWorkflow,
        currentStage: nextStep,
        status: overallStatus,
        stages: updatedSteps.map(step => ({
          id: step.id,
          stageNumber: step.stepNumber,
          approverRole: step.approverRole,
          approverId: step.approverId,
          status: step.status,
          approvedAt: step.approvedAt,
          rejectedAt: step.rejectedAt,
          comments: step.comments,
          required: step.required
        }))
      };
      // Update local state
      setRequests(prev => prev.map(r => 
        r.id === requestId 
          ? { ...r, steps: updatedSteps, currentStep: nextStep, overallStatus }
          : r
      ));
      setWorkflows(prev => prev.map(w => w.id === requestId ? updatedWorkflowData : w));
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to process approval');
      return false;
    }
  }, [requests]);

  const bulkProcessApprovals = useCallback(async (bulkRequest: BulkApprovalRequest): Promise<{ processed: string[]; failed: string[] }> => {
    const processed: string[] = [];
    const failed: string[] = [];

    for (const requestId of bulkRequest.requestIds) {
      const success = await processApproval(requestId, {
        action: bulkRequest.action,
        comments: bulkRequest.comments
      });

      if (success) {
        processed.push(requestId);
      } else {
        failed.push(requestId);
      }
    }

    return { processed, failed };
  }, [processApproval]);

  const delegateApproval = useCallback(async (requestId: string, delegateTo: string, comments?: string): Promise<boolean> => {
    try {
      const request = requests.find(r => r.id === requestId);
      if (!request) {
        setError('Request not found');
        return false;
      }

      const currentStep = request.steps[request.currentStep - 1];
      if (!currentStep) {
        setError('Invalid current step');
        return false;
      }

      // Update current step with new approver
      const updatedStep = {
        ...currentStep,
        approverId: delegateTo,
        comments: comments ? `${currentStep.comments || ''} [Delegated: ${comments}]` : currentStep.comments
      };

      const updatedSteps = request.steps.map((step, index) => 
        index === request.currentStep - 1 ? updatedStep : step
      );

      // Note: This would need to be implemented in the actual API
      // For now, we'll simulate updating a workflow
      const workflow = workflows.find(w => w.id === requestId);
      if (!workflow) {
        setError('Workflow not found');
        return false;
      }

      const updatedWorkflowData = {
        ...workflow,
        stages: updatedSteps.map(step => ({
          id: step.id,
          stageNumber: step.stepNumber,
          approverRole: step.approverRole,
          approverId: step.approverId,
          status: step.status,
          approvedAt: step.approvedAt,
          rejectedAt: step.rejectedAt,
          comments: step.comments,
          required: step.required
        }))
      };

      setRequests(prev => prev.map(r => 
        r.id === requestId ? { ...r, steps: updatedSteps } : r
      ));
      setWorkflows(prev => prev.map(w => w.id === requestId ? updatedWorkflowData : w));
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delegate approval');
      return false;
    }
  }, [requests]);

  const escalateRequest = useCallback(async (requestId: string, reason: string): Promise<boolean> => {
    try {
      // In a real implementation, this would escalate to a higher authority
      // For now, we'll just add a comment and potentially change the approver
      const request = requests.find(r => r.id === requestId);
      if (!request) {
        setError('Request not found');
        return false;
      }

      const currentStep = request.steps[request.currentStep - 1];
      if (!currentStep) {
        setError('Invalid current step');
        return false;
      }

      const updatedStep = {
        ...currentStep,
        comments: `${currentStep.comments || ''} [ESCALATED: ${reason}]`
      };

      const updatedSteps = request.steps.map((step, index) => 
        index === request.currentStep - 1 ? updatedStep : step
      );

      // Note: This would need to be implemented in the actual API
      // For now, we'll simulate updating a workflow
      const workflow = workflows.find(w => w.id === requestId);
      if (!workflow) {
        setError('Workflow not found');
        return false;
      }

      const updatedWorkflowData = {
        ...workflow,
        stages: updatedSteps.map(step => ({
          id: step.id,
          stageNumber: step.stepNumber,
          approverRole: step.approverRole,
          approverId: step.approverId,
          status: step.status,
          approvedAt: step.approvedAt,
          rejectedAt: step.rejectedAt,
          comments: step.comments,
          required: step.required
        }))
      };

      setRequests(prev => prev.map(r => 
        r.id === requestId ? { ...r, steps: updatedSteps } : r
      ));
      setWorkflows(prev => prev.map(w => w.id === requestId ? updatedWorkflowData : w));
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to escalate request');
      return false;
    }
  }, [requests]);

  const withdrawRequest = useCallback(async (requestId: string, reason: string): Promise<boolean> => {
    try {
      // Note: This would need to be implemented in the actual API
      // For now, we'll simulate updating a workflow
      const workflow = workflows.find(w => w.id === requestId);
      if (!workflow) {
        setError('Workflow not found');
        return false;
      }

      const updatedWorkflowData = {
        ...workflow,
        status: ApprovalStatus.REJECTED // Using REJECTED to indicate withdrawn
      };

      setRequests(prev => prev.map(r => 
        r.id === requestId ? { ...r, overallStatus: ApprovalStatus.REJECTED } : r
      ));
      setWorkflows(prev => prev.map(w => w.id === requestId ? updatedWorkflowData : w));
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to withdraw request');
      return false;
    }
  }, [workflows]);

  const getApprovalHistory = useCallback(async (requestId: string): Promise<ApprovalStep[]> => {
    const request = requests.find(r => r.id === requestId);
    return request ? request.steps : [];
  }, [requests]);

  const getRequestsByStatus = useCallback((status: ApprovalStatus): ApprovalRequest[] => {
    return requests.filter(request => request.overallStatus === status);
  }, [requests]);

  const getRequestsByType = useCallback((type: string): ApprovalRequest[] => {
    return requests.filter(request => request.type === type);
  }, [requests]);

  const searchRequests = useCallback((query: string): ApprovalRequest[] => {
    const lowercaseQuery = query.toLowerCase();
    return requests.filter(request => 
      request.title.toLowerCase().includes(lowercaseQuery) ||
      request.description.toLowerCase().includes(lowercaseQuery) ||
      request.traineeId.toLowerCase().includes(lowercaseQuery)
    );
  }, [requests]);

  // Initial data fetch
  useEffect(() => {
    fetchApprovalData();
  }, [fetchApprovalData]);

  return {
    workflows,
    requests,
    pendingRequests,
    myPendingApprovals,
    overdueRequests,
    metrics,
    loading,
    error,
    submitApprovalRequest,
    processApproval,
    bulkProcessApprovals,
    delegateApproval,
    escalateRequest,
    withdrawRequest,
    getApprovalHistory,
    getRequestsByStatus,
    getRequestsByType,
    searchRequests,
    refreshApprovals
  };
};