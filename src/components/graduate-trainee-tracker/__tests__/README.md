# Graduate Trainee Tracker - Testing Suite

This directory contains comprehensive test files and utilities for the Graduate Trainee Tracker application.

## Structure

```
__tests__/
├── components/           # Component-specific tests
│   └── TraineeCard.test.tsx
├── hooks/               # Custom hook tests
│   └── useTrainees.test.ts
├── integration/         # Integration tests
│   └── GraduateTraineeTracker.test.tsx
├── test-utils.tsx       # Testing utilities and helpers
├── setup.ts            # Test environment setup
└── README.md           # This file
```

## Test Utilities

### `test-utils.tsx`
Provides comprehensive testing utilities including:

- **Mock Data Generators**: `mockTrainee()`, `mockProgram()`, `mockReview()`, etc.
- **Custom Render Function**: Wraps components with necessary providers
- **Provider Wrapper**: `AllTheProviders` with React Query, Router, API, and Error Boundary
- **Mock API Responses**: Pre-configured API response mocks
- **Utility Functions**: Helper functions for common testing scenarios

### `setup.ts`
Configures the test environment with:

- Jest-DOM matchers for better assertions
- Mock implementations for browser APIs (IntersectionObserver, ResizeObserver, etc.)
- LocalStorage and SessionStorage mocks
- Media query mocks

## Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm test -- --watch

# Run tests with coverage
npm test -- --coverage

# Run specific test file
npm test TraineeCard.test.tsx

# Run tests matching a pattern
npm test -- --grep "trainee"
```

## Writing Tests

### Component Tests

```typescript
import { render, screen } from '../test-utils';
import { MyComponent } from '../../components/MyComponent';

describe('MyComponent', () => {
  it('renders correctly', () => {
    render(<MyComponent />);
    expect(screen.getByText('Expected Text')).toBeTruthy();
  });
});
```

### Hook Tests

```typescript
import { renderHook } from '@testing-library/react';
import { useMyHook } from '../../hooks/useMyHook';

describe('useMyHook', () => {
  it('returns expected values', () => {
    const { result } = renderHook(() => useMyHook());
    expect(result.current.value).toBe('expected');
  });
});
```

### Integration Tests

```typescript
import { render, screen } from '../test-utils';
import { App } from '../../App';

describe('App Integration', () => {
  it('renders full application', () => {
    render(<App />);
    expect(screen.getByTestId('app')).toBeTruthy();
  });
});
```

## Mock Data

The test utilities provide factory functions for creating mock data:

```typescript
// Create a mock trainee with default values
const trainee = mockTrainee();

// Create a mock trainee with custom values
const customTrainee = mockTrainee({
  firstName: 'John',
  lastName: 'Doe',
  status: 'active'
});
```

## Best Practices

1. **Use Descriptive Test Names**: Test names should clearly describe what is being tested
2. **Test User Interactions**: Focus on testing how users interact with components
3. **Mock External Dependencies**: Use mocks for API calls, external services, etc.
4. **Test Error States**: Include tests for error conditions and edge cases
5. **Keep Tests Isolated**: Each test should be independent and not rely on others
6. **Use Custom Render**: Always use the custom render function from test-utils
7. **Test Accessibility**: Include tests for keyboard navigation, screen readers, etc.

## Coverage Goals

- **Components**: 90%+ coverage for all UI components
- **Hooks**: 95%+ coverage for custom hooks
- **Utils**: 100% coverage for utility functions
- **Integration**: Key user flows covered

## Continuous Integration

Tests are automatically run on:
- Pull requests
- Commits to main branch
- Nightly builds

Failing tests will block deployments to ensure code quality.