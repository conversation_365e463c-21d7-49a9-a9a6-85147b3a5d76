import React, { useState, useMemo } from 'react';
import { StatCard, ProgressBar, Badge, Avatar } from './ui';

// Types
export interface DashboardCardProps {
  cardId: string;
  className?: string;
  variant?: 'default' | 'compact' | 'detailed' | 'chart';
  size?: 'small' | 'medium' | 'large';
  refreshable?: boolean;
  collapsible?: boolean;
  removable?: boolean;
  onRefresh?: (cardId: string) => void;
  onRemove?: (cardId: string) => void;
  onExpand?: (cardId: string) => void;
}

export interface DashboardCard {
  id: string;
  type: 'metric' | 'chart' | 'list' | 'progress' | 'activity' | 'summary' | 'custom';
  category: 'trainees' | 'programs' | 'reviews' | 'assessments' | 'workflows' | 'analytics' | 'system';
  title: string;
  subtitle?: string;
  description?: string;
  icon?: string;
  color?: string;
  priority: number;
  position: {
    row: number;
    col: number;
    width: number;
    height: number;
  };
  data: DashboardCardData;
  config: DashboardCardConfig;
  permissions: string[];
  lastUpdated: string;
  refreshInterval?: number; // in seconds
  isLoading?: boolean;
  hasError?: boolean;
  errorMessage?: string;
}

export interface DashboardCardData {
  // Metric data
  value?: number | string;
  previousValue?: number | string;
  target?: number;
  unit?: string;
  trend?: {
    direction: 'up' | 'down' | 'stable';
    percentage: number;
    period: string;
  };
  
  // Chart data
  chartData?: ChartDataPoint[];
  chartType?: 'line' | 'bar' | 'pie' | 'doughnut' | 'area';
  
  // List data
  items?: DashboardListItem[];
  
  // Progress data
  progress?: {
    current: number;
    total: number;
    percentage: number;
    segments?: ProgressSegment[];
  };
  
  // Activity data
  activities?: DashboardActivity[];
  
  // Summary data
  summary?: DashboardSummary;
  
  // Custom data
  customData?: Record<string, any>;
}

export interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
  metadata?: Record<string, any>;
}

export interface DashboardListItem {
  id: string;
  title: string;
  subtitle?: string;
  value?: string | number;
  status?: string;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  avatar?: string;
  url?: string;
  metadata?: Record<string, any>;
}

export interface ProgressSegment {
  label: string;
  value: number;
  color: string;
  percentage: number;
}

export interface DashboardActivity {
  id: string;
  type: 'created' | 'updated' | 'completed' | 'assigned' | 'approved' | 'rejected' | 'deleted';
  title: string;
  description: string;
  user: {
    id: string;
    name: string;
    avatar?: string;
  };
  timestamp: string;
  metadata?: Record<string, any>;
}

export interface DashboardSummary {
  title: string;
  items: DashboardSummaryItem[];
  total?: number;
  footer?: string;
}

export interface DashboardSummaryItem {
  label: string;
  value: number | string;
  change?: {
    value: number;
    percentage: number;
    direction: 'up' | 'down' | 'stable';
  };
  color?: string;
}

export interface DashboardCardConfig {
  showHeader?: boolean;
  showFooter?: boolean;
  showRefreshButton?: boolean;
  showExpandButton?: boolean;
  showRemoveButton?: boolean;
  maxItems?: number;
  dateRange?: {
    start: string;
    end: string;
  };
  filters?: Record<string, any>;
  customSettings?: Record<string, any>;
}

// Utility functions
const formatNumber = (value: number, unit?: string): string => {
  const formatted = new Intl.NumberFormat('en-US', {
    notation: value >= 1000000 ? 'compact' : 'standard',
    maximumFractionDigits: 1
  }).format(value);
  
  return unit ? `${formatted} ${unit}` : formatted;
};

const formatPercentage = (value: number): string => {
  return `${value.toFixed(1)}%`;
};

const formatTimeAgo = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  if (diffInSeconds < 60) return 'Just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
  if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
  return `${Math.floor(diffInSeconds / 604800)}w ago`;
};

const getTrendColor = (direction: string): string => {
  const colors = {
    up: 'success',
    down: 'error',
    stable: 'default'
  };
  return colors[direction as keyof typeof colors] || 'default';
};

const getTrendIcon = (direction: string): string => {
  const icons = {
    up: '↗️',
    down: '↘️',
    stable: '→'
  };
  return icons[direction as keyof typeof icons] || '→';
};

const getPriorityColor = (priority: string): string => {
  const colors = {
    low: 'default',
    medium: 'info',
    high: 'warning',
    urgent: 'error'
  };
  return colors[priority as keyof typeof colors] || 'default';
};

const getStatusColor = (status: string): string => {
  const colors = {
    active: 'success',
    pending: 'warning',
    completed: 'success',
    cancelled: 'error',
    draft: 'default',
    in_progress: 'info',
    overdue: 'error'
  };
  return colors[status as keyof typeof colors] || 'default';
};

const getActivityIcon = (type: string): string => {
  const icons = {
    created: '➕',
    updated: '✏️',
    completed: '✅',
    assigned: '👤',
    approved: '✅',
    rejected: '❌',
    deleted: '🗑️'
  };
  return icons[type as keyof typeof icons] || '📝';
};

// Sub-components
interface DashboardCardHeaderProps {
  card: DashboardCard;
  variant: string;
  onRefresh?: (cardId: string) => void;
  onRemove?: (cardId: string) => void;
  onExpand?: (cardId: string) => void;
}

const DashboardCardHeader: React.FC<DashboardCardHeaderProps> = ({
  card,
  variant,
  onRefresh,
  onRemove,
  onExpand
}) => {
  if (!card.config.showHeader) return null;
  
  return (
    <div className="dashboard-card__header">
      <div className="dashboard-card__title-section">
        {card.icon && (
          <span className="dashboard-card__icon">{card.icon}</span>
        )}
        
        <div className="dashboard-card__title-content">
          <h3 className="dashboard-card__title">{card.title}</h3>
          {card.subtitle && (
            <p className="dashboard-card__subtitle">{card.subtitle}</p>
          )}
        </div>
        
        {card.data.trend && (
          <div className="dashboard-card__trend">
            <Badge variant={getTrendColor(card.data.trend.direction) as any} size="small">
              {getTrendIcon(card.data.trend.direction)} {formatPercentage(card.data.trend.percentage)}
            </Badge>
          </div>
        )}
      </div>
      
      <div className="dashboard-card__actions">
        {card.config.showRefreshButton && onRefresh && (
          <button
            onClick={() => onRefresh(card.id)}
            className="dashboard-card__action dashboard-card__action--refresh"
            title="Refresh data"
            disabled={card.isLoading}
          >
            🔄
          </button>
        )}
        
        {card.config.showExpandButton && onExpand && variant !== 'detailed' && (
          <button
            onClick={() => onExpand(card.id)}
            className="dashboard-card__action dashboard-card__action--expand"
            title="Expand card"
          >
            ⛶
          </button>
        )}
        
        {card.config.showRemoveButton && onRemove && (
          <button
            onClick={() => onRemove(card.id)}
            className="dashboard-card__action dashboard-card__action--remove"
            title="Remove card"
          >
            ×
          </button>
        )}
      </div>
    </div>
  );
};

interface MetricContentProps {
  data: DashboardCardData;
  variant: string;
}

const MetricContent: React.FC<MetricContentProps> = ({ data, variant }) => {
  if (!data.value) return null;
  
  return (
    <div className="dashboard-card__metric">
      <div className="dashboard-card__metric-value">
        <span className="dashboard-card__metric-number">
          {typeof data.value === 'number' ? formatNumber(data.value, data.unit) : data.value}
        </span>
        
        {data.target && typeof data.value === 'number' && (
          <span className="dashboard-card__metric-target">
            / {formatNumber(data.target, data.unit)}
          </span>
        )}
      </div>
      
      {data.previousValue && (
        <div className="dashboard-card__metric-comparison">
          <span className="dashboard-card__metric-previous">
            Previous: {typeof data.previousValue === 'number' ? formatNumber(data.previousValue, data.unit) : data.previousValue}
          </span>
        </div>
      )}
      
      {data.trend && variant !== 'compact' && (
        <div className="dashboard-card__metric-trend">
          <span className={`dashboard-card__metric-trend-text dashboard-card__metric-trend--${data.trend.direction}`}>
            {getTrendIcon(data.trend.direction)} {formatPercentage(data.trend.percentage)} {data.trend.period}
          </span>
        </div>
      )}
    </div>
  );
};

interface ProgressContentProps {
  data: DashboardCardData;
  variant: string;
}

const ProgressContent: React.FC<ProgressContentProps> = ({ data, variant }) => {
  if (!data.progress) return null;
  
  const { current, total, percentage, segments } = data.progress;
  
  return (
    <div className="dashboard-card__progress">
      <div className="dashboard-card__progress-header">
        <span className="dashboard-card__progress-label">
          {current} of {total}
        </span>
        <span className="dashboard-card__progress-percentage">
          {formatPercentage(percentage)}
        </span>
      </div>
      
      <ProgressBar
        value={percentage}
        max={100}
        size={variant === 'compact' ? 'small' : 'medium'}
        color={percentage >= 90 ? 'success' : percentage >= 70 ? 'info' : percentage >= 50 ? 'warning' : 'error'}
      />
      
      {segments && variant !== 'compact' && (
        <div className="dashboard-card__progress-segments">
          {segments.map((segment, index) => (
            <div key={index} className="dashboard-card__progress-segment">
              <div 
                className="dashboard-card__progress-segment-bar"
                style={{ 
                  width: `${segment.percentage}%`,
                  backgroundColor: segment.color
                }}
              />
              <span className="dashboard-card__progress-segment-label">
                {segment.label}: {segment.value}
              </span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

interface ListContentProps {
  data: DashboardCardData;
  variant: string;
  maxItems?: number;
}

const ListContent: React.FC<ListContentProps> = ({ data, variant, maxItems = 5 }) => {
  if (!data.items || data.items.length === 0) return null;
  
  const visibleItems = data.items.slice(0, maxItems);
  const hasMoreItems = data.items.length > maxItems;
  
  return (
    <div className="dashboard-card__list">
      {visibleItems.map((item) => (
        <div key={item.id} className="dashboard-card__list-item">
          <div className="dashboard-card__list-item-content">
            {item.avatar && (
              <Avatar
                src={item.avatar}
                alt={item.title}
                size="small"
                fallback={item.title.charAt(0)}
              />
            )}
            
            <div className="dashboard-card__list-item-text">
              <div className="dashboard-card__list-item-title">
                {item.url ? (
                  <a href={item.url} className="dashboard-card__list-item-link">
                    {item.title}
                  </a>
                ) : (
                  item.title
                )}
              </div>
              
              {item.subtitle && variant !== 'compact' && (
                <div className="dashboard-card__list-item-subtitle">
                  {item.subtitle}
                </div>
              )}
            </div>
          </div>
          
          <div className="dashboard-card__list-item-meta">
            {item.value && (
              <span className="dashboard-card__list-item-value">
                {item.value}
              </span>
            )}
            
            {item.status && (
              <Badge variant={getStatusColor(item.status) as any} size="small">
                {item.status}
              </Badge>
            )}
            
            {item.priority && (
              <Badge variant={getPriorityColor(item.priority) as any} size="small">
                {item.priority}
              </Badge>
            )}
          </div>
        </div>
      ))}
      
      {hasMoreItems && (
        <div className="dashboard-card__list-more">
          +{data.items.length - maxItems} more items
        </div>
      )}
    </div>
  );
};

interface ActivityContentProps {
  data: DashboardCardData;
  variant: string;
  maxItems?: number;
}

const ActivityContent: React.FC<ActivityContentProps> = ({ data, variant, maxItems = 5 }) => {
  if (!data.activities || data.activities.length === 0) return null;
  
  const visibleActivities = data.activities.slice(0, maxItems);
  const hasMoreActivities = data.activities.length > maxItems;
  
  return (
    <div className="dashboard-card__activity">
      {visibleActivities.map((activity) => (
        <div key={activity.id} className="dashboard-card__activity-item">
          <div className="dashboard-card__activity-icon">
            {getActivityIcon(activity.type)}
          </div>
          
          <div className="dashboard-card__activity-content">
            <div className="dashboard-card__activity-header">
              <span className="dashboard-card__activity-title">{activity.title}</span>
              <span className="dashboard-card__activity-time">
                {formatTimeAgo(activity.timestamp)}
              </span>
            </div>
            
            {variant !== 'compact' && (
              <div className="dashboard-card__activity-description">
                {activity.description}
              </div>
            )}
            
            <div className="dashboard-card__activity-user">
              <Avatar
                src={activity.user.avatar}
                alt={activity.user.name}
                size="small"
                fallback={activity.user.name.charAt(0)}
              />
              <span className="dashboard-card__activity-user-name">
                {activity.user.name}
              </span>
            </div>
          </div>
        </div>
      ))}
      
      {hasMoreActivities && (
        <div className="dashboard-card__activity-more">
          +{data.activities.length - maxItems} more activities
        </div>
      )}
    </div>
  );
};

interface SummaryContentProps {
  data: DashboardCardData;
  variant: string;
}

const SummaryContent: React.FC<SummaryContentProps> = ({ data, variant }) => {
  if (!data.summary) return null;
  
  const { title, items, total, footer } = data.summary;
  
  return (
    <div className="dashboard-card__summary">
      <h4 className="dashboard-card__summary-title">{title}</h4>
      
      <div className="dashboard-card__summary-items">
        {items.map((item, index) => (
          <div key={index} className="dashboard-card__summary-item">
            <div className="dashboard-card__summary-item-content">
              <span className="dashboard-card__summary-item-label">{item.label}</span>
              <span className="dashboard-card__summary-item-value">
                {typeof item.value === 'number' ? formatNumber(item.value) : item.value}
              </span>
            </div>
            
            {item.change && variant !== 'compact' && (
              <div className="dashboard-card__summary-item-change">
                <Badge variant={getTrendColor(item.change.direction) as any} size="small">
                  {getTrendIcon(item.change.direction)} {formatPercentage(item.change.percentage)}
                </Badge>
              </div>
            )}
          </div>
        ))}
      </div>
      
      {total && (
        <div className="dashboard-card__summary-total">
          <strong>Total: {formatNumber(total)}</strong>
        </div>
      )}
      
      {footer && (
        <div className="dashboard-card__summary-footer">
          {footer}
        </div>
      )}
    </div>
  );
};

interface DashboardCardFooterProps {
  card: DashboardCard;
}

const DashboardCardFooter: React.FC<DashboardCardFooterProps> = ({ card }) => {
  if (!card.config.showFooter) return null;
  
  return (
    <div className="dashboard-card__footer">
      <div className="dashboard-card__footer-info">
        <span className="dashboard-card__last-updated">
          Last updated: {formatTimeAgo(card.lastUpdated)}
        </span>
        
        {card.refreshInterval && (
          <span className="dashboard-card__refresh-interval">
            Refreshes every {card.refreshInterval}s
          </span>
        )}
      </div>
      
      {card.description && (
        <div className="dashboard-card__description">
          {card.description}
        </div>
      )}
    </div>
  );
};

// Main DashboardCard Component
export const DashboardCard: React.FC<DashboardCardProps> = ({
  cardId,
  className = '',
  variant = 'default',
  size = 'medium',
  refreshable = true,
  collapsible = false,
  removable = false,
  onRefresh,
  onRemove,
  onExpand
}) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  
  // Mock data - in real implementation, this would come from a hook
  const mockCard: DashboardCard = {
    id: cardId,
    type: 'metric',
    category: 'trainees',
    title: 'Active Trainees',
    subtitle: 'Currently enrolled in programs',
    description: 'Total number of trainees actively participating in training programs',
    icon: '👥',
    color: '#3b82f6',
    priority: 1,
    position: {
      row: 1,
      col: 1,
      width: 1,
      height: 1
    },
    data: {
      value: 142,
      previousValue: 138,
      target: 150,
      unit: 'trainees',
      trend: {
        direction: 'up',
        percentage: 2.9,
        period: 'vs last month'
      },
      progress: {
        current: 142,
        total: 150,
        percentage: 94.7,
        segments: [
          { label: 'Beginner', value: 45, color: '#ef4444', percentage: 31.7 },
          { label: 'Intermediate', value: 62, color: '#f59e0b', percentage: 43.7 },
          { label: 'Advanced', value: 35, color: '#10b981', percentage: 24.6 }
        ]
      },
      items: [
        {
          id: 'trainee-1',
          title: 'John Doe',
          subtitle: 'Software Development Program',
          value: '85%',
          status: 'active',
          priority: 'medium',
          avatar: '/avatars/john-doe.jpg',
          url: '/trainees/john-doe'
        },
        {
          id: 'trainee-2',
          title: 'Jane Smith',
          subtitle: 'Data Analytics Program',
          value: '92%',
          status: 'active',
          priority: 'high',
          avatar: '/avatars/jane-smith.jpg',
          url: '/trainees/jane-smith'
        },
        {
          id: 'trainee-3',
          title: 'Mike Johnson',
          subtitle: 'DevOps Program',
          value: '78%',
          status: 'active',
          priority: 'low',
          avatar: '/avatars/mike-johnson.jpg',
          url: '/trainees/mike-johnson'
        }
      ],
      activities: [
        {
          id: 'activity-1',
          type: 'completed',
          title: 'Assessment Completed',
          description: 'John Doe completed React Fundamentals assessment',
          user: {
            id: 'trainee-1',
            name: 'John Doe',
            avatar: '/avatars/john-doe.jpg'
          },
          timestamp: '2024-02-10T14:30:00Z'
        },
        {
          id: 'activity-2',
          type: 'assigned',
          title: 'New Assignment',
          description: 'Jane Smith assigned to Advanced JavaScript module',
          user: {
            id: 'hr-1',
            name: 'HR Manager'
          },
          timestamp: '2024-02-10T11:15:00Z'
        }
      ],
      summary: {
        title: 'Trainee Overview',
        items: [
          {
            label: 'Total Enrolled',
            value: 142,
            change: { value: 4, percentage: 2.9, direction: 'up' }
          },
          {
            label: 'Completed Programs',
            value: 89,
            change: { value: 12, percentage: 15.6, direction: 'up' }
          },
          {
            label: 'In Progress',
            value: 53,
            change: { value: -8, percentage: -13.1, direction: 'down' }
          }
        ],
        total: 142,
        footer: 'Data as of February 2024'
      }
    },
    config: {
      showHeader: true,
      showFooter: true,
      showRefreshButton: refreshable,
      showExpandButton: true,
      showRemoveButton: removable,
      maxItems: 5
    },
    permissions: ['read', 'refresh'],
    lastUpdated: '2024-02-10T15:30:00Z',
    refreshInterval: 300,
    isLoading: false,
    hasError: false
  };
  
  const card = mockCard;
  
  const handleToggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };
  
  const containerClasses = [
    'dashboard-card',
    `dashboard-card--${variant}`,
    `dashboard-card--${size}`,
    `dashboard-card--${card.type}`,
    card.isLoading ? 'dashboard-card--loading' : '',
    card.hasError ? 'dashboard-card--error' : '',
    isCollapsed ? 'dashboard-card--collapsed' : '',
    className
  ].filter(Boolean).join(' ');
  
  if (card.hasError) {
    return (
      <div className={containerClasses}>
        <div className="dashboard-card__error">
          <div className="dashboard-card__error-icon">⚠️</div>
          <div className="dashboard-card__error-message">
            {card.errorMessage || 'Failed to load card data'}
          </div>
          {onRefresh && (
            <button
              onClick={() => onRefresh(card.id)}
              className="dashboard-card__error-retry"
            >
              Retry
            </button>
          )}
        </div>
      </div>
    );
  }
  
  return (
    <div className={containerClasses}>
      <DashboardCardHeader
        card={card}
        variant={variant}
        onRefresh={onRefresh}
        onRemove={onRemove}
        onExpand={onExpand}
      />
      
      {card.isLoading ? (
        <div className="dashboard-card__loading">
          <div className="dashboard-card__loading-spinner">⏳</div>
          <div className="dashboard-card__loading-message">Loading...</div>
        </div>
      ) : !isCollapsed && (
        <div className="dashboard-card__content">
          {card.type === 'metric' && (
            <MetricContent data={card.data} variant={variant} />
          )}
          
          {card.type === 'progress' && (
            <ProgressContent data={card.data} variant={variant} />
          )}
          
          {card.type === 'list' && (
            <ListContent 
              data={card.data} 
              variant={variant} 
              maxItems={card.config.maxItems}
            />
          )}
          
          {card.type === 'activity' && (
            <ActivityContent 
              data={card.data} 
              variant={variant} 
              maxItems={card.config.maxItems}
            />
          )}
          
          {card.type === 'summary' && (
            <SummaryContent data={card.data} variant={variant} />
          )}
        </div>
      )}
      
      <DashboardCardFooter card={card} />
      
      {collapsible && (
        <button
          onClick={handleToggleCollapse}
          className="dashboard-card__collapse-toggle"
          title={isCollapsed ? 'Expand card' : 'Collapse card'}
        >
          {isCollapsed ? '▼' : '▲'}
        </button>
      )}
    </div>
  );
};

export default DashboardCard;