import React, { useState, useEffect, useMemo } from 'react';
import { <PERSON><PERSON><PERSON>, DatePicker, MultiSelect, Badge, ProgressBar } from './ui';
import { useReviews, useTrainees, useCompetencies } from '../hooks';

// Types
export interface ReviewFormProps {
  reviewId?: string;
  traineeId?: string;
  onSubmit: (data: ReviewFormData) => void;
  onCancel?: () => void;
  className?: string;
  mode?: 'create' | 'edit' | 'view';
  initialData?: Partial<ReviewFormData>;
}

export interface ReviewFormData {
  traineeId: string;
  reviewerId: string;
  type: 'quarterly' | 'mid-year' | 'annual' | 'probation' | 'project' | 'ad-hoc';
  period: string;
  reviewDate: string;
  dueDate: string;
  status: 'draft' | 'in_progress' | 'completed' | 'approved' | 'rejected';
  overallRating: number;
  summary: string;
  strengths: string[];
  areasForImprovement: string[];
  goals: ReviewGoal[];
  competencyRatings: CompetencyRating[];
  feedback: ReviewFeedback[];
  actionItems: ActionItem[];
  nextReviewDate?: string;
  reviewerComments?: string;
  traineeComments?: string;
  hrComments?: string;
  attachments?: ReviewAttachment[];
}

export interface ReviewGoal {
  id: string;
  description: string;
  targetDate: string;
  priority: 'low' | 'medium' | 'high';
  status: 'not_started' | 'in_progress' | 'completed';
  progress: number;
  notes?: string;
}

export interface CompetencyRating {
  competencyId: string;
  rating: number;
  comments?: string;
  evidence?: string[];
}

export interface ReviewFeedback {
  id: string;
  category: 'technical' | 'behavioral' | 'leadership' | 'communication' | 'teamwork';
  feedback: string;
  rating: number;
  examples?: string[];
}

export interface ActionItem {
  id: string;
  description: string;
  assignee: string;
  dueDate: string;
  priority: 'low' | 'medium' | 'high';
  status: 'pending' | 'in_progress' | 'completed';
  notes?: string;
}

export interface ReviewAttachment {
  id: string;
  name: string;
  type: string;
  url: string;
  uploadedAt: string;
}

// Utility functions
const validateForm = (data: Partial<ReviewFormData>): string[] => {
  const errors: string[] = [];
  
  if (!data.traineeId) {
    errors.push('Trainee is required');
  }
  
  if (!data.reviewerId) {
    errors.push('Reviewer is required');
  }
  
  if (!data.type) {
    errors.push('Review type is required');
  }
  
  if (!data.reviewDate) {
    errors.push('Review date is required');
  }
  
  if (!data.dueDate) {
    errors.push('Due date is required');
  }
  
  if (data.reviewDate && data.dueDate && new Date(data.reviewDate) > new Date(data.dueDate)) {
    errors.push('Review date cannot be after due date');
  }
  
  if (data.overallRating !== undefined && (data.overallRating < 1 || data.overallRating > 5)) {
    errors.push('Overall rating must be between 1 and 5');
  }
  
  if (!data.summary?.trim()) {
    errors.push('Review summary is required');
  }
  
  return errors;
};

const getRatingLabel = (rating: number): string => {
  const labels = {
    1: 'Poor',
    2: 'Below Expectations',
    3: 'Meets Expectations',
    4: 'Exceeds Expectations',
    5: 'Outstanding'
  };
  return labels[rating as keyof typeof labels] || 'Not Rated';
};

const getRatingColor = (rating: number): string => {
  if (rating >= 4.5) return 'success';
  if (rating >= 3.5) return 'info';
  if (rating >= 2.5) return 'warning';
  return 'error';
};

const getPriorityColor = (priority: string): string => {
  const colors = {
    low: 'info',
    medium: 'warning',
    high: 'error'
  };
  return colors[priority as keyof typeof colors] || 'default';
};

const getStatusColor = (status: string): string => {
  const colors = {
    draft: 'default',
    in_progress: 'info',
    completed: 'success',
    approved: 'success',
    rejected: 'error',
    pending: 'warning',
    not_started: 'default'
  };
  return colors[status as keyof typeof colors] || 'default';
};

// Sub-components
interface BasicInfoProps {
  data: Partial<ReviewFormData>;
  onChange: (field: string, value: any) => void;
  errors: string[];
  mode: 'create' | 'edit' | 'view';
}

const BasicInfo: React.FC<BasicInfoProps> = ({ data, onChange, errors, mode }) => {
  const isReadOnly = mode === 'view';
  const { data: trainees } = useTrainees({});
  
  const reviewTypeOptions = [
    { value: 'quarterly', label: 'Quarterly Review' },
    { value: 'mid-year', label: 'Mid-Year Review' },
    { value: 'annual', label: 'Annual Review' },
    { value: 'probation', label: 'Probation Review' },
    { value: 'project', label: 'Project Review' },
    { value: 'ad-hoc', label: 'Ad-hoc Review' }
  ];
  
  const statusOptions = [
    { value: 'draft', label: 'Draft' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'completed', label: 'Completed' },
    { value: 'approved', label: 'Approved' },
    { value: 'rejected', label: 'Rejected' }
  ];
  
  const traineeOptions = trainees?.map((trainee: any) => ({
    value: trainee.id,
    label: `${trainee.firstName} ${trainee.lastName}`
  })) || [];
  
  return (
    <div className="review-form__section">
      <h3 className="review-form__section-title">Basic Information</h3>
      
      <div className="review-form__grid">
        <FormField name="traineeId" label="Trainee" required error={errors.find(e => e.includes('Trainee'))}>
          <FormField.Select
            value={data.traineeId || ''}
            onChange={(e) => onChange('traineeId', e.target.value)}
            disabled={isReadOnly}
          >
            <option value="">Select trainee</option>
            {traineeOptions.map((option: any) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </FormField.Select>
        </FormField>
        
        <FormField name="reviewerId" label="Reviewer" required error={errors.find(e => e.includes('Reviewer'))}>
          <FormField.Input
            value={data.reviewerId || ''}
            onChange={(e) => onChange('reviewerId', e.target.value)}
            placeholder="Enter reviewer ID"
            disabled={isReadOnly}
          />
        </FormField>
        
        <FormField name="type" label="Review Type" required error={errors.find(e => e.includes('type'))}>
          <FormField.Select
            value={data.type || ''}
            onChange={(e) => onChange('type', e.target.value)}
            disabled={isReadOnly}
          >
            <option value="">Select review type</option>
            {reviewTypeOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </FormField.Select>
        </FormField>
        
        <FormField name="period" label="Review Period">
          <FormField.Input
            value={data.period || ''}
            onChange={(e) => onChange('period', e.target.value)}
            placeholder="e.g., Q1 2024, Jan-Mar 2024"
            disabled={isReadOnly}
          />
        </FormField>
        
        <FormField name="reviewDate" label="Review Date" required error={errors.find(e => e.includes('Review date'))}>
          <DatePicker
            value={data.reviewDate || ''}
            onChange={(date) => onChange('reviewDate', date)}
            disabled={isReadOnly}
          />
        </FormField>
        
        <FormField name="dueDate" label="Due Date" required error={errors.find(e => e.includes('Due date'))}>
          <DatePicker
            value={data.dueDate || ''}
            onChange={(date) => onChange('dueDate', date)}
            disabled={isReadOnly}
            minDate={data.reviewDate ? new Date(data.reviewDate) : undefined}
          />
        </FormField>
        
        <FormField name="status" label="Status">
          <FormField.Select
            value={data.status || 'draft'}
            onChange={(e) => onChange('status', e.target.value)}
            disabled={isReadOnly}
          >
            {statusOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </FormField.Select>
        </FormField>
        
        <FormField name="nextReviewDate" label="Next Review Date">
          <DatePicker
            value={data.nextReviewDate || ''}
            onChange={(date) => onChange('nextReviewDate', date)}
            disabled={isReadOnly}
            minDate={data.reviewDate ? new Date(data.reviewDate) : undefined}
          />
        </FormField>
      </div>
    </div>
  );
};

interface RatingsSectionProps {
  data: Partial<ReviewFormData>;
  onChange: (field: string, value: any) => void;
  errors: string[];
  mode: 'create' | 'edit' | 'view';
}

const RatingsSection: React.FC<RatingsSectionProps> = ({ data, onChange, errors, mode }) => {
  const isReadOnly = mode === 'view';
  
  const handleRatingChange = (rating: number) => {
    onChange('overallRating', rating);
  };
  
  return (
    <div className="review-form__section">
      <h3 className="review-form__section-title">Overall Rating</h3>
      
      <div className="review-form__rating-section">
        <FormField name="overallRating" label="Overall Performance Rating" error={errors.find(e => e.includes('rating'))}>
          <div className="review-form__rating-input">
            {!isReadOnly ? (
              <div className="review-form__rating-stars">
                {[1, 2, 3, 4, 5].map((rating) => (
                  <button
                    key={rating}
                    type="button"
                    onClick={() => handleRatingChange(rating)}
                    className={`review-form__rating-star ${
                      (data.overallRating || 0) >= rating ? 'review-form__rating-star--active' : ''
                    }`}
                  >
                    ★
                  </button>
                ))}
              </div>
            ) : (
              <div className="review-form__rating-display">
                <div className="review-form__rating-stars">
                  {[1, 2, 3, 4, 5].map((rating) => (
                    <span
                      key={rating}
                      className={`review-form__rating-star ${
                        (data.overallRating || 0) >= rating ? 'review-form__rating-star--active' : ''
                      }`}
                    >
                      ★
                    </span>
                  ))}
                </div>
              </div>
            )}
            
            <div className="review-form__rating-info">
              <span className="review-form__rating-value">
                {data.overallRating || 0}/5
              </span>
              
              <Badge variant={getRatingColor(data.overallRating || 0) as any} size="small">
                {getRatingLabel(data.overallRating || 0)}
              </Badge>
            </div>
          </div>
        </FormField>
        
        <FormField name="summary" label="Review Summary" required error={errors.find(e => e.includes('summary'))}>
          <FormField.Textarea
            value={data.summary || ''}
            onChange={(e) => onChange('summary', e.target.value)}
            placeholder="Provide an overall summary of the trainee's performance"
            rows={4}
            disabled={isReadOnly}
          />
        </FormField>
      </div>
    </div>
  );
};

interface FeedbackSectionProps {
  strengths: string[];
  areasForImprovement: string[];
  onChange: (field: string, value: string[]) => void;
  mode: 'create' | 'edit' | 'view';
}

const FeedbackSection: React.FC<FeedbackSectionProps> = ({
  strengths,
  areasForImprovement,
  onChange,
  mode
}) => {
  const [newStrength, setNewStrength] = useState('');
  const [newImprovement, setNewImprovement] = useState('');
  const isReadOnly = mode === 'view';
  
  const addStrength = () => {
    if (newStrength.trim() && !strengths.includes(newStrength.trim())) {
      onChange('strengths', [...strengths, newStrength.trim()]);
      setNewStrength('');
    }
  };
  
  const removeStrength = (index: number) => {
    onChange('strengths', strengths.filter((_, i) => i !== index));
  };
  
  const addImprovement = () => {
    if (newImprovement.trim() && !areasForImprovement.includes(newImprovement.trim())) {
      onChange('areasForImprovement', [...areasForImprovement, newImprovement.trim()]);
      setNewImprovement('');
    }
  };
  
  const removeImprovement = (index: number) => {
    onChange('areasForImprovement', areasForImprovement.filter((_, i) => i !== index));
  };
  
  return (
    <div className="review-form__section">
      <h3 className="review-form__section-title">Feedback</h3>
      
      <div className="review-form__feedback-grid">
        <div className="review-form__feedback-column">
          <h4 className="review-form__feedback-subtitle">Strengths</h4>
          
          {!isReadOnly && (
            <div className="review-form__add-item">
              <FormField name="newStrength" label="Add Strength">
                <div className="review-form__add-item-input">
                  <FormField.Input
                    value={newStrength}
                    onChange={(e) => setNewStrength(e.target.value)}
                    placeholder="Enter a strength"
                    onKeyPress={(e) => e.key === 'Enter' && addStrength()}
                  />
                  <button
                    type="button"
                    onClick={addStrength}
                    className="review-form__add-btn"
                    disabled={!newStrength.trim()}
                  >
                    Add
                  </button>
                </div>
              </FormField>
            </div>
          )}
          
          <div className="review-form__list">
            {strengths.map((strength, index) => (
              <div key={index} className="review-form__list-item review-form__list-item--positive">
                <span className="review-form__list-text">{strength}</span>
                
                {!isReadOnly && (
                  <button
                    type="button"
                    onClick={() => removeStrength(index)}
                    className="review-form__remove-btn"
                  >
                    Remove
                  </button>
                )}
              </div>
            ))}
          </div>
          
          {strengths.length === 0 && (
            <div className="review-form__empty-state">
              No strengths identified yet.
            </div>
          )}
        </div>
        
        <div className="review-form__feedback-column">
          <h4 className="review-form__feedback-subtitle">Areas for Improvement</h4>
          
          {!isReadOnly && (
            <div className="review-form__add-item">
              <FormField name="newImprovement" label="Add Area for Improvement">
                <div className="review-form__add-item-input">
                  <FormField.Input
                    value={newImprovement}
                    onChange={(e) => setNewImprovement(e.target.value)}
                    placeholder="Enter an area for improvement"
                    onKeyPress={(e) => e.key === 'Enter' && addImprovement()}
                  />
                  <button
                    type="button"
                    onClick={addImprovement}
                    className="review-form__add-btn"
                    disabled={!newImprovement.trim()}
                  >
                    Add
                  </button>
                </div>
              </FormField>
            </div>
          )}
          
          <div className="review-form__list">
            {areasForImprovement.map((improvement, index) => (
              <div key={index} className="review-form__list-item review-form__list-item--improvement">
                <span className="review-form__list-text">{improvement}</span>
                
                {!isReadOnly && (
                  <button
                    type="button"
                    onClick={() => removeImprovement(index)}
                    className="review-form__remove-btn"
                  >
                    Remove
                  </button>
                )}
              </div>
            ))}
          </div>
          
          {areasForImprovement.length === 0 && (
            <div className="review-form__empty-state">
              No areas for improvement identified yet.
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

interface CommentsSectionProps {
  data: Partial<ReviewFormData>;
  onChange: (field: string, value: any) => void;
  mode: 'create' | 'edit' | 'view';
}

const CommentsSection: React.FC<CommentsSectionProps> = ({ data, onChange, mode }) => {
  const isReadOnly = mode === 'view';
  
  return (
    <div className="review-form__section">
      <h3 className="review-form__section-title">Additional Comments</h3>
      
      <div className="review-form__comments-grid">
        <FormField name="reviewerComments" label="Reviewer Comments">
          <FormField.Textarea
            value={data.reviewerComments || ''}
            onChange={(e) => onChange('reviewerComments', e.target.value)}
            placeholder="Additional comments from the reviewer"
            rows={3}
            disabled={isReadOnly}
          />
        </FormField>
        
        <FormField name="traineeComments" label="Trainee Comments">
          <FormField.Textarea
            value={data.traineeComments || ''}
            onChange={(e) => onChange('traineeComments', e.target.value)}
            placeholder="Comments from the trainee"
            rows={3}
            disabled={isReadOnly}
          />
        </FormField>
        
        <FormField name="hrComments" label="HR Comments">
          <FormField.Textarea
            value={data.hrComments || ''}
            onChange={(e) => onChange('hrComments', e.target.value)}
            placeholder="Comments from HR"
            rows={3}
            disabled={isReadOnly}
          />
        </FormField>
      </div>
    </div>
  );
};

// Main ReviewForm Component
export const ReviewForm: React.FC<ReviewFormProps> = ({
  reviewId,
  traineeId,
  onSubmit,
  onCancel,
  className = '',
  mode = 'create',
  initialData = {}
}) => {
  const [formData, setFormData] = useState<Partial<ReviewFormData>>({
    traineeId: traineeId || '',
    reviewerId: '',
    type: 'quarterly',
    period: '',
    reviewDate: '',
    dueDate: '',
    status: 'draft',
    overallRating: 0,
    summary: '',
    strengths: [],
    areasForImprovement: [],
    goals: [],
    competencyRatings: [],
    feedback: [],
    actionItems: [],
    ...initialData
  });
  
  const [errors, setErrors] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState<string>('basic');
  const { data: existingReview, loading } = useReviews(reviewId ? { id: reviewId } : {});
  
  // Load existing review data
  useEffect(() => {
    if (existingReview && mode === 'edit') {
      setFormData(existingReview);
    }
  }, [existingReview, mode]);
  
  const handleFieldChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear related errors
    setErrors(prev => prev.filter(error => !error.toLowerCase().includes(field.toLowerCase())));
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const validationErrors = validateForm(formData);
    
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }
    
    onSubmit(formData as ReviewFormData);
  };
  
  const isReadOnly = mode === 'view';
  const isLoading = loading && mode === 'edit';
  
  const containerClasses = [
    'review-form',
    `review-form--${mode}`,
    className
  ].filter(Boolean).join(' ');
  
  const tabs = [
    { id: 'basic', label: 'Basic Info' },
    { id: 'ratings', label: 'Ratings' },
    { id: 'feedback', label: 'Feedback' },
    { id: 'comments', label: 'Comments' }
  ];
  
  const renderTabContent = () => {
    switch (activeTab) {
      case 'basic':
        return (
          <BasicInfo
            data={formData}
            onChange={handleFieldChange}
            errors={errors}
            mode={mode}
          />
        );
      
      case 'ratings':
        return (
          <RatingsSection
            data={formData}
            onChange={handleFieldChange}
            errors={errors}
            mode={mode}
          />
        );
      
      case 'feedback':
        return (
          <FeedbackSection
            strengths={formData.strengths || []}
            areasForImprovement={formData.areasForImprovement || []}
            onChange={handleFieldChange}
            mode={mode}
          />
        );
      
      case 'comments':
        return (
          <CommentsSection
            data={formData}
            onChange={handleFieldChange}
            mode={mode}
          />
        );
      
      default:
        return null;
    }
  };
  
  if (isLoading) {
    return (
      <div className="review-form review-form--loading">
        <div className="review-form__skeleton">
          Loading review data...
        </div>
      </div>
    );
  }
  
  return (
    <form className={containerClasses} onSubmit={handleSubmit}>
      <div className="review-form__header">
        <h2 className="review-form__title">
          {mode === 'create' && 'Create New Review'}
          {mode === 'edit' && 'Edit Review'}
          {mode === 'view' && 'Review Details'}
        </h2>
        
        {formData.type && (
          <div className="review-form__subtitle">
            <Badge variant={getStatusColor(formData.status || 'draft') as any}>
              {formData.status?.replace('_', ' ') || 'Draft'}
            </Badge>
            
            <span className="review-form__type">
              {formData.type.replace('_', ' ').replace('-', ' ')}
            </span>
          </div>
        )}
      </div>
      
      {errors.length > 0 && (
        <div className="review-form__errors">
          <h4>Please fix the following errors:</h4>
          <ul>
            {errors.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        </div>
      )}
      
      <div className="review-form__tabs">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            type="button"
            onClick={() => setActiveTab(tab.id)}
            className={`review-form__tab ${
              activeTab === tab.id ? 'review-form__tab--active' : ''
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>
      
      <div className="review-form__content">
        {renderTabContent()}
      </div>
      
      <div className="review-form__actions">
        {onCancel && (
          <button
            type="button"
            onClick={onCancel}
            className="review-form__action-btn review-form__action-btn--cancel"
          >
            Cancel
          </button>
        )}
        
        {!isReadOnly && (
          <button
            type="submit"
            className="review-form__action-btn review-form__action-btn--submit"
          >
            {mode === 'create' ? 'Create Review' : 'Update Review'}
          </button>
        )}
      </div>
    </form>
  );
};

export default ReviewForm;