import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  ChevronDown,
  ChevronUp,
  X,
  Check,
  AlertCircle,
  Camera,
  Upload,
  Calendar,
  Clock,
  MapPin,
} from 'lucide-react';
import { useIsMobile, useIsTouchDevice } from './ResponsiveUtils';

interface MobileFormField {
  id: string;
  type: 'text' | 'email' | 'tel' | 'password' | 'number' | 'textarea' | 'select' | 'multiselect' | 'checkbox' | 'radio' | 'date' | 'time' | 'datetime-local' | 'file' | 'image' | 'location';
  label: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  value?: any;
  options?: { label: string; value: any }[];
  validation?: {
    pattern?: RegExp;
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
    custom?: (value: any) => string | null;
  };
  description?: string;
  section?: string;
  priority?: 'high' | 'medium' | 'low';
  onChange?: (value: any) => void;
}

interface MobileFormSection {
  id: string;
  title: string;
  description?: string;
  collapsible?: boolean;
  defaultExpanded?: boolean;
  fields: string[]; // Field IDs
}

interface MobileFormProps {
  fields: MobileFormField[];
  sections?: MobileFormSection[];
  values: Record<string, any>;
  errors?: Record<string, string>;
  loading?: boolean;
  onSubmit: (values: Record<string, any>) => void;
  onCancel?: () => void;
  onChange?: (fieldId: string, value: any) => void;
  submitLabel?: string;
  cancelLabel?: string;
  className?: string;
  autoSave?: boolean;
  showProgress?: boolean;
}

export const MobileForm: React.FC<MobileFormProps> = ({
  fields,
  sections,
  values,
  errors = {},
  loading = false,
  onSubmit,
  onCancel,
  onChange,
  submitLabel = 'Submit',
  cancelLabel = 'Cancel',
  className,
  autoSave = false,
  showProgress = false,
}) => {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(() => {
    const defaultExpanded = new Set<string>();
    sections?.forEach(section => {
      if (section.defaultExpanded !== false) {
        defaultExpanded.add(section.id);
      }
    });
    return defaultExpanded;
  });
  
  const [focusedField, setFocusedField] = useState<string | null>(null);
  const [touchStartY, setTouchStartY] = useState<number | null>(null);
  const formRef = useRef<HTMLFormElement>(null);
  const isMobile = useIsMobile();
  const isTouch = useIsTouchDevice();

  // Auto-save functionality
  useEffect(() => {
    if (autoSave && onChange) {
      const timeoutId = setTimeout(() => {
        // Trigger auto-save logic here
      }, 1000);
      return () => clearTimeout(timeoutId);
    }
  }, [values, autoSave, onChange]);

  // Handle virtual keyboard on mobile
  useEffect(() => {
    if (!isMobile || !isTouch) return;

    const handleTouchStart = (e: TouchEvent) => {
      setTouchStartY(e.touches[0].clientY);
    };

    const handleTouchMove = (e: TouchEvent) => {
      if (touchStartY === null) return;
      
      const currentY = e.touches[0].clientY;
      const diff = touchStartY - currentY;
      
      // If scrolling up significantly, likely keyboard appeared
      if (diff > 150 && focusedField) {
        const fieldElement = document.getElementById(focusedField);
        if (fieldElement) {
          setTimeout(() => {
            fieldElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
          }, 300);
        }
      }
    };

    document.addEventListener('touchstart', handleTouchStart);
    document.addEventListener('touchmove', handleTouchMove);
    
    return () => {
      document.removeEventListener('touchstart', handleTouchStart);
      document.removeEventListener('touchmove', handleTouchMove);
    };
  }, [isMobile, isTouch, focusedField, touchStartY]);

  const toggleSection = (sectionId: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId);
    } else {
      newExpanded.add(sectionId);
    }
    setExpandedSections(newExpanded);
  };

  const handleFieldChange = (fieldId: string, value: any) => {
    onChange?.(fieldId, value);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(values);
  };

  const getFieldValue = (field: MobileFormField) => {
    return values[field.id] ?? field.value ?? '';
  };

  const getFieldError = (field: MobileFormField) => {
    return errors[field.id];
  };

  const validateField = (field: MobileFormField, value: any): string | null => {
    if (field.required && (!value || (typeof value === 'string' && !value.trim()))) {
      return `${field.label} is required`;
    }

    if (field.validation) {
      const { pattern, minLength, maxLength, min, max, custom } = field.validation;
      
      if (pattern && typeof value === 'string' && !pattern.test(value)) {
        return `${field.label} format is invalid`;
      }
      
      if (minLength && typeof value === 'string' && value.length < minLength) {
        return `${field.label} must be at least ${minLength} characters`;
      }
      
      if (maxLength && typeof value === 'string' && value.length > maxLength) {
        return `${field.label} must be no more than ${maxLength} characters`;
      }
      
      if (min !== undefined && typeof value === 'number' && value < min) {
        return `${field.label} must be at least ${min}`;
      }
      
      if (max !== undefined && typeof value === 'number' && value > max) {
        return `${field.label} must be no more than ${max}`;
      }
      
      if (custom) {
        const customError = custom(value);
        if (customError) return customError;
      }
    }

    return null;
  };

  const renderField = (field: MobileFormField) => {
    const value = getFieldValue(field);
    const error = getFieldError(field);
    const hasError = !!error;
    const isFocused = focusedField === field.id;

    const baseInputClasses = cn(
      'w-full px-4 py-3 border rounded-lg transition-colors',
      'focus:ring-2 focus:ring-blue-500 focus:border-transparent',
      'disabled:bg-gray-50 disabled:text-gray-500',
      hasError ? 'border-red-500' : 'border-gray-300',
      isTouch && 'min-h-[48px] text-base', // Prevent zoom on iOS
      isFocused && 'ring-2 ring-blue-500'
    );

    const renderInput = () => {
      switch (field.type) {
        case 'textarea':
          return (
            <textarea
              id={field.id}
              value={value}
              placeholder={field.placeholder}
              required={field.required}
              disabled={field.disabled}
              rows={4}
              className={cn(baseInputClasses, 'resize-none')}
              onChange={(e) => handleFieldChange(field.id, e.target.value)}
              onFocus={() => setFocusedField(field.id)}
              onBlur={() => setFocusedField(null)}
            />
          );

        case 'select':
          return (
            <select
              id={field.id}
              value={value}
              required={field.required}
              disabled={field.disabled}
              className={baseInputClasses}
              onChange={(e) => handleFieldChange(field.id, e.target.value)}
              onFocus={() => setFocusedField(field.id)}
              onBlur={() => setFocusedField(null)}
            >
              <option value="">{field.placeholder || 'Select an option'}</option>
              {field.options?.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          );

        case 'multiselect':
          return (
            <div className="space-y-2">
              {field.options?.map((option) => {
                const isSelected = Array.isArray(value) && value.includes(option.value);
                return (
                  <label
                    key={option.value}
                    className={cn(
                      'flex items-center p-3 border rounded-lg cursor-pointer transition-colors',
                      isSelected ? 'bg-blue-50 border-blue-500' : 'border-gray-300 hover:bg-gray-50',
                      isTouch && 'min-h-[48px]'
                    )}
                  >
                    <input
                      type="checkbox"
                      checked={isSelected}
                      onChange={(e) => {
                        const currentValue = Array.isArray(value) ? value : [];
                        const newValue = e.target.checked
                          ? [...currentValue, option.value]
                          : currentValue.filter(v => v !== option.value);
                        handleFieldChange(field.id, newValue);
                      }}
                      className="sr-only"
                    />
                    <div className={cn(
                      'w-5 h-5 border-2 rounded mr-3 flex items-center justify-center',
                      isSelected ? 'bg-blue-500 border-blue-500' : 'border-gray-300'
                    )}>
                      {isSelected && <Check className="w-3 h-3 text-white" />}
                    </div>
                    <span className="text-sm">{option.label}</span>
                  </label>
                );
              })}
            </div>
          );

        case 'radio':
          return (
            <div className="space-y-2">
              {field.options?.map((option) => {
                const isSelected = value === option.value;
                return (
                  <label
                    key={option.value}
                    className={cn(
                      'flex items-center p-3 border rounded-lg cursor-pointer transition-colors',
                      isSelected ? 'bg-blue-50 border-blue-500' : 'border-gray-300 hover:bg-gray-50',
                      isTouch && 'min-h-[48px]'
                    )}
                  >
                    <input
                      type="radio"
                      name={field.id}
                      value={option.value}
                      checked={isSelected}
                      onChange={(e) => handleFieldChange(field.id, e.target.value)}
                      className="sr-only"
                    />
                    <div className={cn(
                      'w-5 h-5 border-2 rounded-full mr-3 flex items-center justify-center',
                      isSelected ? 'border-blue-500' : 'border-gray-300'
                    )}>
                      {isSelected && <div className="w-2 h-2 bg-blue-500 rounded-full" />}
                    </div>
                    <span className="text-sm">{option.label}</span>
                  </label>
                );
              })}
            </div>
          );

        case 'checkbox':
          return (
            <label className={cn(
              'flex items-center p-3 border rounded-lg cursor-pointer transition-colors',
              value ? 'bg-blue-50 border-blue-500' : 'border-gray-300 hover:bg-gray-50',
              isTouch && 'min-h-[48px]'
            )}>
              <input
                type="checkbox"
                checked={!!value}
                onChange={(e) => handleFieldChange(field.id, e.target.checked)}
                className="sr-only"
              />
              <div className={cn(
                'w-5 h-5 border-2 rounded mr-3 flex items-center justify-center',
                value ? 'bg-blue-500 border-blue-500' : 'border-gray-300'
              )}>
                {value && <Check className="w-3 h-3 text-white" />}
              </div>
              <span className="text-sm">{field.label}</span>
            </label>
          );

        case 'file':
        case 'image':
          return (
            <div className="space-y-3">
              <input
                type="file"
                id={field.id}
                accept={field.type === 'image' ? 'image/*' : undefined}
                capture={field.type === 'image' && isTouch ? 'environment' : undefined}
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  handleFieldChange(field.id, file);
                }}
                className="sr-only"
              />
              <label
                htmlFor={field.id}
                className={cn(
                  'flex flex-col items-center justify-center p-6 border-2 border-dashed rounded-lg cursor-pointer transition-colors',
                  'hover:bg-gray-50 focus:ring-2 focus:ring-blue-500',
                  hasError ? 'border-red-500' : 'border-gray-300',
                  isTouch && 'min-h-[120px]'
                )}
              >
                {field.type === 'image' ? (
                  <Camera className="w-8 h-8 text-gray-400 mb-2" />
                ) : (
                  <Upload className="w-8 h-8 text-gray-400 mb-2" />
                )}
                <span className="text-sm text-gray-600 text-center">
                  {field.placeholder || `Choose ${field.type === 'image' ? 'image' : 'file'}`}
                </span>
                {value && (
                  <span className="text-xs text-blue-600 mt-1">
                    {value.name || 'File selected'}
                  </span>
                )}
              </label>
            </div>
          );

        case 'date':
        case 'time':
        case 'datetime-local':
          return (
            <div className="relative">
              <input
                type={field.type}
                id={field.id}
                value={value}
                placeholder={field.placeholder}
                required={field.required}
                disabled={field.disabled}
                className={cn(baseInputClasses, 'pr-10')}
                onChange={(e) => handleFieldChange(field.id, e.target.value)}
                onFocus={() => setFocusedField(field.id)}
                onBlur={() => setFocusedField(null)}
              />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                {field.type === 'time' ? (
                  <Clock className="w-5 h-5 text-gray-400" />
                ) : (
                  <Calendar className="w-5 h-5 text-gray-400" />
                )}
              </div>
            </div>
          );

        default:
          return (
            <input
              type={field.type}
              id={field.id}
              value={value}
              placeholder={field.placeholder}
              required={field.required}
              disabled={field.disabled}
              min={field.validation?.min}
              max={field.validation?.max}
              minLength={field.validation?.minLength}
              maxLength={field.validation?.maxLength}
              pattern={field.validation?.pattern?.source}
              className={baseInputClasses}
              onChange={(e) => {
                const newValue = field.type === 'number' ? parseFloat(e.target.value) || 0 : e.target.value;
                handleFieldChange(field.id, newValue);
              }}
              onFocus={() => setFocusedField(field.id)}
              onBlur={() => setFocusedField(null)}
            />
          );
      }
    };

    return (
      <div key={field.id} className="space-y-2">
        {field.type !== 'checkbox' && (
          <label htmlFor={field.id} className="block text-sm font-medium text-gray-700">
            {field.label}
            {field.required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}
        
        {renderInput()}
        
        {field.description && (
          <p className="text-xs text-gray-500">{field.description}</p>
        )}
        
        {error && (
          <div className="flex items-center text-red-600 text-sm">
            <AlertCircle className="w-4 h-4 mr-1" />
            {error}
          </div>
        )}
      </div>
    );
  };

  const renderSection = (section: MobileFormSection) => {
    const isExpanded = expandedSections.has(section.id);
    const sectionFields = fields.filter(field => section.fields.includes(field.id));
    
    return (
      <div key={section.id} className="border border-gray-200 rounded-lg overflow-hidden">
        <button
          type="button"
          onClick={() => section.collapsible && toggleSection(section.id)}
          className={cn(
            'w-full p-4 text-left bg-gray-50 hover:bg-gray-100 transition-colors',
            'flex items-center justify-between',
            !section.collapsible && 'cursor-default hover:bg-gray-50',
            isTouch && 'min-h-[56px]'
          )}
        >
          <div>
            <h3 className="font-medium text-gray-900">{section.title}</h3>
            {section.description && (
              <p className="text-sm text-gray-500 mt-1">{section.description}</p>
            )}
          </div>
          {section.collapsible && (
            <div className="ml-4">
              {isExpanded ? (
                <ChevronUp className="w-5 h-5 text-gray-400" />
              ) : (
                <ChevronDown className="w-5 h-5 text-gray-400" />
              )}
            </div>
          )}
        </button>
        
        {isExpanded && (
          <div className="p-4 space-y-4 bg-white">
            {sectionFields.map(renderField)}
          </div>
        )}
      </div>
    );
  };

  // Calculate form progress
  const completedFields = fields.filter(field => {
    const value = getFieldValue(field);
    return value !== '' && value !== null && value !== undefined;
  }).length;
  const progress = fields.length > 0 ? (completedFields / fields.length) * 100 : 0;

  if (!isMobile) {
    return null; // Use regular form on desktop
  }

  return (
    <div className={cn('w-full max-w-lg mx-auto', className)}>
      {/* Progress indicator */}
      {showProgress && (
        <div className="mb-6">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>Form Progress</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>
      )}

      <form ref={formRef} onSubmit={handleSubmit} className="space-y-6">
        {sections ? (
          <div className="space-y-4">
            {sections.map(renderSection)}
          </div>
        ) : (
          <div className="space-y-4">
            {fields.map(renderField)}
          </div>
        )}

        {/* Form Actions */}
        <div className="flex flex-col space-y-3 pt-6">
          <Button
            type="submit"
            disabled={loading}
            className={cn(
              'w-full',
              isTouch && 'min-h-[48px]'
            )}
          >
            {loading ? 'Submitting...' : submitLabel}
          </Button>
          
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              className={cn(
                'w-full',
                isTouch && 'min-h-[48px]'
              )}
            >
              {cancelLabel}
            </Button>
          )}
        </div>
      </form>
    </div>
  );
};

export default MobileForm;