import React, { useState, useRef } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Users,
  BookOpen,
  ClipboardList,
  TrendingUp,
  TrendingDown,
  Bell,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Calendar,
  Clock,
  CheckCircle,
  AlertTriangle,
  ArrowRight,
  RefreshCw,
} from 'lucide-react';
import { useIsMobile, useIsTouchDevice } from './ResponsiveUtils';

interface DashboardMetric {
  id: string;
  title: string;
  value: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease' | 'neutral';
    period: string;
  };
  icon: React.ComponentType<{ className?: string }>;
  color: 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'gray';
  onClick?: () => void;
}

interface QuickAction {
  id: string;
  title: string;
  description?: string;
  icon: React.ComponentType<{ className?: string }>;
  color: 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'gray';
  onClick: () => void;
}

interface RecentActivity {
  id: string;
  type: 'trainee' | 'program' | 'assessment' | 'review' | 'notification';
  title: string;
  description: string;
  timestamp: Date;
  status?: 'pending' | 'completed' | 'overdue' | 'cancelled';
  onClick?: () => void;
}

interface UpcomingTask {
  id: string;
  title: string;
  description?: string;
  dueDate: Date;
  priority: 'high' | 'medium' | 'low';
  type: 'assessment' | 'review' | 'meeting' | 'deadline';
  onClick?: () => void;
}

interface MobileDashboardProps {
  metrics?: DashboardMetric[];
  quickActions?: QuickAction[];
  recentActivities?: RecentActivity[];
  upcomingTasks?: UpcomingTask[];
  loading?: boolean;
  onRefresh?: () => void;
  className?: string;
}

const defaultMetrics: DashboardMetric[] = [
  {
    id: 'total-trainees',
    title: 'Total Trainees',
    value: 156,
    change: { value: 12, type: 'increase', period: 'this month' },
    icon: Users,
    color: 'blue',
  },
  {
    id: 'active-programs',
    title: 'Active Programs',
    value: 8,
    change: { value: 2, type: 'increase', period: 'this quarter' },
    icon: BookOpen,
    color: 'green',
  },
  {
    id: 'pending-assessments',
    title: 'Pending Assessments',
    value: 23,
    change: { value: 5, type: 'decrease', period: 'this week' },
    icon: ClipboardList,
    color: 'yellow',
  },
  {
    id: 'completion-rate',
    title: 'Completion Rate',
    value: '87%',
    change: { value: 3, type: 'increase', period: 'this month' },
    icon: TrendingUp,
    color: 'purple',
  },
];

const defaultQuickActions: QuickAction[] = [
  {
    id: 'add-trainee',
    title: 'Add Trainee',
    description: 'Register new trainee',
    icon: Users,
    color: 'blue',
    onClick: () => {},
  },
  {
    id: 'create-program',
    title: 'Create Program',
    description: 'Setup new program',
    icon: BookOpen,
    color: 'green',
    onClick: () => {},
  },
  {
    id: 'schedule-assessment',
    title: 'Schedule Assessment',
    description: 'Plan evaluation',
    icon: Calendar,
    color: 'purple',
    onClick: () => {},
  },
  {
    id: 'generate-report',
    title: 'Generate Report',
    description: 'Create analytics',
    icon: TrendingUp,
    color: 'yellow',
    onClick: () => {},
  },
];

const colorClasses = {
  blue: {
    bg: 'bg-blue-50',
    text: 'text-blue-600',
    border: 'border-blue-200',
    icon: 'text-blue-500',
  },
  green: {
    bg: 'bg-green-50',
    text: 'text-green-600',
    border: 'border-green-200',
    icon: 'text-green-500',
  },
  yellow: {
    bg: 'bg-yellow-50',
    text: 'text-yellow-600',
    border: 'border-yellow-200',
    icon: 'text-yellow-500',
  },
  red: {
    bg: 'bg-red-50',
    text: 'text-red-600',
    border: 'border-red-200',
    icon: 'text-red-500',
  },
  purple: {
    bg: 'bg-purple-50',
    text: 'text-purple-600',
    border: 'border-purple-200',
    icon: 'text-purple-500',
  },
  gray: {
    bg: 'bg-gray-50',
    text: 'text-gray-600',
    border: 'border-gray-200',
    icon: 'text-gray-500',
  },
};

export const MobileDashboard: React.FC<MobileDashboardProps> = ({
  metrics = defaultMetrics,
  quickActions = defaultQuickActions,
  recentActivities = [],
  upcomingTasks = [],
  loading = false,
  onRefresh,
  className,
}) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'activities' | 'tasks'>('overview');
  const scrollRef = useRef<HTMLDivElement>(null);
  const isMobile = useIsMobile();
  const isTouch = useIsTouchDevice();

  const formatRelativeTime = (date: Date): string => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    return date.toLocaleDateString();
  };

  const formatDueDate = (date: Date): string => {
    const now = new Date();
    const diff = date.getTime() - now.getTime();
    const days = Math.ceil(diff / 86400000);

    if (days < 0) return 'Overdue';
    if (days === 0) return 'Due today';
    if (days === 1) return 'Due tomorrow';
    if (days < 7) return `Due in ${days} days`;
    return date.toLocaleDateString();
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'overdue':
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'text-red-600 bg-red-50';
      case 'medium':
        return 'text-yellow-600 bg-yellow-50';
      case 'low':
        return 'text-green-600 bg-green-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const renderMetricCard = (metric: DashboardMetric) => {
    const colors = colorClasses[metric.color];
    
    return (
      <button
        key={metric.id}
        onClick={metric.onClick}
        className={cn(
          'p-4 rounded-lg border transition-all duration-200',
          'hover:shadow-md active:scale-95',
          colors.bg,
          colors.border,
          metric.onClick ? 'cursor-pointer' : 'cursor-default',
          isTouch && 'min-h-[100px]'
        )}
      >
        <div className="flex items-start justify-between">
          <div className="flex-1 text-left">
            <p className="text-sm text-gray-600 mb-1">{metric.title}</p>
            <p className={cn('text-2xl font-bold', colors.text)}>{metric.value}</p>
            {metric.change && (
              <div className="flex items-center mt-2">
                {metric.change.type === 'increase' ? (
                  <TrendingUp className="w-3 h-3 text-green-500 mr-1" />
                ) : metric.change.type === 'decrease' ? (
                  <TrendingDown className="w-3 h-3 text-red-500 mr-1" />
                ) : null}
                <span className={cn(
                  'text-xs',
                  metric.change.type === 'increase' ? 'text-green-600' :
                  metric.change.type === 'decrease' ? 'text-red-600' : 'text-gray-600'
                )}>
                  {metric.change.type !== 'neutral' && (
                    <>{metric.change.value > 0 ? '+' : ''}{metric.change.value} </>
                  )}
                  {metric.change.period}
                </span>
              </div>
            )}
          </div>
          <metric.icon className={cn('w-6 h-6', colors.icon)} />
        </div>
      </button>
    );
  };

  const renderQuickAction = (action: QuickAction) => {
    const colors = colorClasses[action.color];
    
    return (
      <button
        key={action.id}
        onClick={action.onClick}
        className={cn(
          'p-4 rounded-lg border transition-all duration-200',
          'hover:shadow-md active:scale-95 text-left',
          colors.bg,
          colors.border,
          isTouch && 'min-h-[80px]'
        )}
      >
        <div className="flex items-center">
          <div className={cn('p-2 rounded-lg mr-3', colors.bg)}>
            <action.icon className={cn('w-5 h-5', colors.icon)} />
          </div>
          <div className="flex-1 min-w-0">
            <p className={cn('font-medium text-sm', colors.text)}>{action.title}</p>
            {action.description && (
              <p className="text-xs text-gray-500 truncate">{action.description}</p>
            )}
          </div>
          <ArrowRight className="w-4 h-4 text-gray-400 ml-2" />
        </div>
      </button>
    );
  };

  const renderActivityItem = (activity: RecentActivity) => (
    <button
      key={activity.id}
      onClick={activity.onClick}
      className={cn(
        'w-full p-3 text-left border-b border-gray-100 hover:bg-gray-50 transition-colors',
        isTouch && 'min-h-[60px]'
      )}
    >
      <div className="flex items-start">
        <div className="mr-3 mt-1">
          {getStatusIcon(activity.status)}
        </div>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-gray-900 truncate">{activity.title}</p>
          <p className="text-xs text-gray-500 truncate">{activity.description}</p>
          <p className="text-xs text-gray-400 mt-1">{formatRelativeTime(activity.timestamp)}</p>
        </div>
        {activity.status && (
          <Badge
            variant={activity.status === 'completed' ? 'default' : 
                    activity.status === 'overdue' ? 'destructive' : 'secondary'}
            className="text-xs"
          >
            {activity.status}
          </Badge>
        )}
      </div>
    </button>
  );

  const renderTaskItem = (task: UpcomingTask) => (
    <button
      key={task.id}
      onClick={task.onClick}
      className={cn(
        'w-full p-3 text-left border-b border-gray-100 hover:bg-gray-50 transition-colors',
        isTouch && 'min-h-[60px]'
      )}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center mb-1">
            <p className="text-sm font-medium text-gray-900 truncate">{task.title}</p>
            <Badge className={cn('ml-2 text-xs', getPriorityColor(task.priority))}>
              {task.priority}
            </Badge>
          </div>
          {task.description && (
            <p className="text-xs text-gray-500 truncate mb-1">{task.description}</p>
          )}
          <p className="text-xs text-gray-400">{formatDueDate(task.dueDate)}</p>
        </div>
        <Calendar className="w-4 h-4 text-gray-400 ml-2 mt-1" />
      </div>
    </button>
  );

  if (!isMobile) {
    return null; // Use regular dashboard on desktop
  }

  return (
    <div className={cn('w-full', className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 bg-white border-b border-gray-200">
        <div>
          <h1 className="text-xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-sm text-gray-500">Graduate Trainee Tracker</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={onRefresh}
            disabled={loading}
            className={cn(isTouch && 'min-h-[44px] min-w-[44px]')}
          >
            <RefreshCw className={cn('w-5 h-5', loading && 'animate-spin')} />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className={cn(isTouch && 'min-h-[44px] min-w-[44px]')}
          >
            <Bell className="w-5 h-5" />
          </Button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex bg-white border-b border-gray-200">
        {[
          { id: 'overview', label: 'Overview' },
          { id: 'activities', label: 'Activities' },
          { id: 'tasks', label: 'Tasks' },
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={cn(
              'flex-1 py-3 px-4 text-sm font-medium transition-colors',
              'border-b-2 border-transparent',
              activeTab === tab.id
                ? 'text-blue-600 border-blue-600 bg-blue-50'
                : 'text-gray-500 hover:text-gray-700',
              isTouch && 'min-h-[48px]'
            )}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="p-4">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Metrics Grid */}
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-3">Key Metrics</h2>
              <div className="grid grid-cols-2 gap-3">
                {metrics.map(renderMetricCard)}
              </div>
            </div>

            {/* Quick Actions */}
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-3">Quick Actions</h2>
              <div className="grid grid-cols-1 gap-3">
                {quickActions.map(renderQuickAction)}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'activities' && (
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-3">Recent Activities</h2>
            {recentActivities.length > 0 ? (
              <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                {recentActivities.map(renderActivityItem)}
              </div>
            ) : (
              <div className="text-center py-12">
                <Clock className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500">No recent activities</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'tasks' && (
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-3">Upcoming Tasks</h2>
            {upcomingTasks.length > 0 ? (
              <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                {upcomingTasks.map(renderTaskItem)}
              </div>
            ) : (
              <div className="text-center py-12">
                <Calendar className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500">No upcoming tasks</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default MobileDashboard;