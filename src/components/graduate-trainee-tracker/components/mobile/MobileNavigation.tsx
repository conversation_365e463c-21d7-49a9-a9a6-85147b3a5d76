import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Menu,
  X,
  Home,
  Users,
  BookOpen,
  ClipboardList,
  BarChart3,
  Bell,
  Settings,
  ChevronRight,
  Search,
} from 'lucide-react';
import { useIsMobile, useIsTouchDevice } from './ResponsiveUtils';

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  href?: string;
  onClick?: () => void;
  badge?: number;
  children?: NavigationItem[];
}

interface MobileNavigationProps {
  items?: NavigationItem[];
  currentPath?: string;
  onNavigate?: (path: string) => void;
  className?: string;
}

const defaultNavigationItems: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: Home,
    href: '/dashboard',
  },
  {
    id: 'trainees',
    label: 'Trainees',
    icon: Users,
    href: '/trainees',
    children: [
      { id: 'trainees-list', label: 'All Trainees', icon: Users, href: '/trainees' },
      { id: 'trainees-add', label: 'Add Trainee', icon: Users, href: '/trainees/add' },
    ],
  },
  {
    id: 'programs',
    label: 'Programs',
    icon: BookOpen,
    href: '/programs',
    children: [
      { id: 'programs-list', label: 'All Programs', icon: BookOpen, href: '/programs' },
      { id: 'programs-add', label: 'Create Program', icon: BookOpen, href: '/programs/add' },
    ],
  },
  {
    id: 'assessments',
    label: 'Assessments',
    icon: ClipboardList,
    href: '/assessments',
  },
  {
    id: 'reports',
    label: 'Reports',
    icon: BarChart3,
    href: '/reports',
  },
  {
    id: 'notifications',
    label: 'Notifications',
    icon: Bell,
    href: '/notifications',
    badge: 3,
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: Settings,
    href: '/settings',
  },
];

export const MobileNavigation: React.FC<MobileNavigationProps> = ({
  items = defaultNavigationItems,
  currentPath = '',
  onNavigate,
  className,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');
  const isMobile = useIsMobile();
  const isTouch = useIsTouchDevice();

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (isOpen && !target.closest('.mobile-nav-container')) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen]);

  // Close menu on route change
  useEffect(() => {
    setIsOpen(false);
  }, [currentPath]);

  const toggleExpanded = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  const handleItemClick = (item: NavigationItem) => {
    if (item.children && item.children.length > 0) {
      toggleExpanded(item.id);
    } else if (item.href) {
      onNavigate?.(item.href);
    } else if (item.onClick) {
      item.onClick();
    }
  };

  const filteredItems = items.filter(item =>
    item.label.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const isItemActive = (item: NavigationItem): boolean => {
    if (item.href === currentPath) return true;
    if (item.children) {
      return item.children.some(child => child.href === currentPath);
    }
    return false;
  };

  const renderNavigationItem = (item: NavigationItem, level = 0) => {
    const isActive = isItemActive(item);
    const isExpanded = expandedItems.has(item.id);
    const hasChildren = item.children && item.children.length > 0;

    return (
      <div key={item.id} className="w-full">
        <button
          onClick={() => handleItemClick(item)}
          className={cn(
            'w-full flex items-center justify-between p-3 rounded-lg text-left transition-colors',
            'hover:bg-gray-100 active:bg-gray-200',
            isActive && 'bg-blue-50 text-blue-600 border-l-4 border-blue-600',
            level > 0 && 'ml-4 text-sm',
            isTouch && 'min-h-[48px]' // Ensure touch targets are at least 48px
          )}
        >
          <div className="flex items-center space-x-3">
            <item.icon className={cn('h-5 w-5', isActive && 'text-blue-600')} />
            <span className={cn('font-medium', isActive && 'text-blue-600')}>
              {item.label}
            </span>
            {item.badge && item.badge > 0 && (
              <Badge variant="destructive" className="ml-2">
                {item.badge > 99 ? '99+' : item.badge}
              </Badge>
            )}
          </div>
          {hasChildren && (
            <ChevronRight
              className={cn(
                'h-4 w-4 transition-transform',
                isExpanded && 'rotate-90'
              )}
            />
          )}
        </button>

        {hasChildren && isExpanded && (
          <div className="mt-1 space-y-1">
            {item.children!.map(child => renderNavigationItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  if (!isMobile) {
    return null; // Don't render on desktop
  }

  return (
    <>
      {/* Menu Toggle Button */}
      <Button
        variant="ghost"
        size="icon"
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          'fixed top-4 left-4 z-50 bg-white shadow-lg border',
          'hover:bg-gray-50 active:bg-gray-100',
          isTouch && 'min-h-[48px] min-w-[48px]'
        )}
        aria-label="Toggle navigation menu"
      >
        {isOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
      </Button>

      {/* Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Navigation Drawer */}
      <div
        className={cn(
          'mobile-nav-container fixed top-0 left-0 h-full w-80 max-w-[85vw] bg-white shadow-xl z-50',
          'transform transition-transform duration-300 ease-in-out',
          'flex flex-col',
          isOpen ? 'translate-x-0' : '-translate-x-full',
          className
        )}
      >
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">
              Graduate Tracker
            </h2>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsOpen(false)}
              className={cn(
                'hover:bg-gray-100',
                isTouch && 'min-h-[48px] min-w-[48px]'
              )}
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search navigation..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className={cn(
                'w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg',
                'focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder-gray-400 text-sm',
                isTouch && 'min-h-[48px]'
              )}
            />
          </div>
        </div>

        {/* Navigation Items */}
        <div className="flex-1 overflow-y-auto p-4">
          <nav className="space-y-2">
            {filteredItems.length > 0 ? (
              filteredItems.map(item => renderNavigationItem(item))
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No navigation items found</p>
              </div>
            )}
          </nav>
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200">
          <div className="text-xs text-gray-500 text-center">
            Graduate Trainee Tracker v1.0
          </div>
        </div>
      </div>
    </>
  );
};

export default MobileNavigation;