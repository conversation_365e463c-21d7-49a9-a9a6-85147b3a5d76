import React, { ReactNode } from 'react';
import { z } from 'zod';
import { useForm, UseFormOptions } from './useForm';
import { FormField } from '../ui/FormField';
import { DatePicker } from '../ui/DatePicker';
import { MultiSelect } from '../ui/MultiSelect';
import { FileUpload } from '../ui/FileUpload';
import { Button } from '@/components/ui/button';

// Field configuration types
export interface BaseFieldConfig {
  name: string;
  label: string;
  placeholder?: string;
  description?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
}

export interface TextFieldConfig extends BaseFieldConfig {
  type: 'text' | 'email' | 'password' | 'url' | 'tel';
  maxLength?: number;
  minLength?: number;
}

export interface NumberFieldConfig extends BaseFieldConfig {
  type: 'number';
  min?: number;
  max?: number;
  step?: number;
}

export interface TextareaFieldConfig extends BaseFieldConfig {
  type: 'textarea';
  rows?: number;
  maxLength?: number;
}

export interface SelectFieldConfig extends BaseFieldConfig {
  type: 'select';
  options: Array<{ value: string | number; label: string; disabled?: boolean }>;
  multiple?: boolean;
}

export interface CheckboxFieldConfig extends BaseFieldConfig {
  type: 'checkbox';
  checkboxLabel?: string;
}

export interface RadioFieldConfig extends BaseFieldConfig {
  type: 'radio';
  options: Array<{ value: string | number; label: string; disabled?: boolean }>;
}

export interface DateFieldConfig extends BaseFieldConfig {
  type: 'date' | 'datetime';
  minDate?: Date;
  maxDate?: Date;
  showTime?: boolean;
}

export interface FileFieldConfig extends BaseFieldConfig {
  type: 'file';
  accept?: string;
  multiple?: boolean;
  maxSize?: number;
  maxFiles?: number;
}

export interface MultiSelectFieldConfig extends BaseFieldConfig {
  type: 'multiselect';
  options: Array<{ value: string | number; label: string; disabled?: boolean }>;
  searchable?: boolean;
  creatable?: boolean;
}

export interface CustomFieldConfig extends BaseFieldConfig {
  type: 'custom';
  render: (props: {
    value: any;
    onChange: (value: any) => void;
    onBlur: () => void;
    error?: string;
    hasError: boolean;
    disabled?: boolean;
  }) => ReactNode;
}

export type FieldConfig = 
  | TextFieldConfig
  | NumberFieldConfig
  | TextareaFieldConfig
  | SelectFieldConfig
  | CheckboxFieldConfig
  | RadioFieldConfig
  | DateFieldConfig
  | FileFieldConfig
  | MultiSelectFieldConfig
  | CustomFieldConfig;

// Section configuration
export interface FormSection {
  title?: string;
  description?: string;
  fields: FieldConfig[];
  className?: string;
  collapsible?: boolean;
  defaultCollapsed?: boolean;
}

// Form builder props
export interface FormBuilderProps<T> extends Omit<UseFormOptions<T>, 'validationSchema'> {
  schema: z.ZodSchema<T>;
  sections: FormSection[];
  submitLabel?: string;
  cancelLabel?: string;
  showCancel?: boolean;
  onCancel?: () => void;
  className?: string;
  loading?: boolean;
  disabled?: boolean;
  children?: ReactNode;
}

// Form builder component
export const FormBuilder = <T extends Record<string, any>>({
  schema,
  sections,
  submitLabel = 'Submit',
  cancelLabel = 'Cancel',
  showCancel = false,
  onCancel,
  className = '',
  loading = false,
  disabled = false,
  children,
  ...formOptions
}: FormBuilderProps<T>) => {
  const form = useForm<T>({
    ...formOptions,
    validationSchema: schema
  });

  const renderField = (field: FieldConfig) => {
    const fieldProps = form.getFieldProps(field.name as keyof T);
    const commonProps = {
      label: field.label,
      placeholder: field.placeholder,
      description: field.description,
      required: field.required,
      disabled: field.disabled || disabled || form.isSubmitting,
      className: field.className,
      error: fieldProps.error,
      hasError: fieldProps.hasError
    };

    switch (field.type) {
      case 'text':
      case 'email':
      case 'password':
      case 'url':
      case 'tel':
        return (
          <FormField
            key={field.name}
            {...commonProps}
            type={field.type}
            name={fieldProps.name}
            value={fieldProps.value}
            onChange={fieldProps.onChange}
            onBlur={fieldProps.onBlur}
            maxLength={field.maxLength}
          />
        );

      case 'number':
        return (
          <FormField
            key={field.name}
            {...commonProps}
            type="number"
            name={fieldProps.name}
            value={fieldProps.value}
            onChange={fieldProps.onChange}
            onBlur={fieldProps.onBlur}
            min={field.min}
            max={field.max}
            step={field.step}
          />
        );

      case 'textarea':
        return (
          <FormField
            key={field.name}
            {...commonProps}
            type="textarea"
            name={fieldProps.name}
            value={fieldProps.value}
            onChange={fieldProps.onChange}
            onBlur={fieldProps.onBlur}
            rows={field.rows}
            maxLength={field.maxLength}
          />
        );

      case 'select':
        if (field.multiple) {
          const selectProps = form.getSelectProps(field.name as keyof T);
          return (
            <MultiSelect
              key={field.name}
              {...commonProps}
              name={selectProps.name}
              value={selectProps.value || []}
              onChange={selectProps.onChange}
              onBlur={selectProps.onBlur}
              options={field.options}
            />
          );
        }
        return (
          <FormField
            key={field.name}
            {...commonProps}
            name={fieldProps.name}
            value={fieldProps.value}
            onChange={fieldProps.onChange}
            onBlur={fieldProps.onBlur}
            options={field.options}
          />
        );

      case 'checkbox':
        const checkboxProps = form.getCheckboxProps(field.name as keyof T);
        return (
          <FormField
            key={field.name}
            {...commonProps}
            name={checkboxProps.name}
            checked={checkboxProps.checked}
            onChange={checkboxProps.onChange}
            onBlur={checkboxProps.onBlur}
            checkboxLabel={field.checkboxLabel}
          />
        );

      case 'radio':
        return (
          <FormField
            key={field.name}
            {...commonProps}
            name={fieldProps.name}
            value={fieldProps.value}
            onChange={fieldProps.onChange}
            onBlur={fieldProps.onBlur}
            options={field.options}
          />
        );

      case 'date':
      case 'datetime':
        const dateProps = form.getSelectProps(field.name as keyof T);
        return (
          <DatePicker
            key={field.name}
            {...commonProps}
            name={dateProps.name}
            value={dateProps.value}
            onChange={dateProps.onChange}
            onBlur={dateProps.onBlur}
            minDate={field.minDate}
            maxDate={field.maxDate}
            showTime={field.showTime || field.type === 'datetime'}
          />
        );

      case 'file':
        const fileProps = form.getSelectProps(field.name as keyof T);
        return (
          <FileUpload
            key={field.name}
            {...commonProps}
            name={fileProps.name}
            value={fileProps.value}
            onChange={fileProps.onChange}
            onBlur={fileProps.onBlur}
            accept={field.accept}
            multiple={field.multiple}
            maxSize={field.maxSize}
            maxFiles={field.maxFiles}
          />
        );

      case 'multiselect':
        const multiselectProps = form.getSelectProps(field.name as keyof T);
        return (
          <MultiSelect
            key={field.name}
            {...commonProps}
            name={multiselectProps.name}
            value={multiselectProps.value || []}
            onChange={multiselectProps.onChange}
            onBlur={multiselectProps.onBlur}
            options={field.options}
            searchable={field.searchable}
            creatable={field.creatable}
          />
        );

      case 'custom':
        const customProps = form.getSelectProps(field.name as keyof T);
        return (
          <div key={field.name} className={field.className}>
            {field.label && (
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {field.label}
                {field.required && <span className="text-red-500 ml-1">*</span>}
              </label>
            )}
            {field.render({
              value: customProps.value,
              onChange: customProps.onChange,
              onBlur: customProps.onBlur,
              error: customProps.error,
              hasError: customProps.hasError,
              disabled: field.disabled || disabled || form.isSubmitting
            })}
            {field.description && (
              <p className="text-sm text-gray-500 mt-1">{field.description}</p>
            )}
            {customProps.error && (
              <p className="text-sm text-red-600 mt-1">{customProps.error}</p>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  const renderSection = (section: FormSection, index: number) => {
    const [collapsed, setCollapsed] = React.useState(section.defaultCollapsed || false);

    return (
      <div key={index} className={`space-y-4 ${section.className || ''}`}>
        {section.title && (
          <div className="border-b border-gray-200 pb-2">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">
                {section.title}
              </h3>
              {section.collapsible && (
                <button
                  type="button"
                  onClick={() => setCollapsed(!collapsed)}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  {collapsed ? 'Expand' : 'Collapse'}
                </button>
              )}
            </div>
            {section.description && (
              <p className="text-sm text-gray-600 mt-1">{section.description}</p>
            )}
          </div>
        )}
        
        {(!section.collapsible || !collapsed) && (
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            {section.fields.map(renderField)}
          </div>
        )}
      </div>
    );
  };

  return (
    <form onSubmit={form.handleSubmit} className={`space-y-6 ${className}`}>
      {sections.map(renderSection)}
      
      {children}
      
      <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
        {showCancel && (
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={form.isSubmitting}
          >
            {cancelLabel}
          </Button>
        )}
        <Button
          type="submit"
          variant="primary"
          loading={loading || form.isSubmitting}
          disabled={disabled || !form.isValid}
        >
          {submitLabel}
        </Button>
      </div>
    </form>
  );
};

export default FormBuilder;