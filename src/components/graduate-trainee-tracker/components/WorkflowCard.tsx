import React, { useState, useMemo } from 'react';
import { Badge, ProgressBar, Avatar } from './ui';

// Types
export interface WorkflowCardProps {
  workflowId: string;
  className?: string;
  variant?: 'default' | 'compact' | 'detailed';
  showActions?: boolean;
  onView?: (workflowId: string) => void;
  onEdit?: (workflowId: string) => void;
  onDelete?: (workflowId: string) => void;
  onStart?: (workflowId: string) => void;
  onPause?: (workflowId: string) => void;
  onResume?: (workflowId: string) => void;
  onComplete?: (workflowId: string) => void;
}

export interface Workflow {
  id: string;
  name: string;
  description: string;
  type: 'onboarding' | 'training' | 'assessment' | 'review' | 'approval' | 'custom';
  category: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'draft' | 'active' | 'paused' | 'completed' | 'cancelled' | 'archived';
  progress: number; // 0-100
  totalSteps: number;
  completedSteps: number;
  currentStep?: WorkflowStep;
  steps: WorkflowStep[];
  assignee?: string;
  assigneeName?: string;
  assigneeAvatar?: string;
  dueDate?: string;
  startedAt?: string;
  completedAt?: string;
  estimatedDuration: number; // in hours
  actualDuration?: number; // in hours
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  tags: string[];
  dependencies?: string[];
  notifications: WorkflowNotification[];
}

export interface WorkflowStep {
  id: string;
  name: string;
  description: string;
  type: 'task' | 'approval' | 'review' | 'assessment' | 'document' | 'meeting';
  status: 'pending' | 'in_progress' | 'completed' | 'skipped' | 'failed';
  order: number;
  assignee?: string;
  assigneeName?: string;
  dueDate?: string;
  completedAt?: string;
  estimatedHours: number;
  actualHours?: number;
  requirements?: string[];
  deliverables?: string[];
  notes?: string;
  attachments?: WorkflowAttachment[];
}

export interface WorkflowNotification {
  id: string;
  type: 'reminder' | 'overdue' | 'completed' | 'assigned' | 'escalation';
  message: string;
  createdAt: string;
  read: boolean;
}

export interface WorkflowAttachment {
  id: string;
  name: string;
  type: string;
  url: string;
  uploadedAt: string;
  uploadedBy: string;
}

export interface WorkflowStats {
  totalWorkflows: number;
  activeWorkflows: number;
  completedWorkflows: number;
  averageCompletionTime: number;
  onTimeCompletionRate: number;
  overdueWorkflows: number;
}

// Utility functions
const formatDuration = (hours: number): string => {
  if (hours < 1) {
    return `${Math.round(hours * 60)}m`;
  }
  if (hours < 24) {
    return `${Math.round(hours)}h`;
  }
  const days = Math.floor(hours / 24);
  const remainingHours = Math.round(hours % 24);
  return remainingHours > 0 ? `${days}d ${remainingHours}h` : `${days}d`;
};

const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const formatDateTime = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const isOverdue = (dueDate?: string): boolean => {
  if (!dueDate) return false;
  return new Date(dueDate) < new Date();
};

const getDaysUntilDue = (dueDate?: string): number => {
  if (!dueDate) return 0;
  const due = new Date(dueDate);
  const now = new Date();
  const diffTime = due.getTime() - now.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

const getPriorityColor = (priority: string): string => {
  const colors = {
    low: 'info',
    medium: 'warning',
    high: 'error',
    urgent: 'error'
  };
  return colors[priority as keyof typeof colors] || 'default';
};

const getStatusColor = (status: string): string => {
  const colors = {
    draft: 'default',
    active: 'info',
    paused: 'warning',
    completed: 'success',
    cancelled: 'error',
    archived: 'default',
    pending: 'default',
    in_progress: 'info',
    skipped: 'warning',
    failed: 'error'
  };
  return colors[status as keyof typeof colors] || 'default';
};

const getTypeColor = (type: string): string => {
  const colors = {
    onboarding: 'success',
    training: 'info',
    assessment: 'warning',
    review: 'info',
    approval: 'warning',
    custom: 'default',
    task: 'info',
    document: 'default',
    meeting: 'warning'
  };
  return colors[type as keyof typeof colors] || 'default';
};

const getProgressColor = (progress: number, isOverdue: boolean): string => {
  if (isOverdue) return 'error';
  if (progress >= 90) return 'success';
  if (progress >= 70) return 'info';
  if (progress >= 50) return 'warning';
  return 'error';
};

// Sub-components
interface WorkflowHeaderProps {
  workflow: Workflow;
  variant: 'default' | 'compact' | 'detailed';
}

const WorkflowHeader: React.FC<WorkflowHeaderProps> = ({ workflow, variant }) => {
  const overdue = isOverdue(workflow.dueDate);
  const daysUntilDue = getDaysUntilDue(workflow.dueDate);
  
  return (
    <div className="workflow-card__header">
      <div className="workflow-card__title-section">
        <h3 className="workflow-card__title">{workflow.name}</h3>
        
        <div className="workflow-card__badges">
          <Badge variant={getTypeColor(workflow.type) as any} size="small">
            {workflow.type}
          </Badge>
          
          <Badge variant={getPriorityColor(workflow.priority) as any} size="small">
            {workflow.priority}
          </Badge>
          
          <Badge variant={getStatusColor(workflow.status) as any} size="small">
            {workflow.status}
          </Badge>
          
          {overdue && (
            <Badge variant="error" size="small">
              Overdue
            </Badge>
          )}
        </div>
      </div>
      
      {variant !== 'compact' && (
        <div className="workflow-card__meta">
          <span className="workflow-card__category">{workflow.category}</span>
          
          {workflow.dueDate && (
            <span className={`workflow-card__due-date ${
              overdue ? 'workflow-card__due-date--overdue' : ''
            }`}>
              {overdue ? 'Overdue by' : 'Due in'} {Math.abs(daysUntilDue)} days
            </span>
          )}
          
          <span className="workflow-card__duration">
            Est: {formatDuration(workflow.estimatedDuration)}
          </span>
        </div>
      )}
    </div>
  );
};

interface WorkflowProgressProps {
  workflow: Workflow;
  variant: 'default' | 'compact' | 'detailed';
}

const WorkflowProgress: React.FC<WorkflowProgressProps> = ({ workflow, variant }) => {
  const overdue = isOverdue(workflow.dueDate);
  const progressColor = getProgressColor(workflow.progress, overdue);
  
  return (
    <div className="workflow-card__progress">
      <div className="workflow-card__progress-header">
        <span className="workflow-card__progress-label">
          Progress: {workflow.completedSteps} of {workflow.totalSteps} steps
        </span>
        <span className="workflow-card__progress-percentage">
          {workflow.progress}%
        </span>
      </div>
      
      <ProgressBar
        value={workflow.progress}
        max={100}
        color={progressColor}
        size={variant === 'compact' ? 'small' : 'medium'}
        label={variant !== 'compact' ? `${workflow.progress}%` : undefined}
      />
      
      {variant === 'detailed' && workflow.currentStep && (
        <div className="workflow-card__current-step">
          <span className="workflow-card__current-step-label">Current Step:</span>
          <div className="workflow-card__current-step-info">
            <span className="workflow-card__current-step-name">
              {workflow.currentStep.name}
            </span>
            <Badge variant={getStatusColor(workflow.currentStep.status) as any} size="small">
              {workflow.currentStep.status.replace('_', ' ')}
            </Badge>
          </div>
        </div>
      )}
    </div>
  );
};

interface WorkflowAssigneeProps {
  workflow: Workflow;
  variant: 'default' | 'compact' | 'detailed';
}

const WorkflowAssignee: React.FC<WorkflowAssigneeProps> = ({ workflow, variant }) => {
  if (!workflow.assignee || variant === 'compact') return null;
  
  return (
    <div className="workflow-card__assignee">
      <span className="workflow-card__assignee-label">Assigned to:</span>
      <div className="workflow-card__assignee-info">
        <Avatar
          src={workflow.assigneeAvatar}
          alt={workflow.assigneeName || 'Assignee'}
          size="small"
          fallback={workflow.assigneeName?.charAt(0) || 'A'}
        />
        <span className="workflow-card__assignee-name">
          {workflow.assigneeName || workflow.assignee}
        </span>
      </div>
    </div>
  );
};

interface WorkflowStepsProps {
  steps: WorkflowStep[];
  variant: 'default' | 'compact' | 'detailed';
}

const WorkflowSteps: React.FC<WorkflowStepsProps> = ({ steps, variant }) => {
  if (variant !== 'detailed') return null;
  
  const visibleSteps = steps.slice(0, 5); // Show first 5 steps
  const hasMoreSteps = steps.length > 5;
  
  return (
    <div className="workflow-card__steps">
      <h4 className="workflow-card__steps-title">Steps</h4>
      
      <div className="workflow-card__steps-list">
        {visibleSteps.map((step) => {
          const stepOverdue = isOverdue(step.dueDate);
          
          return (
            <div key={step.id} className="workflow-card__step">
              <div className="workflow-card__step-header">
                <span className="workflow-card__step-order">{step.order}</span>
                <span className="workflow-card__step-name">{step.name}</span>
                
                <div className="workflow-card__step-badges">
                  <Badge variant={getTypeColor(step.type) as any} size="small">
                    {step.type}
                  </Badge>
                  
                  <Badge variant={getStatusColor(step.status) as any} size="small">
                    {step.status.replace('_', ' ')}
                  </Badge>
                  
                  {stepOverdue && (
                    <Badge variant="error" size="small">
                      Overdue
                    </Badge>
                  )}
                </div>
              </div>
              
              {step.description && (
                <div className="workflow-card__step-description">
                  {step.description}
                </div>
              )}
              
              <div className="workflow-card__step-meta">
                {step.assigneeName && (
                  <span className="workflow-card__step-assignee">
                    Assigned to: {step.assigneeName}
                  </span>
                )}
                
                {step.dueDate && (
                  <span className={`workflow-card__step-due ${
                    stepOverdue ? 'workflow-card__step-due--overdue' : ''
                  }`}>
                    Due: {formatDate(step.dueDate)}
                  </span>
                )}
                
                <span className="workflow-card__step-duration">
                  Est: {formatDuration(step.estimatedHours)}
                  {step.actualHours && (
                    <span> | Actual: {formatDuration(step.actualHours)}</span>
                  )}
                </span>
              </div>
            </div>
          );
        })}
      </div>
      
      {hasMoreSteps && (
        <div className="workflow-card__steps-more">
          +{steps.length - 5} more steps
        </div>
      )}
    </div>
  );
};

interface WorkflowActionsProps {
  workflow: Workflow;
  showActions: boolean;
  onView?: (workflowId: string) => void;
  onEdit?: (workflowId: string) => void;
  onDelete?: (workflowId: string) => void;
  onStart?: (workflowId: string) => void;
  onPause?: (workflowId: string) => void;
  onResume?: (workflowId: string) => void;
  onComplete?: (workflowId: string) => void;
}

const WorkflowActions: React.FC<WorkflowActionsProps> = ({
  workflow,
  showActions,
  onView,
  onEdit,
  onDelete,
  onStart,
  onPause,
  onResume,
  onComplete
}) => {
  if (!showActions) return null;
  
  const canStart = workflow.status === 'draft';
  const canPause = workflow.status === 'active';
  const canResume = workflow.status === 'paused';
  const canComplete = workflow.status === 'active' && workflow.progress === 100;
  
  return (
    <div className="workflow-card__actions">
      {onView && (
        <button
          onClick={() => onView(workflow.id)}
          className="workflow-card__action-btn workflow-card__action-btn--view"
        >
          View Details
        </button>
      )}
      
      {canStart && onStart && (
        <button
          onClick={() => onStart(workflow.id)}
          className="workflow-card__action-btn workflow-card__action-btn--start"
        >
          Start Workflow
        </button>
      )}
      
      {canPause && onPause && (
        <button
          onClick={() => onPause(workflow.id)}
          className="workflow-card__action-btn workflow-card__action-btn--pause"
        >
          Pause
        </button>
      )}
      
      {canResume && onResume && (
        <button
          onClick={() => onResume(workflow.id)}
          className="workflow-card__action-btn workflow-card__action-btn--resume"
        >
          Resume
        </button>
      )}
      
      {canComplete && onComplete && (
        <button
          onClick={() => onComplete(workflow.id)}
          className="workflow-card__action-btn workflow-card__action-btn--complete"
        >
          Mark Complete
        </button>
      )}
      
      {onEdit && (
        <button
          onClick={() => onEdit(workflow.id)}
          className="workflow-card__action-btn workflow-card__action-btn--edit"
        >
          Edit
        </button>
      )}
      
      {onDelete && workflow.status !== 'active' && (
        <button
          onClick={() => onDelete(workflow.id)}
          className="workflow-card__action-btn workflow-card__action-btn--delete"
        >
          Delete
        </button>
      )}
    </div>
  );
};

// Main WorkflowCard Component
export const WorkflowCard: React.FC<WorkflowCardProps> = ({
  workflowId,
  className = '',
  variant = 'default',
  showActions = true,
  onView,
  onEdit,
  onDelete,
  onStart,
  onPause,
  onResume,
  onComplete
}) => {
  const [showAllSteps, setShowAllSteps] = useState(false);
  
  // Mock data - in real implementation, this would come from a hook
  const mockWorkflow: Workflow = {
    id: workflowId,
    name: 'New Trainee Onboarding',
    description: 'Complete onboarding process for new graduate trainees including documentation, training, and initial assessments.',
    type: 'onboarding',
    category: 'HR Process',
    priority: 'high',
    status: 'active',
    progress: 65,
    totalSteps: 8,
    completedSteps: 5,
    currentStep: {
      id: 'step-6',
      name: 'Complete Initial Assessment',
      description: 'Take the initial technical assessment to evaluate current skill level.',
      type: 'assessment',
      status: 'in_progress',
      order: 6,
      assignee: 'trainee-1',
      assigneeName: 'John Doe',
      dueDate: '2024-02-15T17:00:00Z',
      estimatedHours: 2,
      requirements: ['Complete training modules 1-3'],
      deliverables: ['Assessment results', 'Skill evaluation report']
    },
    steps: [
      {
        id: 'step-1',
        name: 'Welcome & Introduction',
        description: 'Welcome session and company introduction.',
        type: 'meeting',
        status: 'completed',
        order: 1,
        assignee: 'hr-1',
        assigneeName: 'HR Manager',
        completedAt: '2024-01-15T10:00:00Z',
        estimatedHours: 1,
        actualHours: 1.5
      },
      {
        id: 'step-2',
        name: 'Documentation Setup',
        description: 'Complete all required documentation and forms.',
        type: 'document',
        status: 'completed',
        order: 2,
        assignee: 'trainee-1',
        assigneeName: 'John Doe',
        completedAt: '2024-01-16T14:30:00Z',
        estimatedHours: 2,
        actualHours: 1.5
      },
      {
        id: 'step-3',
        name: 'IT Setup',
        description: 'Set up computer, accounts, and access permissions.',
        type: 'task',
        status: 'completed',
        order: 3,
        assignee: 'it-1',
        assigneeName: 'IT Support',
        completedAt: '2024-01-17T11:00:00Z',
        estimatedHours: 3,
        actualHours: 2.5
      },
      {
        id: 'step-4',
        name: 'Training Module 1',
        description: 'Complete first training module on company policies.',
        type: 'task',
        status: 'completed',
        order: 4,
        assignee: 'trainee-1',
        assigneeName: 'John Doe',
        completedAt: '2024-01-20T16:00:00Z',
        estimatedHours: 4,
        actualHours: 4
      },
      {
        id: 'step-5',
        name: 'Training Module 2',
        description: 'Complete second training module on technical skills.',
        type: 'task',
        status: 'completed',
        order: 5,
        assignee: 'trainee-1',
        assigneeName: 'John Doe',
        completedAt: '2024-01-25T15:30:00Z',
        estimatedHours: 6,
        actualHours: 5.5
      },
      {
        id: 'step-6',
        name: 'Complete Initial Assessment',
        description: 'Take the initial technical assessment to evaluate current skill level.',
        type: 'assessment',
        status: 'in_progress',
        order: 6,
        assignee: 'trainee-1',
        assigneeName: 'John Doe',
        dueDate: '2024-02-15T17:00:00Z',
        estimatedHours: 2
      },
      {
        id: 'step-7',
        name: 'Mentor Assignment',
        description: 'Meet with assigned mentor and establish goals.',
        type: 'meeting',
        status: 'pending',
        order: 7,
        assignee: 'mentor-1',
        assigneeName: 'Senior Developer',
        dueDate: '2024-02-20T10:00:00Z',
        estimatedHours: 1
      },
      {
        id: 'step-8',
        name: 'Final Review',
        description: 'Final onboarding review with HR and manager.',
        type: 'review',
        status: 'pending',
        order: 8,
        assignee: 'hr-1',
        assigneeName: 'HR Manager',
        dueDate: '2024-02-25T14:00:00Z',
        estimatedHours: 1
      }
    ],
    assignee: 'trainee-1',
    assigneeName: 'John Doe',
    assigneeAvatar: '/avatars/john-doe.jpg',
    dueDate: '2024-02-28T17:00:00Z',
    startedAt: '2024-01-15T09:00:00Z',
    estimatedDuration: 20,
    actualDuration: 15,
    createdBy: 'hr-1',
    createdAt: '2024-01-10T10:00:00Z',
    updatedAt: '2024-01-25T15:30:00Z',
    tags: ['onboarding', 'new-hire', 'graduate'],
    dependencies: [],
    notifications: [
      {
        id: 'notif-1',
        type: 'reminder',
        message: 'Assessment due in 2 days',
        createdAt: '2024-02-13T09:00:00Z',
        read: false
      }
    ]
  };
  
  const workflow = mockWorkflow;
  const overdue = isOverdue(workflow.dueDate);
  
  const containerClasses = [
    'workflow-card',
    `workflow-card--${variant}`,
    `workflow-card--${workflow.status}`,
    overdue ? 'workflow-card--overdue' : '',
    className
  ].filter(Boolean).join(' ');
  
  return (
    <div className={containerClasses}>
      <WorkflowHeader workflow={workflow} variant={variant} />
      
      {variant !== 'compact' && workflow.description && (
        <div className="workflow-card__description">
          {workflow.description}
        </div>
      )}
      
      <WorkflowProgress workflow={workflow} variant={variant} />
      
      <WorkflowAssignee workflow={workflow} variant={variant} />
      
      <WorkflowSteps 
        steps={showAllSteps ? workflow.steps : workflow.steps} 
        variant={variant} 
      />
      
      {variant === 'detailed' && workflow.tags.length > 0 && (
        <div className="workflow-card__tags">
          <span className="workflow-card__tags-label">Tags:</span>
          <div className="workflow-card__tags-list">
            {workflow.tags.map((tag: string, index: number) => (
              <Badge key={index} variant="default" size="small">
                {tag}
              </Badge>
            ))}
          </div>
        </div>
      )}
      
      {variant === 'detailed' && workflow.notifications.length > 0 && (
        <div className="workflow-card__notifications">
          <span className="workflow-card__notifications-label">Recent Notifications:</span>
          <div className="workflow-card__notifications-list">
            {workflow.notifications.slice(0, 3).map((notification) => (
              <div key={notification.id} className={`workflow-card__notification ${
                !notification.read ? 'workflow-card__notification--unread' : ''
              }`}>
                <Badge variant={getStatusColor(notification.type) as any} size="small">
                  {notification.type}
                </Badge>
                <span className="workflow-card__notification-message">
                  {notification.message}
                </span>
                <span className="workflow-card__notification-time">
                  {formatDate(notification.createdAt)}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
      
      <WorkflowActions
        workflow={workflow}
        showActions={showActions}
        onView={onView}
        onEdit={onEdit}
        onDelete={onDelete}
        onStart={onStart}
        onPause={onPause}
        onResume={onResume}
        onComplete={onComplete}
      />
    </div>
  );
};

export default WorkflowCard;