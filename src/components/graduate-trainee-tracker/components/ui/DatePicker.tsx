import React, { useState, useRef, useEffect, forwardRef } from 'react';
import { FieldError } from 'react-hook-form';

// Types
export interface DatePickerProps {
  value?: Date | string;
  onChange?: (date: Date | null) => void;
  placeholder?: string;
  disabled?: boolean;
  error?: FieldError | string | boolean;
  className?: string;
  size?: 'small' | 'medium' | 'large';
  variant?: 'default' | 'filled' | 'outlined';
  format?: 'MM/dd/yyyy' | 'dd/MM/yyyy' | 'yyyy-MM-dd' | 'MMM dd, yyyy';
  minDate?: Date;
  maxDate?: Date;
  showTime?: boolean;
  timeFormat?: '12h' | '24h';
  clearable?: boolean;
  showToday?: boolean;
  disabledDates?: Date[];
  highlightedDates?: Date[];
  weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6; // 0 = Sunday, 1 = Monday, etc.
  locale?: string;
  name?: string;
  id?: string;
  'aria-label'?: string;
  'aria-describedby'?: string;
}

export interface CalendarProps {
  selectedDate?: Date;
  onDateSelect: (date: Date) => void;
  minDate?: Date;
  maxDate?: Date;
  disabledDates?: Date[];
  highlightedDates?: Date[];
  weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6;
  showTime?: boolean;
  timeFormat?: '12h' | '24h';
  locale?: string;
}

// Utility functions
const formatDate = (date: Date, format: string): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const monthNames = [
    'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
    'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
  ];
  
  switch (format) {
    case 'MM/dd/yyyy':
      return `${month}/${day}/${year}`;
    case 'dd/MM/yyyy':
      return `${day}/${month}/${year}`;
    case 'yyyy-MM-dd':
      return `${year}-${month}-${day}`;
    case 'MMM dd, yyyy':
      return `${monthNames[date.getMonth()]} ${day}, ${year}`;
    default:
      return `${month}/${day}/${year}`;
  }
};

const parseDate = (dateString: string, format: string): Date | null => {
  if (!dateString) return null;
  
  try {
    // Simple parsing - in a real app, you'd use a library like date-fns
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? null : date;
  } catch {
    return null;
  }
};

const isSameDay = (date1: Date, date2: Date): boolean => {
  return date1.getFullYear() === date2.getFullYear() &&
         date1.getMonth() === date2.getMonth() &&
         date1.getDate() === date2.getDate();
};

const isDateDisabled = (date: Date, minDate?: Date, maxDate?: Date, disabledDates?: Date[]): boolean => {
  if (minDate && date < minDate) return true;
  if (maxDate && date > maxDate) return true;
  if (disabledDates && disabledDates.some(disabledDate => isSameDay(date, disabledDate))) return true;
  return false;
};

const isDateHighlighted = (date: Date, highlightedDates?: Date[]): boolean => {
  return highlightedDates ? highlightedDates.some(highlightedDate => isSameDay(date, highlightedDate)) : false;
};

// Calendar Component
const Calendar: React.FC<CalendarProps> = ({
  selectedDate,
  onDateSelect,
  minDate,
  maxDate,
  disabledDates,
  highlightedDates,
  weekStartsOn = 0,
  showTime = false,
  timeFormat = '12h',
  locale = 'en-US'
}) => {
  const [currentMonth, setCurrentMonth] = useState(() => selectedDate || new Date());
  const [selectedTime, setSelectedTime] = useState(() => {
    if (selectedDate) {
      return {
        hours: selectedDate.getHours(),
        minutes: selectedDate.getMinutes()
      };
    }
    return { hours: 12, minutes: 0 };
  });

  const today = new Date();
  const year = currentMonth.getFullYear();
  const month = currentMonth.getMonth();
  
  // Get first day of month and adjust for week start
  const firstDayOfMonth = new Date(year, month, 1);
  const lastDayOfMonth = new Date(year, month + 1, 0);
  const firstDayWeekday = (firstDayOfMonth.getDay() - weekStartsOn + 7) % 7;
  
  // Generate calendar days
  const calendarDays: (Date | null)[] = [];
  
  // Add empty cells for days before month starts
  for (let i = 0; i < firstDayWeekday; i++) {
    calendarDays.push(null);
  }
  
  // Add days of the month
  for (let day = 1; day <= lastDayOfMonth.getDate(); day++) {
    calendarDays.push(new Date(year, month, day));
  }
  
  // Week day names
  const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  const adjustedWeekDays = [...weekDays.slice(weekStartsOn), ...weekDays.slice(0, weekStartsOn)];
  
  const handleDateClick = (date: Date) => {
    if (isDateDisabled(date, minDate, maxDate, disabledDates)) return;
    
    if (showTime) {
      const newDate = new Date(date);
      newDate.setHours(selectedTime.hours, selectedTime.minutes);
      onDateSelect(newDate);
    } else {
      onDateSelect(date);
    }
  };
  
  const handleTimeChange = (hours: number, minutes: number) => {
    setSelectedTime({ hours, minutes });
    if (selectedDate) {
      const newDate = new Date(selectedDate);
      newDate.setHours(hours, minutes);
      onDateSelect(newDate);
    }
  };
  
  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentMonth(prev => {
      const newMonth = new Date(prev);
      if (direction === 'prev') {
        newMonth.setMonth(newMonth.getMonth() - 1);
      } else {
        newMonth.setMonth(newMonth.getMonth() + 1);
      }
      return newMonth;
    });
  };
  
  return (
    <div className="date-picker__calendar">
      {/* Calendar Header */}
      <div className="date-picker__calendar-header">
        <button
          type="button"
          onClick={() => navigateMonth('prev')}
          className="date-picker__nav-button"
          aria-label="Previous month"
        >
          <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
            <path d="M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z"/>
          </svg>
        </button>
        
        <div className="date-picker__month-year">
          {currentMonth.toLocaleDateString(locale, { month: 'long', year: 'numeric' })}
        </div>
        
        <button
          type="button"
          onClick={() => navigateMonth('next')}
          className="date-picker__nav-button"
          aria-label="Next month"
        >
          <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
            <path d="M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z"/>
          </svg>
        </button>
      </div>
      
      {/* Week Days */}
      <div className="date-picker__week-days">
        {adjustedWeekDays.map(day => (
          <div key={day} className="date-picker__week-day">
            {day}
          </div>
        ))}
      </div>
      
      {/* Calendar Grid */}
      <div className="date-picker__calendar-grid">
        {calendarDays.map((date, index) => {
          if (!date) {
            return <div key={index} className="date-picker__calendar-cell date-picker__calendar-cell--empty" />;
          }
          
          const isSelected = selectedDate && isSameDay(date, selectedDate);
          const isToday = isSameDay(date, today);
          const isDisabled = isDateDisabled(date, minDate, maxDate, disabledDates);
          const isHighlighted = isDateHighlighted(date, highlightedDates);
          
          const cellClasses = [
            'date-picker__calendar-cell',
            isSelected && 'date-picker__calendar-cell--selected',
            isToday && 'date-picker__calendar-cell--today',
            isDisabled && 'date-picker__calendar-cell--disabled',
            isHighlighted && 'date-picker__calendar-cell--highlighted'
          ].filter(Boolean).join(' ');
          
          return (
            <button
              key={index}
              type="button"
              className={cellClasses}
              onClick={() => handleDateClick(date)}
              disabled={isDisabled}
              aria-label={date.toLocaleDateString(locale)}
            >
              {date.getDate()}
            </button>
          );
        })}
      </div>
      
      {/* Time Picker */}
      {showTime && (
        <div className="date-picker__time-picker">
          <div className="date-picker__time-inputs">
            <select
              value={selectedTime.hours}
              onChange={(e) => handleTimeChange(Number(e.target.value), selectedTime.minutes)}
              className="date-picker__time-select"
            >
              {Array.from({ length: timeFormat === '12h' ? 12 : 24 }, (_, i) => {
                const hour = timeFormat === '12h' ? (i === 0 ? 12 : i) : i;
                return (
                  <option key={i} value={timeFormat === '12h' ? (i === 0 ? 12 : i) : i}>
                    {String(hour).padStart(2, '0')}
                  </option>
                );
              })}
            </select>
            <span>:</span>
            <select
              value={selectedTime.minutes}
              onChange={(e) => handleTimeChange(selectedTime.hours, Number(e.target.value))}
              className="date-picker__time-select"
            >
              {Array.from({ length: 60 }, (_, i) => (
                <option key={i} value={i}>
                  {String(i).padStart(2, '0')}
                </option>
              ))}
            </select>
            {timeFormat === '12h' && (
              <select
                value={selectedTime.hours >= 12 ? 'PM' : 'AM'}
                onChange={(e) => {
                  const isPM = e.target.value === 'PM';
                  const newHours = isPM ? 
                    (selectedTime.hours < 12 ? selectedTime.hours + 12 : selectedTime.hours) :
                    (selectedTime.hours >= 12 ? selectedTime.hours - 12 : selectedTime.hours);
                  handleTimeChange(newHours, selectedTime.minutes);
                }}
                className="date-picker__time-select"
              >
                <option value="AM">AM</option>
                <option value="PM">PM</option>
              </select>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// Main DatePicker Component
export const DatePicker = forwardRef<HTMLInputElement, DatePickerProps>((
  {
    value,
    onChange,
    placeholder = 'Select date',
    disabled = false,
    error,
    className = '',
    size = 'medium',
    variant = 'default',
    format = 'MM/dd/yyyy',
    minDate,
    maxDate,
    showTime = false,
    timeFormat = '12h',
    clearable = true,
    showToday = true,
    disabledDates,
    highlightedDates,
    weekStartsOn = 0,
    locale = 'en-US',
    name,
    id,
    'aria-label': ariaLabel,
    'aria-describedby': ariaDescribedBy
  },
  ref
) => {
  const [isOpen, setIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  
  // Initialize date from value prop
  useEffect(() => {
    if (value) {
      const date = value instanceof Date ? value : parseDate(value, format);
      if (date) {
        setSelectedDate(date);
        setInputValue(formatDate(date, format));
      }
    } else {
      setSelectedDate(null);
      setInputValue('');
    }
  }, [value, format]);
  
  // Handle clicks outside to close calendar
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    
    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen]);
  
  // Handle keyboard events
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
        inputRef.current?.focus();
      }
    };
    
    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isOpen]);
  
  const hasError = !!error;
  
  const baseClasses = 'date-picker';
  const sizeClasses = {
    small: 'date-picker--small',
    medium: 'date-picker--medium',
    large: 'date-picker--large'
  };
  const variantClasses = {
    default: 'date-picker--default',
    filled: 'date-picker--filled',
    outlined: 'date-picker--outlined'
  };
  
  const containerClasses = [
    baseClasses,
    sizeClasses[size],
    variantClasses[variant],
    hasError && 'date-picker--error',
    disabled && 'date-picker--disabled',
    isOpen && 'date-picker--open',
    className
  ].filter(Boolean).join(' ');
  
  const inputClasses = [
    'date-picker__input',
    hasError && 'date-picker__input--error',
    disabled && 'date-picker__input--disabled'
  ].filter(Boolean).join(' ');
  
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;
    setInputValue(newValue);
    
    // Try to parse the input value
    const parsedDate = parseDate(newValue, format);
    if (parsedDate && !isDateDisabled(parsedDate, minDate, maxDate, disabledDates)) {
      setSelectedDate(parsedDate);
      onChange?.(parsedDate);
    }
  };
  
  const handleDateSelect = (date: Date) => {
    setSelectedDate(date);
    setInputValue(formatDate(date, format));
    onChange?.(date);
    if (!showTime) {
      setIsOpen(false);
    }
  };
  
  const handleClear = () => {
    setSelectedDate(null);
    setInputValue('');
    onChange?.(null);
    inputRef.current?.focus();
  };
  
  const handleTodayClick = () => {
    const today = new Date();
    if (!isDateDisabled(today, minDate, maxDate, disabledDates)) {
      handleDateSelect(today);
    }
  };
  
  const toggleCalendar = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };
  
  return (
    <div ref={containerRef} className={containerClasses}>
      <div className="date-picker__input-wrapper">
        <input
          ref={ref || inputRef}
          type="text"
          name={name}
          id={id}
          value={inputValue}
          onChange={handleInputChange}
          onClick={toggleCalendar}
          placeholder={placeholder}
          disabled={disabled}
          className={inputClasses}
          aria-label={ariaLabel}
          aria-describedby={ariaDescribedBy}
          aria-expanded={isOpen}
          aria-haspopup="dialog"
          role="combobox"
        />
        
        <div className="date-picker__input-actions">
          {clearable && selectedDate && (
            <button
              type="button"
              onClick={handleClear}
              className="date-picker__clear-button"
              aria-label="Clear date"
              tabIndex={-1}
            >
              <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8 2.146 2.854Z"/>
              </svg>
            </button>
          )}
          
          <button
            type="button"
            onClick={toggleCalendar}
            className="date-picker__calendar-button"
            aria-label="Open calendar"
            tabIndex={-1}
          >
            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5zM1 4v10a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V4H1z"/>
            </svg>
          </button>
        </div>
      </div>
      
      {isOpen && (
        <div className="date-picker__dropdown">
          <Calendar
            selectedDate={selectedDate || undefined}
            onDateSelect={handleDateSelect}
            minDate={minDate}
            maxDate={maxDate}
            disabledDates={disabledDates}
            highlightedDates={highlightedDates}
            weekStartsOn={weekStartsOn}
            showTime={showTime}
            timeFormat={timeFormat}
            locale={locale}
          />
          
          {showToday && (
            <div className="date-picker__footer">
              <button
                type="button"
                onClick={handleTodayClick}
                className="date-picker__today-button"
                disabled={isDateDisabled(new Date(), minDate, maxDate, disabledDates)}
              >
                Today
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
});

DatePicker.displayName = 'DatePicker';

export default DatePicker;