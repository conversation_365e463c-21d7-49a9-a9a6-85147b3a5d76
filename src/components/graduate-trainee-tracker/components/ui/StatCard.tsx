import React, { forwardRef } from 'react';

// Types
export interface StatCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  description?: string;
  icon?: React.ReactNode;
  trend?: {
    value: number;
    label?: string;
    direction: 'up' | 'down' | 'neutral';
  };
  color?: 'default' | 'primary' | 'success' | 'warning' | 'error' | 'info';
  variant?: 'default' | 'outlined' | 'filled' | 'gradient';
  size?: 'small' | 'medium' | 'large';
  loading?: boolean;
  clickable?: boolean;
  onClick?: () => void;
  className?: string;
  children?: React.ReactNode;
  footer?: React.ReactNode;
  badge?: {
    text: string;
    color?: 'default' | 'primary' | 'success' | 'warning' | 'error' | 'info';
  };
  progress?: {
    value: number;
    max?: number;
    color?: string;
    showLabel?: boolean;
  };
  chart?: React.ReactNode;
  actions?: React.ReactNode;
  id?: string;
  'aria-label'?: string;
  'aria-describedby'?: string;
}

// Trend Icon Component
interface TrendIconProps {
  direction: 'up' | 'down' | 'neutral';
  className?: string;
}

const TrendIcon: React.FC<TrendIconProps> = ({ direction, className = '' }) => {
  const icons = {
    up: (
      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className={className}>
        <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"/>
      </svg>
    ),
    down: (
      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className={className}>
        <path d="M16 18l2.29-2.29-4.88-4.88-4 4L2 7.41 3.41 6l6 6 4-4 6.3 6.29L22 12v6z"/>
      </svg>
    ),
    neutral: (
      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className={className}>
        <path d="M22 12l-4-4v3H3v2h15v3z"/>
      </svg>
    )
  };
  
  return icons[direction];
};

// Loading Skeleton Component
const LoadingSkeleton: React.FC = () => {
  return (
    <div className="stat-card__loading">
      <div className="stat-card__loading-header">
        <div className="stat-card__loading-icon" />
        <div className="stat-card__loading-title" />
      </div>
      <div className="stat-card__loading-value" />
      <div className="stat-card__loading-subtitle" />
    </div>
  );
};

// Progress Bar Component
interface ProgressBarProps {
  value: number;
  max?: number;
  color?: string;
  showLabel?: boolean;
  className?: string;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  value,
  max = 100,
  color = 'currentColor',
  showLabel = false,
  className = ''
}) => {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100);
  
  return (
    <div className={`stat-card__progress ${className}`}>
      <div className="stat-card__progress-track">
        <div 
          className="stat-card__progress-fill"
          style={{ 
            width: `${percentage}%`,
            backgroundColor: color
          }}
        />
      </div>
      {showLabel && (
        <span className="stat-card__progress-label">
          {Math.round(percentage)}%
        </span>
      )}
    </div>
  );
};

// Badge Component
interface BadgeProps {
  text: string;
  color?: 'default' | 'primary' | 'success' | 'warning' | 'error' | 'info';
  className?: string;
}

const Badge: React.FC<BadgeProps> = ({ text, color = 'default', className = '' }) => {
  const colorClasses = {
    default: 'stat-card__badge--default',
    primary: 'stat-card__badge--primary',
    success: 'stat-card__badge--success',
    warning: 'stat-card__badge--warning',
    error: 'stat-card__badge--error',
    info: 'stat-card__badge--info'
  };
  
  return (
    <span className={`stat-card__badge ${colorClasses[color]} ${className}`}>
      {text}
    </span>
  );
};

// Format number utility
const formatNumber = (value: string | number): string => {
  if (typeof value === 'string') return value;
  
  if (value >= 1000000) {
    return (value / 1000000).toFixed(1) + 'M';
  }
  if (value >= 1000) {
    return (value / 1000).toFixed(1) + 'K';
  }
  return value.toString();
};

// Format trend value
const formatTrendValue = (value: number): string => {
  const absValue = Math.abs(value);
  const sign = value >= 0 ? '+' : '-';
  return `${sign}${absValue}%`;
};

// Main StatCard Component
export const StatCard = forwardRef<HTMLDivElement, StatCardProps>((
  {
    title,
    value,
    subtitle,
    description,
    icon,
    trend,
    color = 'default',
    variant = 'default',
    size = 'medium',
    loading = false,
    clickable = false,
    onClick,
    className = '',
    children,
    footer,
    badge,
    progress,
    chart,
    actions,
    id,
    'aria-label': ariaLabel,
    'aria-describedby': ariaDescribedBy
  },
  ref
) => {
  const baseClasses = 'stat-card';
  
  const colorClasses = {
    default: 'stat-card--default',
    primary: 'stat-card--primary',
    success: 'stat-card--success',
    warning: 'stat-card--warning',
    error: 'stat-card--error',
    info: 'stat-card--info'
  };
  
  const variantClasses = {
    default: 'stat-card--default-variant',
    outlined: 'stat-card--outlined',
    filled: 'stat-card--filled',
    gradient: 'stat-card--gradient'
  };
  
  const sizeClasses = {
    small: 'stat-card--small',
    medium: 'stat-card--medium',
    large: 'stat-card--large'
  };
  
  const trendClasses = {
    up: 'stat-card__trend--up',
    down: 'stat-card__trend--down',
    neutral: 'stat-card__trend--neutral'
  };
  
  const containerClasses = [
    baseClasses,
    colorClasses[color],
    variantClasses[variant],
    sizeClasses[size],
    loading && 'stat-card--loading',
    clickable && 'stat-card--clickable',
    className
  ].filter(Boolean).join(' ');
  
  const handleClick = () => {
    if (clickable && onClick && !loading) {
      onClick();
    }
  };
  
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (clickable && onClick && !loading && (event.key === 'Enter' || event.key === ' ')) {
      event.preventDefault();
      onClick();
    }
  };
  
  if (loading) {
    return (
      <div
        ref={ref}
        className={containerClasses}
        id={id}
        aria-label={ariaLabel}
        aria-describedby={ariaDescribedBy}
      >
        <LoadingSkeleton />
      </div>
    );
  }
  
  return (
    <div
      ref={ref}
      className={containerClasses}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      role={clickable ? 'button' : undefined}
      tabIndex={clickable ? 0 : undefined}
      id={id}
      aria-label={ariaLabel}
      aria-describedby={ariaDescribedBy}
    >
      {/* Header */}
      <div className="stat-card__header">
        <div className="stat-card__header-content">
          {icon && (
            <div className="stat-card__icon">
              {icon}
            </div>
          )}
          
          <div className="stat-card__title-section">
            <h3 className="stat-card__title">{title}</h3>
            {subtitle && (
              <p className="stat-card__subtitle">{subtitle}</p>
            )}
          </div>
        </div>
        
        <div className="stat-card__header-actions">
          {badge && (
            <Badge text={badge.text} color={badge.color} />
          )}
          
          {actions && (
            <div className="stat-card__actions">
              {actions}
            </div>
          )}
        </div>
      </div>
      
      {/* Main Content */}
      <div className="stat-card__content">
        <div className="stat-card__value-section">
          <div className="stat-card__value">
            {formatNumber(value)}
          </div>
          
          {trend && (
            <div className={`stat-card__trend ${trendClasses[trend.direction]}`}>
              <TrendIcon direction={trend.direction} className="stat-card__trend-icon" />
              <span className="stat-card__trend-value">
                {formatTrendValue(trend.value)}
              </span>
              {trend.label && (
                <span className="stat-card__trend-label">
                  {trend.label}
                </span>
              )}
            </div>
          )}
        </div>
        
        {description && (
          <p className="stat-card__description">{description}</p>
        )}
        
        {progress && (
          <ProgressBar
            value={progress.value}
            max={progress.max}
            color={progress.color}
            showLabel={progress.showLabel}
            className="stat-card__progress-bar"
          />
        )}
        
        {chart && (
          <div className="stat-card__chart">
            {chart}
          </div>
        )}
        
        {children && (
          <div className="stat-card__custom-content">
            {children}
          </div>
        )}
      </div>
      
      {/* Footer */}
      {footer && (
        <div className="stat-card__footer">
          {footer}
        </div>
      )}
    </div>
  );
});

StatCard.displayName = 'StatCard';

// Create compound component with proper typing
type StatCardWithSubComponents = typeof StatCard & {
  Badge: typeof Badge;
  ProgressBar: typeof ProgressBar;
  TrendIcon: typeof TrendIcon;
};

const StatCardWithSubComponents = StatCard as StatCardWithSubComponents;
StatCardWithSubComponents.Badge = Badge;
StatCardWithSubComponents.ProgressBar = ProgressBar;
StatCardWithSubComponents.TrendIcon = TrendIcon;

export default StatCardWithSubComponents;