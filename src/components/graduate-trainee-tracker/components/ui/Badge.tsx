import React, { forwardRef } from 'react';

// Types
export interface BadgeProps {
  children?: React.ReactNode;
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info' | 'outline' | 'ghost';
  size?: 'small' | 'medium' | 'large';
  color?: string;
  className?: string;
  rounded?: boolean;
  removable?: boolean;
  onRemove?: () => void;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  clickable?: boolean;
  onClick?: () => void;
  disabled?: boolean;
  pulse?: boolean;
  dot?: boolean;
  count?: number;
  maxCount?: number;
  showZero?: boolean;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  offset?: [number, number];
  id?: string;
  'aria-label'?: string;
  'aria-describedby'?: string;
  role?: string;
}

export interface CountBadgeProps {
  count: number;
  maxCount?: number;
  showZero?: boolean;
  variant?: BadgeProps['variant'];
  size?: BadgeProps['size'];
  className?: string;
  children: React.ReactNode;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  offset?: [number, number];
  dot?: boolean;
}

export interface StatusBadgeProps {
  status: 'online' | 'offline' | 'away' | 'busy' | 'idle';
  size?: 'small' | 'medium' | 'large';
  className?: string;
  children?: React.ReactNode;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  offset?: [number, number];
  showText?: boolean;
}

// Utility functions
const formatCount = (count: number, maxCount?: number): string => {
  if (maxCount && count > maxCount) {
    return `${maxCount}+`;
  }
  return count.toString();
};

const getStatusColor = (status: StatusBadgeProps['status']): string => {
  const colors = {
    online: '#10b981', // green
    offline: '#6b7280', // gray
    away: '#f59e0b', // yellow
    busy: '#ef4444', // red
    idle: '#8b5cf6' // purple
  };
  return colors[status];
};

const getStatusText = (status: StatusBadgeProps['status']): string => {
  const texts = {
    online: 'Online',
    offline: 'Offline',
    away: 'Away',
    busy: 'Busy',
    idle: 'Idle'
  };
  return texts[status];
};

// Remove Icon Component
const RemoveIcon: React.FC<{ onClick: () => void; className?: string }> = ({ onClick, className = '' }) => {
  return (
    <button
      type="button"
      onClick={onClick}
      className={`badge__remove ${className}`}
      aria-label="Remove"
    >
      <svg
        width="12"
        height="12"
        viewBox="0 0 12 12"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M9 3L3 9M3 3L9 9"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </button>
  );
};

// Main Badge Component
export const Badge = forwardRef<HTMLSpanElement, BadgeProps>((
  {
    children,
    variant = 'default',
    size = 'medium',
    color,
    className = '',
    rounded = true,
    removable = false,
    onRemove,
    icon,
    iconPosition = 'left',
    clickable = false,
    onClick,
    disabled = false,
    pulse = false,
    dot = false,
    count,
    maxCount = 99,
    showZero = false,
    position,
    offset = [0, 0],
    id,
    'aria-label': ariaLabel,
    'aria-describedby': ariaDescribedBy,
    role
  },
  ref
) => {
  const baseClasses = 'badge';
  
  const variantClasses = {
    default: 'badge--default',
    primary: 'badge--primary',
    secondary: 'badge--secondary',
    success: 'badge--success',
    warning: 'badge--warning',
    error: 'badge--error',
    info: 'badge--info',
    outline: 'badge--outline',
    ghost: 'badge--ghost'
  };
  
  const sizeClasses = {
    small: 'badge--small',
    medium: 'badge--medium',
    large: 'badge--large'
  };
  
  const positionClasses = {
    'top-right': 'badge--top-right',
    'top-left': 'badge--top-left',
    'bottom-right': 'badge--bottom-right',
    'bottom-left': 'badge--bottom-left'
  };
  
  const badgeClasses = [
    baseClasses,
    variantClasses[variant],
    sizeClasses[size],
    rounded && 'badge--rounded',
    clickable && 'badge--clickable',
    disabled && 'badge--disabled',
    pulse && 'badge--pulse',
    dot && 'badge--dot',
    position && positionClasses[position],
    className
  ].filter(Boolean).join(' ');
  
  const badgeStyle: React.CSSProperties = {
    ...(color && { backgroundColor: color }),
    ...(position && {
      transform: `translate(${offset[0]}px, ${offset[1]}px)`
    })
  };
  
  const handleClick = () => {
    if (clickable && onClick && !disabled) {
      onClick();
    }
  };
  
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (clickable && (event.key === 'Enter' || event.key === ' ')) {
      event.preventDefault();
      handleClick();
    }
  };
  
  const renderContent = () => {
    if (dot) {
      return null;
    }
    
    if (count !== undefined) {
      if (count === 0 && !showZero) {
        return null;
      }
      return formatCount(count, maxCount);
    }
    
    return (
      <>
        {icon && iconPosition === 'left' && (
          <span className="badge__icon badge__icon--left">{icon}</span>
        )}
        
        <span className="badge__content">{children}</span>
        
        {icon && iconPosition === 'right' && (
          <span className="badge__icon badge__icon--right">{icon}</span>
        )}
        
        {removable && onRemove && (
          <RemoveIcon onClick={onRemove} className="badge__remove-icon" />
        )}
      </>
    );
  };
  
  const content = renderContent();
  
  // Don't render if content is null (e.g., count is 0 and showZero is false)
  if (content === null && !dot) {
    return null;
  }
  
  const Component = clickable ? 'button' : 'span';
  
  return (
    <Component
      ref={ref as any}
      className={badgeClasses}
      style={badgeStyle}
      onClick={clickable ? handleClick : undefined}
      onKeyDown={clickable ? handleKeyDown : undefined}
      disabled={clickable ? disabled : undefined}
      tabIndex={clickable && !disabled ? 0 : undefined}
      role={role || (clickable ? 'button' : 'status')}
      aria-label={ariaLabel}
      aria-describedby={ariaDescribedBy}
      id={id}
    >
      {content}
    </Component>
  );
});

Badge.displayName = 'Badge';

// Count Badge Component
export const CountBadge = forwardRef<HTMLDivElement, CountBadgeProps>((
  {
    count,
    maxCount = 99,
    showZero = false,
    variant = 'error',
    size = 'small',
    className = '',
    children,
    position = 'top-right',
    offset = [0, 0],
    dot = false
  },
  ref
) => {
  const containerClasses = [
    'count-badge',
    className
  ].filter(Boolean).join(' ');
  
  return (
    <div ref={ref} className={containerClasses}>
      <div className="count-badge__content">
        {children}
      </div>
      
      <Badge
        count={count}
        maxCount={maxCount}
        showZero={showZero}
        variant={variant}
        size={size}
        position={position}
        offset={offset}
        dot={dot}
        className="count-badge__badge"
      />
    </div>
  );
});

CountBadge.displayName = 'CountBadge';

// Status Badge Component
export const StatusBadge = forwardRef<HTMLDivElement, StatusBadgeProps>((
  {
    status,
    size = 'medium',
    className = '',
    children,
    position = 'bottom-right',
    offset = [0, 0],
    showText = false
  },
  ref
) => {
  const containerClasses = [
    'status-badge',
    className
  ].filter(Boolean).join(' ');
  
  const statusColor = getStatusColor(status);
  const statusText = getStatusText(status);
  
  return (
    <div ref={ref} className={containerClasses}>
      {children && (
        <div className="status-badge__content">
          {children}
        </div>
      )}
      
      <div
        className={`status-badge__indicator status-badge__indicator--${size} status-badge__indicator--${position}`}
        style={{
          backgroundColor: statusColor,
          transform: `translate(${offset[0]}px, ${offset[1]}px)`
        }}
        title={statusText}
        aria-label={`Status: ${statusText}`}
      />
      
      {showText && (
        <span className="status-badge__text">{statusText}</span>
      )}
    </div>
  );
});

StatusBadge.displayName = 'StatusBadge';

// Notification Badge Component
export interface NotificationBadgeProps {
  count?: number;
  maxCount?: number;
  showZero?: boolean;
  dot?: boolean;
  variant?: BadgeProps['variant'];
  size?: BadgeProps['size'];
  className?: string;
  children: React.ReactNode;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  offset?: [number, number];
  pulse?: boolean;
}

export const NotificationBadge = forwardRef<HTMLDivElement, NotificationBadgeProps>((
  {
    count,
    maxCount = 99,
    showZero = false,
    dot = false,
    variant = 'error',
    size = 'small',
    className = '',
    children,
    position = 'top-right',
    offset = [-8, -8],
    pulse = false
  },
  ref
) => {
  const containerClasses = [
    'notification-badge',
    className
  ].filter(Boolean).join(' ');
  
  const shouldShow = dot || (count !== undefined && (count > 0 || showZero));
  
  return (
    <div ref={ref} className={containerClasses}>
      <div className="notification-badge__content">
        {children}
      </div>
      
      {shouldShow && (
        <Badge
          count={dot ? undefined : count}
          maxCount={maxCount}
          showZero={showZero}
          variant={variant}
          size={size}
          position={position}
          offset={offset}
          dot={dot}
          pulse={pulse}
          className="notification-badge__badge"
        />
      )}
    </div>
  );
});

NotificationBadge.displayName = 'NotificationBadge';

// Create compound component with proper typing
type BadgeWithSubComponents = typeof Badge & {
  Count: typeof CountBadge;
  Status: typeof StatusBadge;
  Notification: typeof NotificationBadge;
};

const BadgeWithSubComponents = Badge as BadgeWithSubComponents;
BadgeWithSubComponents.Count = CountBadge;
BadgeWithSubComponents.Status = StatusBadge;
BadgeWithSubComponents.Notification = NotificationBadge;

export default BadgeWithSubComponents;