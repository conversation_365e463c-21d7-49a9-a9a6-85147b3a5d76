import React, { useState, useRef, useEffect, forwardRef, useMemo } from 'react';
import { FieldError } from 'react-hook-form';

// Types
export interface Option {
  value: string | number;
  label: string;
  disabled?: boolean;
  group?: string;
  description?: string;
  icon?: React.ReactNode;
}

export interface OptionGroup {
  label: string;
  options: Option[];
  disabled?: boolean | string;
}

export interface MultiSelectProps {
  options: Option[] | OptionGroup[];
  value?: (string | number)[];
  onChange?: (values: (string | number)[]) => void;
  placeholder?: string;
  searchPlaceholder?: string;
  disabled?: boolean;
  error?: FieldError | string | boolean;
  className?: string;
  size?: 'small' | 'medium' | 'large';
  variant?: 'default' | 'filled' | 'outlined';
  maxHeight?: number;
  searchable?: boolean;
  clearable?: boolean;
  selectAll?: boolean;
  closeOnSelect?: boolean;
  maxSelectedItems?: number;
  showSelectedCount?: boolean;
  customSelectedDisplay?: (selectedItems: Option[]) => React.ReactNode;
  filterFunction?: (option: Option, searchTerm: string) => boolean;
  sortFunction?: (a: Option, b: Option) => number;
  groupBy?: (option: Option) => string;
  virtualized?: boolean;
  loading?: boolean;
  loadingText?: string;
  noOptionsText?: string;
  name?: string;
  id?: string;
  'aria-label'?: string;
  'aria-describedby'?: string;
}

// Default filter function
const defaultFilterFunction = (option: Option, searchTerm: string): boolean => {
  const term = searchTerm.toLowerCase();
  return (
    option.label.toLowerCase().includes(term) ||
    (option.description && option.description.toLowerCase().includes(term))
  );
};

// Default sort function
const defaultSortFunction = (a: Option, b: Option): number => {
  return a.label.localeCompare(b.label);
};

// Utility functions
const isOptionGroup = (item: Option | OptionGroup): item is OptionGroup => {
  return 'options' in item;
};

// Utility to normalize boolean values
const normalizeBoolean = (value: string | boolean | undefined): boolean => {
  if (typeof value === 'boolean') return value;
  if (typeof value === 'string') return value === 'true';
  return false;
};

const flattenOptions = (items: (Option | OptionGroup)[]): Option[] => {
  const flattened: Option[] = [];
  
  items.forEach(item => {
    if (isOptionGroup(item)) {
      flattened.push(...item.options);
    } else {
      flattened.push(item);
    }
  });
  
  return flattened;
};

const groupOptions = (options: Option[], groupBy?: (option: Option) => string): OptionGroup[] => {
  if (!groupBy) return [{ label: '', options }];
  
  const groups: { [key: string]: Option[] } = {};
  
  options.forEach(option => {
    const groupKey = groupBy(option);
    if (!groups[groupKey]) {
      groups[groupKey] = [];
    }
    groups[groupKey].push(option);
  });
  
  return Object.entries(groups).map(([label, options]) => ({ label, options }));
};

// Option Component
interface OptionItemProps {
  option: Option;
  isSelected: boolean;
  isHighlighted: boolean;
  onSelect: (option: Option) => void;
  searchTerm?: string;
}

const OptionItem: React.FC<OptionItemProps> = ({
  option,
  isSelected,
  isHighlighted,
  onSelect,
  searchTerm
}) => {
  const handleClick = () => {
    if (!option.disabled) {
      onSelect(option);
    }
  };
  
  const highlightText = (text: string, term?: string) => {
    if (!term) return text;
    
    const regex = new RegExp(`(${term})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="multi-select__highlight">{part}</mark>
      ) : part
    );
  };
  
  const classes = [
    'multi-select__option',
    isSelected && 'multi-select__option--selected',
    isHighlighted && 'multi-select__option--highlighted',
    option.disabled && 'multi-select__option--disabled'
  ].filter(Boolean).join(' ');
  
  return (
    <div
      className={classes}
      onClick={handleClick}
      role="option"
      aria-selected={isSelected}
      aria-disabled={normalizeBoolean(option.disabled)}
    >
      <div className="multi-select__option-content">
        {option.icon && (
          <div className="multi-select__option-icon">
            {option.icon}
          </div>
        )}
        
        <div className="multi-select__option-text">
          <div className="multi-select__option-label">
            {highlightText(option.label, searchTerm)}
          </div>
          {option.description && (
            <div className="multi-select__option-description">
              {highlightText(option.description, searchTerm)}
            </div>
          )}
        </div>
      </div>
      
      {isSelected && (
        <div className="multi-select__option-check">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
            <path d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"/>
          </svg>
        </div>
      )}
    </div>
  );
};

// Selected Item Component
interface SelectedItemProps {
  option: Option;
  onRemove: (option: Option) => void;
  disabled?: boolean;
}

const SelectedItem: React.FC<SelectedItemProps> = ({ option, onRemove, disabled }) => {
  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!disabled) {
      onRemove(option);
    }
  };
  
  return (
    <div className="multi-select__selected-item">
      {option.icon && (
        <div className="multi-select__selected-item-icon">
          {option.icon}
        </div>
      )}
      <span className="multi-select__selected-item-label">{option.label}</span>
      {!disabled && (
        <button
          type="button"
          onClick={handleRemove}
          className="multi-select__selected-item-remove"
          aria-label={`Remove ${option.label}`}
          tabIndex={-1}
        >
          <svg width="12" height="12" viewBox="0 0 16 16" fill="currentColor">
            <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8 2.146 2.854Z"/>
          </svg>
        </button>
      )}
    </div>
  );
};

// Main MultiSelect Component
export const MultiSelect = forwardRef<HTMLDivElement, MultiSelectProps>((
  {
    options,
    value = [],
    onChange,
    placeholder = 'Select options...',
    searchPlaceholder = 'Search options...',
    disabled = false,
    error,
    className = '',
    size = 'medium',
    variant = 'default',
    maxHeight = 200,
    searchable = true,
    clearable = true,
    selectAll = false,
    closeOnSelect = false,
    maxSelectedItems,
    showSelectedCount = false,
    customSelectedDisplay,
    filterFunction = defaultFilterFunction,
    sortFunction = defaultSortFunction,
    groupBy,
    virtualized = false,
    loading = false,
    loadingText = 'Loading...',
    noOptionsText = 'No options available',
    name,
    id,
    'aria-label': ariaLabel,
    'aria-describedby': ariaDescribedBy
  },
  ref
) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const containerRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const optionsRef = useRef<HTMLDivElement>(null);
  
  // Flatten and process options
  const flatOptions = useMemo(() => {
    const flattened = flattenOptions(options);
    return flattened.sort(sortFunction);
  }, [options, sortFunction]);
  
  // Filter options based on search term
  const filteredOptions = useMemo(() => {
    if (!searchTerm) return flatOptions;
    return flatOptions.filter(option => filterFunction(option, searchTerm));
  }, [flatOptions, searchTerm, filterFunction]);
  
  // Group filtered options
  const groupedOptions = useMemo(() => {
    return groupOptions(filteredOptions, groupBy);
  }, [filteredOptions, groupBy]);
  
  // Get selected options
  const selectedOptions = useMemo(() => {
    return flatOptions.filter(option => value.includes(option.value));
  }, [flatOptions, value]);
  
  // Handle clicks outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm('');
        setHighlightedIndex(-1);
      }
    };
    
    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen]);
  
  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return;
      
      switch (event.key) {
        case 'Escape':
          setIsOpen(false);
          setSearchTerm('');
          setHighlightedIndex(-1);
          break;
        case 'ArrowDown':
          event.preventDefault();
          setHighlightedIndex(prev => 
            prev < filteredOptions.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          event.preventDefault();
          setHighlightedIndex(prev => 
            prev > 0 ? prev - 1 : filteredOptions.length - 1
          );
          break;
        case 'Enter':
          event.preventDefault();
          if (highlightedIndex >= 0 && highlightedIndex < filteredOptions.length) {
            handleOptionSelect(filteredOptions[highlightedIndex]);
          }
          break;
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, filteredOptions, highlightedIndex]);
  
  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchable && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen, searchable]);
  
  const hasError = !!error;
  
  const baseClasses = 'multi-select';
  const sizeClasses = {
    small: 'multi-select--small',
    medium: 'multi-select--medium',
    large: 'multi-select--large'
  };
  const variantClasses = {
    default: 'multi-select--default',
    filled: 'multi-select--filled',
    outlined: 'multi-select--outlined'
  };
  
  const containerClasses = [
    baseClasses,
    sizeClasses[size],
    variantClasses[variant],
    hasError && 'multi-select--error',
    disabled && 'multi-select--disabled',
    isOpen && 'multi-select--open',
    className
  ].filter(Boolean).join(' ');
  
  const handleToggle = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
      if (!isOpen) {
        setHighlightedIndex(-1);
      }
    }
  };
  
  const handleOptionSelect = (option: Option) => {
    if (option.disabled) return;
    
    const isSelected = value.includes(option.value);
    let newValue: (string | number)[];
    
    if (isSelected) {
      newValue = value.filter(v => v !== option.value);
    } else {
      if (maxSelectedItems && value.length >= maxSelectedItems) {
        return; // Don't add more items if limit reached
      }
      newValue = [...value, option.value];
    }
    
    onChange?.(newValue);
    
    if (closeOnSelect && !isSelected) {
      setIsOpen(false);
      setSearchTerm('');
      setHighlightedIndex(-1);
    }
  };
  
  const handleSelectAll = () => {
    const availableOptions = filteredOptions.filter(option => !option.disabled);
    const allSelected = availableOptions.every(option => value.includes(option.value));
    
    if (allSelected) {
      // Deselect all filtered options
      const newValue = value.filter(v => 
        !availableOptions.some(option => option.value === v)
      );
      onChange?.(newValue);
    } else {
      // Select all filtered options (respecting maxSelectedItems)
      const currentCount = value.length;
      const availableCount = availableOptions.length;
      const maxToAdd = maxSelectedItems ? Math.max(0, maxSelectedItems - currentCount) : availableCount;
      
      const optionsToAdd = availableOptions
        .filter(option => !value.includes(option.value))
        .slice(0, maxToAdd)
        .map(option => option.value);
      
      onChange?.([...value, ...optionsToAdd]);
    }
  };
  
  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onChange?.([]);
  };
  
  const handleRemoveItem = (option: Option) => {
    const newValue = value.filter(v => v !== option.value);
    onChange?.(newValue);
  };
  
  const renderSelectedItems = () => {
    if (customSelectedDisplay) {
      return customSelectedDisplay(selectedOptions);
    }
    
    if (selectedOptions.length === 0) {
      return <span className="multi-select__placeholder">{placeholder}</span>;
    }
    
    if (showSelectedCount && selectedOptions.length > 2) {
      return (
        <span className="multi-select__selected-count">
          {selectedOptions.length} items selected
        </span>
      );
    }
    
    return (
      <div className="multi-select__selected-items">
        {selectedOptions.map(option => (
          <SelectedItem
            key={option.value}
            option={option}
            onRemove={handleRemoveItem}
            disabled={disabled}
          />
        ))}
      </div>
    );
  };
  
  const renderOptions = () => {
    if (loading) {
      return (
        <div className="multi-select__loading">
          <div className="multi-select__loading-spinner" />
          <span>{loadingText}</span>
        </div>
      );
    }
    
    if (filteredOptions.length === 0) {
      return (
        <div className="multi-select__no-options">
          {noOptionsText}
        </div>
      );
    }
    
    return groupedOptions.map((group, groupIndex) => (
      <div key={group.label || groupIndex} className="multi-select__option-group">
        {group.label && (
          <div className="multi-select__option-group-label">
            {group.label}
          </div>
        )}
        {group.options.map((option, optionIndex) => {
          const flatIndex = filteredOptions.findIndex(o => o.value === option.value);
          return (
            <OptionItem
              key={option.value}
              option={option}
              isSelected={value.includes(option.value)}
              isHighlighted={flatIndex === highlightedIndex}
              onSelect={handleOptionSelect}
              searchTerm={searchTerm}
            />
          );
        })}
      </div>
    ));
  };
  
  return (
    <div ref={ref || containerRef} className={containerClasses}>
      <div className="multi-select__control" onClick={handleToggle}>
        <div className="multi-select__value">
          {renderSelectedItems()}
        </div>
        
        <div className="multi-select__indicators">
          {clearable && selectedOptions.length > 0 && (
            <button
              type="button"
              onClick={handleClear}
              className="multi-select__clear-button"
              aria-label="Clear selection"
              tabIndex={-1}
            >
              <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8 2.146 2.854Z"/>
              </svg>
            </button>
          )}
          
          <div className="multi-select__dropdown-indicator">
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="currentColor"
              className={isOpen ? 'multi-select__dropdown-icon--open' : ''}
            >
              <path d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z"/>
            </svg>
          </div>
        </div>
      </div>
      
      {isOpen && (
        <div className="multi-select__dropdown" style={{ maxHeight }}>
          {searchable && (
            <div className="multi-select__search">
              <input
                ref={searchInputRef}
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder={searchPlaceholder}
                className="multi-select__search-input"
              />
            </div>
          )}
          
          {selectAll && filteredOptions.length > 0 && (
            <div className="multi-select__select-all">
              <button
                type="button"
                onClick={handleSelectAll}
                className="multi-select__select-all-button"
              >
                {filteredOptions.filter(option => !option.disabled).every(option => value.includes(option.value))
                  ? 'Deselect All'
                  : 'Select All'
                }
              </button>
            </div>
          )}
          
          <div
            ref={optionsRef}
            className="multi-select__options"
            role="listbox"
            aria-multiselectable="true"
          >
            {renderOptions()}
          </div>
        </div>
      )}
      
      {/* Hidden input for form submission */}
      <input
        type="hidden"
        name={name}
        value={JSON.stringify(value)}
      />
    </div>
  );
});

MultiSelect.displayName = 'MultiSelect';

export default MultiSelect;