import React, { useState, useEffect, useMemo } from 'react';
import { FormField, DatePicker, MultiSelect, FileUpload, Badge } from './ui';
import { usePrograms } from '../hooks';

// Types
export interface ProgramFormProps {
  programId?: string;
  onSubmit: (data: ProgramFormData) => void;
  onCancel?: () => void;
  className?: string;
  mode?: 'create' | 'edit' | 'view';
  initialData?: Partial<ProgramFormData>;
}

export interface ProgramFormData {
  name: string;
  description: string;
  duration: number;
  durationUnit: 'days' | 'weeks' | 'months' | 'years';
  startDate: string;
  endDate: string;
  capacity: number;
  department: string;
  level: 'entry' | 'intermediate' | 'advanced';
  status: 'draft' | 'active' | 'completed' | 'cancelled';
  objectives: string[];
  prerequisites: string[];
  competencies: ProgramCompetency[];
  modules: ProgramModule[];
  assessments: ProgramAssessment[];
  resources: ProgramResource[];
  instructors: string[];
  tags: string[];
  budget?: number;
  currency?: string;
}

export interface ProgramCompetency {
  id: string;
  name: string;
  description: string;
  level: 'basic' | 'intermediate' | 'advanced' | 'expert';
  weight: number;
  required: boolean;
}

export interface ProgramModule {
  id: string;
  name: string;
  description: string;
  duration: number;
  order: number;
  type: 'lecture' | 'workshop' | 'project' | 'assessment' | 'fieldwork';
  objectives: string[];
  resources: string[];
  prerequisites?: string[];
  deliverables?: string[];
}

export interface ProgramAssessment {
  id: string;
  name: string;
  type: 'quiz' | 'assignment' | 'project' | 'presentation' | 'exam';
  weight: number;
  passingScore: number;
  moduleId?: string;
  dueDate?: string;
  instructions: string;
}

export interface ProgramResource {
  id: string;
  name: string;
  type: 'document' | 'video' | 'link' | 'book' | 'software';
  url?: string;
  description?: string;
  required: boolean;
}

// Utility functions
const validateForm = (data: Partial<ProgramFormData>): string[] => {
  const errors: string[] = [];
  
  if (!data.name?.trim()) {
    errors.push('Program name is required');
  }
  
  if (!data.description?.trim()) {
    errors.push('Program description is required');
  }
  
  if (!data.duration || data.duration <= 0) {
    errors.push('Duration must be greater than 0');
  }
  
  if (!data.startDate) {
    errors.push('Start date is required');
  }
  
  if (!data.endDate) {
    errors.push('End date is required');
  }
  
  if (data.startDate && data.endDate && new Date(data.startDate) >= new Date(data.endDate)) {
    errors.push('End date must be after start date');
  }
  
  if (!data.capacity || data.capacity <= 0) {
    errors.push('Capacity must be greater than 0');
  }
  
  if (!data.department?.trim()) {
    errors.push('Department is required');
  }
  
  return errors;
};

const calculateEndDate = (startDate: string, duration: number, unit: string): string => {
  const start = new Date(startDate);
  const multipliers = {
    days: 1,
    weeks: 7,
    months: 30,
    years: 365
  };
  
  const days = duration * (multipliers[unit as keyof typeof multipliers] || 1);
  const end = new Date(start.getTime() + days * 24 * 60 * 60 * 1000);
  
  return end.toISOString().split('T')[0];
};

const formatDuration = (duration: number, unit: string): string => {
  return `${duration} ${unit}${duration !== 1 ? 's' : ''}`;
};

// Sub-components
interface BasicInfoProps {
  data: Partial<ProgramFormData>;
  onChange: (field: string, value: any) => void;
  errors: string[];
  mode: 'create' | 'edit' | 'view';
}

const BasicInfo: React.FC<BasicInfoProps> = ({ data, onChange, errors, mode }) => {
  const isReadOnly = mode === 'view';
  
  const durationOptions = [
    { value: 'days', label: 'Days' },
    { value: 'weeks', label: 'Weeks' },
    { value: 'months', label: 'Months' },
    { value: 'years', label: 'Years' }
  ];
  
  const levelOptions = [
    { value: 'entry', label: 'Entry Level' },
    { value: 'intermediate', label: 'Intermediate' },
    { value: 'advanced', label: 'Advanced' }
  ];
  
  const statusOptions = [
    { value: 'draft', label: 'Draft' },
    { value: 'active', label: 'Active' },
    { value: 'completed', label: 'Completed' },
    { value: 'cancelled', label: 'Cancelled' }
  ];
  
  // Auto-calculate end date when start date or duration changes
  useEffect(() => {
    if (data.startDate && data.duration && data.durationUnit) {
      const endDate = calculateEndDate(data.startDate, data.duration, data.durationUnit);
      onChange('endDate', endDate);
    }
  }, [data.startDate, data.duration, data.durationUnit, onChange]);
  
  return (
    <div className="program-form__section">
      <h3 className="program-form__section-title">Basic Information</h3>
      
      <div className="program-form__grid">
        <FormField name="name" label="Program Name" required error={errors.find(e => e.includes('name'))}>
          <FormField.Input
            value={data.name || ''}
            onChange={(e) => onChange('name', e.target.value)}
            placeholder="Enter program name"
            disabled={isReadOnly}
          />
        </FormField>
        
        <FormField name="department" label="Department" required error={errors.find(e => e.includes('Department'))}>
          <FormField.Input
            value={data.department || ''}
            onChange={(e) => onChange('department', e.target.value)}
            placeholder="Enter department"
            disabled={isReadOnly}
          />
        </FormField>
        
        <FormField name="description" label="Description" required error={errors.find(e => e.includes('description'))}>
          <FormField.Textarea
            value={data.description || ''}
            onChange={(e) => onChange('description', e.target.value)}
            placeholder="Enter program description"
            rows={4}
            disabled={isReadOnly}
          />
        </FormField>
        
        <div className="program-form__duration-group">
          <FormField name="duration" label="Duration" required error={errors.find(e => e.includes('Duration'))}>
            <FormField.Input
              type="number"
              value={data.duration || ''}
              onChange={(e) => onChange('duration', parseInt(e.target.value) || 0)}
              placeholder="0"
              min="1"
              disabled={isReadOnly}
            />
          </FormField>
          
          <FormField name="durationUnit" label="Unit">
            <FormField.Select
              value={data.durationUnit || 'weeks'}
              onChange={(e) => onChange('durationUnit', e.target.value)}
              disabled={isReadOnly}
            >
              {durationOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </FormField.Select>
          </FormField>
        </div>
        
        <FormField name="startDate" label="Start Date" required error={errors.find(e => e.includes('Start date'))}>
          <DatePicker
            value={data.startDate || ''}
            onChange={(date) => onChange('startDate', date)}
            disabled={isReadOnly}
          />
        </FormField>
        
        <FormField name="endDate" label="End Date" required error={errors.find(e => e.includes('End date'))}>
          <DatePicker
            value={data.endDate || ''}
            onChange={(date) => onChange('endDate', date)}
            disabled={isReadOnly}
            minDate={data.startDate ? new Date(data.startDate) : undefined}
          />
        </FormField>
        
        <FormField name="capacity" label="Capacity" required error={errors.find(e => e.includes('Capacity'))}>
          <FormField.Input
            type="number"
            value={data.capacity || ''}
            onChange={(e) => onChange('capacity', parseInt(e.target.value) || 0)}
            placeholder="0"
            min="1"
            disabled={isReadOnly}
          />
        </FormField>
        
        <FormField name="level" label="Level">
          <FormField.Select
            value={data.level || 'entry'}
            onChange={(e) => onChange('level', e.target.value)}
            disabled={isReadOnly}
          >
            {levelOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </FormField.Select>
        </FormField>
        
        <FormField name="status" label="Status">
          <FormField.Select
            value={data.status || 'draft'}
            onChange={(e) => onChange('status', e.target.value)}
            disabled={isReadOnly}
          >
            {statusOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </FormField.Select>
        </FormField>
        
        <div className="program-form__budget-group">
          <FormField name="budget" label="Budget">
            <FormField.Input
              type="number"
              value={data.budget || ''}
              onChange={(e) => onChange('budget', parseFloat(e.target.value) || 0)}
              placeholder="0.00"
              min="0"
              step="0.01"
              disabled={isReadOnly}
            />
          </FormField>
          
          <FormField name="currency" label="Currency">
            <FormField.Select
              value={data.currency || 'USD'}
              onChange={(e) => onChange('currency', e.target.value)}
              disabled={isReadOnly}
            >
              <option value="USD">USD</option>
              <option value="EUR">EUR</option>
              <option value="GBP">GBP</option>
              <option value="CAD">CAD</option>
            </FormField.Select>
          </FormField>
        </div>
      </div>
    </div>
  );
};

interface ObjectivesProps {
  objectives: string[];
  onChange: (objectives: string[]) => void;
  mode: 'create' | 'edit' | 'view';
}

const Objectives: React.FC<ObjectivesProps> = ({ objectives, onChange, mode }) => {
  const [newObjective, setNewObjective] = useState('');
  const isReadOnly = mode === 'view';
  
  const addObjective = () => {
    if (newObjective.trim() && !objectives.includes(newObjective.trim())) {
      onChange([...objectives, newObjective.trim()]);
      setNewObjective('');
    }
  };
  
  const removeObjective = (index: number) => {
    onChange(objectives.filter((_, i) => i !== index));
  };
  
  return (
    <div className="program-form__section">
      <h3 className="program-form__section-title">Learning Objectives</h3>
      
      {!isReadOnly && (
        <div className="program-form__add-item">
          <FormField name="newObjective" label="Add Objective">
            <div className="program-form__add-item-input">
              <FormField.Input
                value={newObjective}
                onChange={(e) => setNewObjective(e.target.value)}
                placeholder="Enter learning objective"
                onKeyPress={(e) => e.key === 'Enter' && addObjective()}
              />
              <button
                type="button"
                onClick={addObjective}
                className="program-form__add-btn"
                disabled={!newObjective.trim()}
              >
                Add
              </button>
            </div>
          </FormField>
        </div>
      )}
      
      <div className="program-form__list">
        {objectives.map((objective, index) => (
          <div key={index} className="program-form__list-item">
            <span className="program-form__list-text">{objective}</span>
            
            {!isReadOnly && (
              <button
                type="button"
                onClick={() => removeObjective(index)}
                className="program-form__remove-btn"
              >
                Remove
              </button>
            )}
          </div>
        ))}
      </div>
      
      {objectives.length === 0 && (
        <div className="program-form__empty-state">
          No learning objectives added yet.
        </div>
      )}
    </div>
  );
};

interface PrerequisitesProps {
  prerequisites: string[];
  onChange: (prerequisites: string[]) => void;
  mode: 'create' | 'edit' | 'view';
}

const Prerequisites: React.FC<PrerequisitesProps> = ({ prerequisites, onChange, mode }) => {
  const [newPrerequisite, setNewPrerequisite] = useState('');
  const isReadOnly = mode === 'view';
  
  const addPrerequisite = () => {
    if (newPrerequisite.trim() && !prerequisites.includes(newPrerequisite.trim())) {
      onChange([...prerequisites, newPrerequisite.trim()]);
      setNewPrerequisite('');
    }
  };
  
  const removePrerequisite = (index: number) => {
    onChange(prerequisites.filter((_, i) => i !== index));
  };
  
  return (
    <div className="program-form__section">
      <h3 className="program-form__section-title">Prerequisites</h3>
      
      {!isReadOnly && (
        <div className="program-form__add-item">
          <FormField name="newPrerequisite" label="Add Prerequisite">
            <div className="program-form__add-item-input">
              <FormField.Input
                value={newPrerequisite}
                onChange={(e) => setNewPrerequisite(e.target.value)}
                placeholder="Enter prerequisite"
                onKeyPress={(e) => e.key === 'Enter' && addPrerequisite()}
              />
              <button
                type="button"
                onClick={addPrerequisite}
                className="program-form__add-btn"
                disabled={!newPrerequisite.trim()}
              >
                Add
              </button>
            </div>
          </FormField>
        </div>
      )}
      
      <div className="program-form__list">
        {prerequisites.map((prerequisite, index) => (
          <div key={index} className="program-form__list-item">
            <span className="program-form__list-text">{prerequisite}</span>
            
            {!isReadOnly && (
              <button
                type="button"
                onClick={() => removePrerequisite(index)}
                className="program-form__remove-btn"
              >
                Remove
              </button>
            )}
          </div>
        ))}
      </div>
      
      {prerequisites.length === 0 && (
        <div className="program-form__empty-state">
          No prerequisites specified.
        </div>
      )}
    </div>
  );
};

interface TagsProps {
  tags: string[];
  onChange: (tags: string[]) => void;
  mode: 'create' | 'edit' | 'view';
}

const Tags: React.FC<TagsProps> = ({ tags, onChange, mode }) => {
  const isReadOnly = mode === 'view';
  
  const tagOptions = [
    { value: 'leadership', label: 'Leadership' },
    { value: 'technical', label: 'Technical' },
    { value: 'communication', label: 'Communication' },
    { value: 'project-management', label: 'Project Management' },
    { value: 'data-analysis', label: 'Data Analysis' },
    { value: 'customer-service', label: 'Customer Service' },
    { value: 'sales', label: 'Sales' },
    { value: 'marketing', label: 'Marketing' },
    { value: 'finance', label: 'Finance' },
    { value: 'hr', label: 'Human Resources' },
    { value: 'operations', label: 'Operations' },
    { value: 'strategy', label: 'Strategy' }
  ];
  
  return (
    <div className="program-form__section">
      <h3 className="program-form__section-title">Tags</h3>
      
      {!isReadOnly ? (
        <MultiSelect
          options={tagOptions}
          value={tags}
          onChange={(values) => onChange(values as string[])}
          placeholder="Select tags"
          searchable
          creatable
        />
      ) : (
        <div className="program-form__tags-display">
          {tags.map((tag) => (
            <Badge key={tag} variant="outline" size="small">
              {tagOptions.find(opt => opt.value === tag)?.label || tag}
            </Badge>
          ))}
          
          {tags.length === 0 && (
            <div className="program-form__empty-state">
              No tags assigned.
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// Main ProgramForm Component
export const ProgramForm: React.FC<ProgramFormProps> = ({
  programId,
  onSubmit,
  onCancel,
  className = '',
  mode = 'create',
  initialData = {}
}) => {
  const [formData, setFormData] = useState<Partial<ProgramFormData>>({
    name: '',
    description: '',
    duration: 12,
    durationUnit: 'weeks',
    startDate: '',
    endDate: '',
    capacity: 20,
    department: '',
    level: 'entry',
    status: 'draft',
    objectives: [],
    prerequisites: [],
    competencies: [],
    modules: [],
    assessments: [],
    resources: [],
    instructors: [],
    tags: [],
    currency: 'USD',
    ...initialData
  });
  
  const [errors, setErrors] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState<string>('basic');
  const { data: existingProgram, loading } = usePrograms(programId ? { id: programId } : {});
  
  // Load existing program data
  useEffect(() => {
    if (existingProgram && mode === 'edit') {
      setFormData(existingProgram);
    }
  }, [existingProgram, mode]);
  
  const handleFieldChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear related errors
    setErrors(prev => prev.filter(error => !error.toLowerCase().includes(field.toLowerCase())));
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const validationErrors = validateForm(formData);
    
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }
    
    onSubmit(formData as ProgramFormData);
  };
  
  const isReadOnly = mode === 'view';
  const isLoading = loading && mode === 'edit';
  
  const containerClasses = [
    'program-form',
    `program-form--${mode}`,
    className
  ].filter(Boolean).join(' ');
  
  const tabs = [
    { id: 'basic', label: 'Basic Info' },
    { id: 'objectives', label: 'Objectives' },
    { id: 'prerequisites', label: 'Prerequisites' },
    { id: 'tags', label: 'Tags' }
  ];
  
  const renderTabContent = () => {
    switch (activeTab) {
      case 'basic':
        return (
          <BasicInfo
            data={formData}
            onChange={handleFieldChange}
            errors={errors}
            mode={mode}
          />
        );
      
      case 'objectives':
        return (
          <Objectives
            objectives={formData.objectives || []}
            onChange={(objectives) => handleFieldChange('objectives', objectives)}
            mode={mode}
          />
        );
      
      case 'prerequisites':
        return (
          <Prerequisites
            prerequisites={formData.prerequisites || []}
            onChange={(prerequisites) => handleFieldChange('prerequisites', prerequisites)}
            mode={mode}
          />
        );
      
      case 'tags':
        return (
          <Tags
            tags={formData.tags || []}
            onChange={(tags) => handleFieldChange('tags', tags)}
            mode={mode}
          />
        );
      
      default:
        return null;
    }
  };
  
  if (isLoading) {
    return (
      <div className="program-form program-form--loading">
        <div className="program-form__skeleton">
          Loading program data...
        </div>
      </div>
    );
  }
  
  return (
    <form className={containerClasses} onSubmit={handleSubmit}>
      <div className="program-form__header">
        <h2 className="program-form__title">
          {mode === 'create' && 'Create New Program'}
          {mode === 'edit' && 'Edit Program'}
          {mode === 'view' && 'Program Details'}
        </h2>
        
        {formData.name && (
          <div className="program-form__subtitle">
            {formData.name}
          </div>
        )}
      </div>
      
      {errors.length > 0 && (
        <div className="program-form__errors">
          <h4>Please fix the following errors:</h4>
          <ul>
            {errors.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        </div>
      )}
      
      <div className="program-form__tabs">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            type="button"
            onClick={() => setActiveTab(tab.id)}
            className={`program-form__tab ${
              activeTab === tab.id ? 'program-form__tab--active' : ''
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>
      
      <div className="program-form__content">
        {renderTabContent()}
      </div>
      
      <div className="program-form__actions">
        {onCancel && (
          <button
            type="button"
            onClick={onCancel}
            className="program-form__action-btn program-form__action-btn--cancel"
          >
            Cancel
          </button>
        )}
        
        {!isReadOnly && (
          <button
            type="submit"
            className="program-form__action-btn program-form__action-btn--submit"
          >
            {mode === 'create' ? 'Create Program' : 'Update Program'}
          </button>
        )}
      </div>
    </form>
  );
};

export default ProgramForm;