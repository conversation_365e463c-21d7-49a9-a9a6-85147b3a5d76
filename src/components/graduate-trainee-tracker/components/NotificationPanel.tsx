import React, { useState, useMemo } from 'react';
import { Badge, Avatar } from './ui';

// Types
export interface NotificationPanelProps {
  className?: string;
  variant?: 'sidebar' | 'dropdown' | 'page';
  maxHeight?: string;
  showFilters?: boolean;
  showMarkAllRead?: boolean;
  onNotificationClick?: (notification: Notification) => void;
  onMarkAsRead?: (notificationId: string) => void;
  onMarkAllAsRead?: () => void;
  onDeleteNotification?: (notificationId: string) => void;
  onClearAll?: () => void;
}

export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error' | 'reminder' | 'assignment' | 'approval' | 'system';
  category: 'trainee' | 'program' | 'review' | 'workflow' | 'assessment' | 'system' | 'general';
  title: string;
  message: string;
  description?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  read: boolean;
  createdAt: string;
  readAt?: string;
  expiresAt?: string;
  actionUrl?: string;
  actionLabel?: string;
  sender?: NotificationSender;
  recipients: string[];
  metadata?: NotificationMetadata;
  attachments?: NotificationAttachment[];
}

export interface NotificationSender {
  id: string;
  name: string;
  avatar?: string;
  role: string;
  type: 'user' | 'system';
}

export interface NotificationMetadata {
  traineeId?: string;
  traineeName?: string;
  programId?: string;
  programName?: string;
  reviewId?: string;
  workflowId?: string;
  assessmentId?: string;
  relatedEntity?: string;
  relatedEntityId?: string;
  customData?: Record<string, any>;
}

export interface NotificationAttachment {
  id: string;
  name: string;
  type: string;
  url: string;
  size: number;
}

export interface NotificationStats {
  total: number;
  unread: number;
  byType: Record<string, number>;
  byCategory: Record<string, number>;
  byPriority: Record<string, number>;
}

export interface NotificationFilters {
  type?: string[];
  category?: string[];
  priority?: string[];
  read?: boolean;
  dateRange?: {
    start: string;
    end: string;
  };
  sender?: string[];
}

// Utility functions
const formatTimeAgo = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  if (diffInSeconds < 60) {
    return 'Just now';
  }
  
  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes}m ago`;
  }
  
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours}h ago`;
  }
  
  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) {
    return `${diffInDays}d ago`;
  }
  
  const diffInWeeks = Math.floor(diffInDays / 7);
  if (diffInWeeks < 4) {
    return `${diffInWeeks}w ago`;
  }
  
  const diffInMonths = Math.floor(diffInDays / 30);
  return `${diffInMonths}mo ago`;
};

const formatDateTime = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const isExpired = (expiresAt?: string): boolean => {
  if (!expiresAt) return false;
  return new Date(expiresAt) < new Date();
};

const getTypeColor = (type: string): string => {
  const colors = {
    info: 'info',
    success: 'success',
    warning: 'warning',
    error: 'error',
    reminder: 'warning',
    assignment: 'info',
    approval: 'warning',
    system: 'default'
  };
  return colors[type as keyof typeof colors] || 'default';
};

const getPriorityColor = (priority: string): string => {
  const colors = {
    low: 'default',
    medium: 'info',
    high: 'warning',
    urgent: 'error'
  };
  return colors[priority as keyof typeof colors] || 'default';
};

const getCategoryIcon = (category: string): string => {
  const icons = {
    trainee: '👤',
    program: '📚',
    review: '📝',
    workflow: '⚡',
    assessment: '📊',
    system: '⚙️',
    general: '📢'
  };
  return icons[category as keyof typeof icons] || '📢';
};

const getTypeIcon = (type: string): string => {
  const icons = {
    info: 'ℹ️',
    success: '✅',
    warning: '⚠️',
    error: '❌',
    reminder: '⏰',
    assignment: '📋',
    approval: '✋',
    system: '🔧'
  };
  return icons[type as keyof typeof icons] || 'ℹ️';
};

// Sub-components
interface NotificationFiltersProps {
  filters: NotificationFilters;
  onFiltersChange: (filters: NotificationFilters) => void;
  stats: NotificationStats;
}

const NotificationFilters: React.FC<NotificationFiltersProps> = ({
  filters,
  onFiltersChange,
  stats
}) => {
  const handleFilterChange = (key: keyof NotificationFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value
    });
  };
  
  return (
    <div className="notification-panel__filters">
      <div className="notification-panel__filter-group">
        <label className="notification-panel__filter-label">Type</label>
        <select
          value={filters.type?.[0] || ''}
          onChange={(e) => handleFilterChange('type', e.target.value ? [e.target.value] : undefined)}
          className="notification-panel__filter-select"
        >
          <option value="">All Types</option>
          <option value="info">Info ({stats.byType.info || 0})</option>
          <option value="success">Success ({stats.byType.success || 0})</option>
          <option value="warning">Warning ({stats.byType.warning || 0})</option>
          <option value="error">Error ({stats.byType.error || 0})</option>
          <option value="reminder">Reminder ({stats.byType.reminder || 0})</option>
          <option value="assignment">Assignment ({stats.byType.assignment || 0})</option>
          <option value="approval">Approval ({stats.byType.approval || 0})</option>
          <option value="system">System ({stats.byType.system || 0})</option>
        </select>
      </div>
      
      <div className="notification-panel__filter-group">
        <label className="notification-panel__filter-label">Category</label>
        <select
          value={filters.category?.[0] || ''}
          onChange={(e) => handleFilterChange('category', e.target.value ? [e.target.value] : undefined)}
          className="notification-panel__filter-select"
        >
          <option value="">All Categories</option>
          <option value="trainee">Trainee ({stats.byCategory.trainee || 0})</option>
          <option value="program">Program ({stats.byCategory.program || 0})</option>
          <option value="review">Review ({stats.byCategory.review || 0})</option>
          <option value="workflow">Workflow ({stats.byCategory.workflow || 0})</option>
          <option value="assessment">Assessment ({stats.byCategory.assessment || 0})</option>
          <option value="system">System ({stats.byCategory.system || 0})</option>
          <option value="general">General ({stats.byCategory.general || 0})</option>
        </select>
      </div>
      
      <div className="notification-panel__filter-group">
        <label className="notification-panel__filter-label">Priority</label>
        <select
          value={filters.priority?.[0] || ''}
          onChange={(e) => handleFilterChange('priority', e.target.value ? [e.target.value] : undefined)}
          className="notification-panel__filter-select"
        >
          <option value="">All Priorities</option>
          <option value="urgent">Urgent ({stats.byPriority.urgent || 0})</option>
          <option value="high">High ({stats.byPriority.high || 0})</option>
          <option value="medium">Medium ({stats.byPriority.medium || 0})</option>
          <option value="low">Low ({stats.byPriority.low || 0})</option>
        </select>
      </div>
      
      <div className="notification-panel__filter-group">
        <label className="notification-panel__filter-label">Status</label>
        <select
          value={filters.read === undefined ? '' : filters.read ? 'read' : 'unread'}
          onChange={(e) => {
            const value = e.target.value;
            handleFilterChange('read', value === '' ? undefined : value === 'read');
          }}
          className="notification-panel__filter-select"
        >
          <option value="">All ({stats.total})</option>
          <option value="unread">Unread ({stats.unread})</option>
          <option value="read">Read ({stats.total - stats.unread})</option>
        </select>
      </div>
    </div>
  );
};

interface NotificationItemProps {
  notification: Notification;
  variant: 'sidebar' | 'dropdown' | 'page';
  onNotificationClick?: (notification: Notification) => void;
  onMarkAsRead?: (notificationId: string) => void;
  onDeleteNotification?: (notificationId: string) => void;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  variant,
  onNotificationClick,
  onMarkAsRead,
  onDeleteNotification
}) => {
  const expired = isExpired(notification.expiresAt);
  
  const handleClick = () => {
    if (!notification.read && onMarkAsRead) {
      onMarkAsRead(notification.id);
    }
    if (onNotificationClick) {
      onNotificationClick(notification);
    }
  };
  
  const handleMarkAsRead = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onMarkAsRead) {
      onMarkAsRead(notification.id);
    }
  };
  
  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onDeleteNotification) {
      onDeleteNotification(notification.id);
    }
  };
  
  const itemClasses = [
    'notification-panel__item',
    `notification-panel__item--${variant}`,
    `notification-panel__item--${notification.type}`,
    `notification-panel__item--${notification.priority}`,
    !notification.read ? 'notification-panel__item--unread' : '',
    expired ? 'notification-panel__item--expired' : '',
    notification.actionUrl ? 'notification-panel__item--clickable' : ''
  ].filter(Boolean).join(' ');
  
  return (
    <div className={itemClasses} onClick={handleClick}>
      <div className="notification-panel__item-header">
        <div className="notification-panel__item-icon">
          <span className="notification-panel__item-type-icon">
            {getTypeIcon(notification.type)}
          </span>
          <span className="notification-panel__item-category-icon">
            {getCategoryIcon(notification.category)}
          </span>
        </div>
        
        <div className="notification-panel__item-title-section">
          <h4 className="notification-panel__item-title">{notification.title}</h4>
          
          <div className="notification-panel__item-badges">
            <Badge variant={getTypeColor(notification.type) as any} size="small">
              {notification.type}
            </Badge>
            
            <Badge variant={getPriorityColor(notification.priority) as any} size="small">
              {notification.priority}
            </Badge>
            
            {!notification.read && (
              <Badge variant="info" size="small">
                New
              </Badge>
            )}
            
            {expired && (
              <Badge variant="error" size="small">
                Expired
              </Badge>
            )}
          </div>
        </div>
        
        <div className="notification-panel__item-actions">
          {!notification.read && onMarkAsRead && (
            <button
              onClick={handleMarkAsRead}
              className="notification-panel__item-action notification-panel__item-action--read"
              title="Mark as read"
            >
              ✓
            </button>
          )}
          
          {onDeleteNotification && (
            <button
              onClick={handleDelete}
              className="notification-panel__item-action notification-panel__item-action--delete"
              title="Delete notification"
            >
              ×
            </button>
          )}
        </div>
      </div>
      
      <div className="notification-panel__item-content">
        <div className="notification-panel__item-message">
          {notification.message}
        </div>
        
        {variant === 'page' && notification.description && (
          <div className="notification-panel__item-description">
            {notification.description}
          </div>
        )}
        
        <div className="notification-panel__item-meta">
          {notification.sender && (
            <div className="notification-panel__item-sender">
              <Avatar
                src={notification.sender.avatar}
                alt={notification.sender.name}
                size="small"
                fallback={notification.sender.name.charAt(0)}
              />
              <span className="notification-panel__item-sender-name">
                {notification.sender.name}
              </span>
              <span className="notification-panel__item-sender-role">
                ({notification.sender.role})
              </span>
            </div>
          )}
          
          <div className="notification-panel__item-time">
            <span className="notification-panel__item-time-ago">
              {formatTimeAgo(notification.createdAt)}
            </span>
            {variant === 'page' && (
              <span className="notification-panel__item-time-full">
                {formatDateTime(notification.createdAt)}
              </span>
            )}
          </div>
        </div>
        
        {notification.metadata && variant === 'page' && (
          <div className="notification-panel__item-metadata">
            {notification.metadata.traineeName && (
              <span className="notification-panel__item-metadata-item">
                Trainee: {notification.metadata.traineeName}
              </span>
            )}
            {notification.metadata.programName && (
              <span className="notification-panel__item-metadata-item">
                Program: {notification.metadata.programName}
              </span>
            )}
          </div>
        )}
        
        {notification.attachments && notification.attachments.length > 0 && variant === 'page' && (
          <div className="notification-panel__item-attachments">
            <span className="notification-panel__item-attachments-label">Attachments:</span>
            <div className="notification-panel__item-attachments-list">
              {notification.attachments.map((attachment) => (
                <a
                  key={attachment.id}
                  href={attachment.url}
                  className="notification-panel__item-attachment"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {attachment.name}
                </a>
              ))}
            </div>
          </div>
        )}
        
        {notification.actionUrl && notification.actionLabel && (
          <div className="notification-panel__item-action-section">
            <a
              href={notification.actionUrl}
              className="notification-panel__item-action-link"
            >
              {notification.actionLabel}
            </a>
          </div>
        )}
      </div>
    </div>
  );
};

interface NotificationStatsProps {
  stats: NotificationStats;
  variant: 'sidebar' | 'dropdown' | 'page';
}

const NotificationStats: React.FC<NotificationStatsProps> = ({ stats, variant }) => {
  if (variant === 'dropdown') return null;
  
  return (
    <div className="notification-panel__stats">
      <div className="notification-panel__stats-summary">
        <div className="notification-panel__stats-item">
          <span className="notification-panel__stats-value">{stats.total}</span>
          <span className="notification-panel__stats-label">Total</span>
        </div>
        
        <div className="notification-panel__stats-item notification-panel__stats-item--unread">
          <span className="notification-panel__stats-value">{stats.unread}</span>
          <span className="notification-panel__stats-label">Unread</span>
        </div>
        
        <div className="notification-panel__stats-item">
          <span className="notification-panel__stats-value">{stats.total - stats.unread}</span>
          <span className="notification-panel__stats-label">Read</span>
        </div>
      </div>
      
      {variant === 'page' && (
        <div className="notification-panel__stats-breakdown">
          <div className="notification-panel__stats-section">
            <h4 className="notification-panel__stats-section-title">By Priority</h4>
            <div className="notification-panel__stats-section-items">
              {Object.entries(stats.byPriority).map(([priority, count]) => (
                <div key={priority} className="notification-panel__stats-section-item">
                  <Badge variant={getPriorityColor(priority) as any} size="small">
                    {priority}
                  </Badge>
                  <span className="notification-panel__stats-section-count">{count}</span>
                </div>
              ))}
            </div>
          </div>
          
          <div className="notification-panel__stats-section">
            <h4 className="notification-panel__stats-section-title">By Type</h4>
            <div className="notification-panel__stats-section-items">
              {Object.entries(stats.byType).map(([type, count]) => (
                <div key={type} className="notification-panel__stats-section-item">
                  <Badge variant={getTypeColor(type) as any} size="small">
                    {type}
                  </Badge>
                  <span className="notification-panel__stats-section-count">{count}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Main NotificationPanel Component
export const NotificationPanel: React.FC<NotificationPanelProps> = ({
  className = '',
  variant = 'sidebar',
  maxHeight = '400px',
  showFilters = false,
  showMarkAllRead = true,
  onNotificationClick,
  onMarkAsRead,
  onMarkAllAsRead,
  onDeleteNotification,
  onClearAll
}) => {
  const [filters, setFilters] = useState<NotificationFilters>({});
  
  // Mock data - in real implementation, this would come from a hook
  const mockNotifications: Notification[] = [
    {
      id: 'notif-1',
      type: 'assignment',
      category: 'trainee',
      title: 'New Training Assignment',
      message: 'You have been assigned to the "Advanced JavaScript" training program.',
      description: 'This comprehensive program covers advanced JavaScript concepts including async/await, closures, and modern ES6+ features. The program duration is 4 weeks with hands-on projects.',
      priority: 'high',
      read: false,
      createdAt: '2024-02-10T09:00:00Z',
      actionUrl: '/programs/advanced-javascript',
      actionLabel: 'View Program',
      sender: {
        id: 'hr-1',
        name: 'Sarah Johnson',
        avatar: '/avatars/sarah-johnson.jpg',
        role: 'HR Manager',
        type: 'user'
      },
      recipients: ['trainee-1'],
      metadata: {
        traineeId: 'trainee-1',
        traineeName: 'John Doe',
        programId: 'prog-1',
        programName: 'Advanced JavaScript'
      }
    },
    {
      id: 'notif-2',
      type: 'reminder',
      category: 'assessment',
      title: 'Assessment Due Tomorrow',
      message: 'Your technical assessment for "React Fundamentals" is due tomorrow at 5:00 PM.',
      priority: 'urgent',
      read: false,
      createdAt: '2024-02-09T14:30:00Z',
      expiresAt: '2024-02-11T17:00:00Z',
      actionUrl: '/assessments/react-fundamentals',
      actionLabel: 'Take Assessment',
      sender: {
        id: 'system',
        name: 'System',
        role: 'Automated Reminder',
        type: 'system'
      },
      recipients: ['trainee-1'],
      metadata: {
        assessmentId: 'assess-1',
        traineeId: 'trainee-1',
        traineeName: 'John Doe'
      }
    },
    {
      id: 'notif-3',
      type: 'approval',
      category: 'review',
      title: 'Review Approval Required',
      message: 'Please review and approve the quarterly assessment for John Doe.',
      priority: 'medium',
      read: true,
      createdAt: '2024-02-08T11:15:00Z',
      readAt: '2024-02-08T15:30:00Z',
      actionUrl: '/reviews/quarterly-q1-2024',
      actionLabel: 'Review Assessment',
      sender: {
        id: 'manager-1',
        name: 'Michael Chen',
        avatar: '/avatars/michael-chen.jpg',
        role: 'Team Lead',
        type: 'user'
      },
      recipients: ['hr-1', 'manager-2'],
      metadata: {
        reviewId: 'review-1',
        traineeId: 'trainee-1',
        traineeName: 'John Doe'
      }
    },
    {
      id: 'notif-4',
      type: 'success',
      category: 'program',
      title: 'Program Completed',
      message: 'Congratulations! You have successfully completed the "Git & Version Control" program.',
      priority: 'low',
      read: true,
      createdAt: '2024-02-07T16:45:00Z',
      readAt: '2024-02-07T17:00:00Z',
      actionUrl: '/certificates/git-version-control',
      actionLabel: 'View Certificate',
      sender: {
        id: 'system',
        name: 'System',
        role: 'Automated Notification',
        type: 'system'
      },
      recipients: ['trainee-1'],
      metadata: {
        programId: 'prog-2',
        programName: 'Git & Version Control',
        traineeId: 'trainee-1',
        traineeName: 'John Doe'
      }
    },
    {
      id: 'notif-5',
      type: 'info',
      category: 'system',
      title: 'System Maintenance',
      message: 'Scheduled system maintenance will occur on Sunday, February 11th from 2:00 AM to 4:00 AM.',
      description: 'During this time, the platform will be temporarily unavailable. Please save your work and log out before the maintenance window.',
      priority: 'medium',
      read: false,
      createdAt: '2024-02-06T10:00:00Z',
      sender: {
        id: 'admin',
        name: 'System Administrator',
        role: 'Admin',
        type: 'system'
      },
      recipients: ['all'],
      metadata: {
        customData: {
          maintenanceStart: '2024-02-11T02:00:00Z',
          maintenanceEnd: '2024-02-11T04:00:00Z'
        }
      }
    }
  ];
  
  // Filter notifications
  const filteredNotifications = useMemo(() => {
    return mockNotifications.filter((notification) => {
      if (filters.type && !filters.type.includes(notification.type)) return false;
      if (filters.category && !filters.category.includes(notification.category)) return false;
      if (filters.priority && !filters.priority.includes(notification.priority)) return false;
      if (filters.read !== undefined && notification.read !== filters.read) return false;
      return true;
    });
  }, [filters]);
  
  // Calculate stats
  const stats = useMemo((): NotificationStats => {
    const total = filteredNotifications.length;
    const unread = filteredNotifications.filter(n => !n.read).length;
    
    const byType = filteredNotifications.reduce((acc, n) => {
      acc[n.type] = (acc[n.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const byCategory = filteredNotifications.reduce((acc, n) => {
      acc[n.category] = (acc[n.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const byPriority = filteredNotifications.reduce((acc, n) => {
      acc[n.priority] = (acc[n.priority] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    return { total, unread, byType, byCategory, byPriority };
  }, [filteredNotifications]);
  
  const handleMarkAllAsRead = () => {
    if (onMarkAllAsRead) {
      onMarkAllAsRead();
    }
  };
  
  const handleClearAll = () => {
    if (onClearAll) {
      onClearAll();
    }
  };
  
  const containerClasses = [
    'notification-panel',
    `notification-panel--${variant}`,
    className
  ].filter(Boolean).join(' ');
  
  const contentStyle = {
    maxHeight: variant === 'dropdown' ? maxHeight : undefined
  };
  
  return (
    <div className={containerClasses}>
      <div className="notification-panel__header">
        <div className="notification-panel__title-section">
          <h3 className="notification-panel__title">Notifications</h3>
          
          {stats.unread > 0 && (
            <Badge variant="error" size="small">
              {stats.unread} new
            </Badge>
          )}
        </div>
        
        <div className="notification-panel__header-actions">
          {showMarkAllRead && stats.unread > 0 && (
            <button
              onClick={handleMarkAllAsRead}
              className="notification-panel__header-action"
              title="Mark all as read"
            >
              Mark All Read
            </button>
          )}
          
          {onClearAll && variant === 'page' && (
            <button
              onClick={handleClearAll}
              className="notification-panel__header-action notification-panel__header-action--danger"
              title="Clear all notifications"
            >
              Clear All
            </button>
          )}
        </div>
      </div>
      
      <NotificationStats stats={stats} variant={variant} />
      
      {showFilters && (
        <NotificationFilters
          filters={filters}
          onFiltersChange={setFilters}
          stats={stats}
        />
      )}
      
      <div className="notification-panel__content" style={contentStyle}>
        {filteredNotifications.length === 0 ? (
          <div className="notification-panel__empty">
            <div className="notification-panel__empty-icon">📭</div>
            <div className="notification-panel__empty-message">
              {Object.keys(filters).length > 0 ? 'No notifications match your filters' : 'No notifications yet'}
            </div>
          </div>
        ) : (
          <div className="notification-panel__list">
            {filteredNotifications.map((notification) => (
              <NotificationItem
                key={notification.id}
                notification={notification}
                variant={variant}
                onNotificationClick={onNotificationClick}
                onMarkAsRead={onMarkAsRead}
                onDeleteNotification={onDeleteNotification}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default NotificationPanel;