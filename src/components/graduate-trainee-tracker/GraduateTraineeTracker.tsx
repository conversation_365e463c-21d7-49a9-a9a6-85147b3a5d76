import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  GraduateTraineeTrackerProps,
  GraduateTraineeTrackerState,
  User,
  UserRole,
  Trainee,
  TrainingProgram,
  QuarterlyReview,
  ApprovalWorkflow,
  Notification,
  TraineeStatus,
  GoalCategory,
  GoalStatus,
  MilestoneStatus,
  ResourceType,
  SkillAssessment,
  Competency,
  CompetencyCategory,
  CompetencyLevel,
  ReviewType,
  ReviewStatus,
  AssessorType,
  ApprovalStatus,
  StageStatus,
  ApproverType,
  DevelopmentGoal,
  ActionItem,
  ActionItemStatus,
  Priority,
  Feedback,
  FeedbackCategory,
  ProgramStatus,
  NotificationType,
  NotificationPriority
} from './types';
import { TraineeTracker } from './Trainees/TraineeTracker';
import { ProgramManagement } from './Programs/ProgramManagement';
import { ReviewSystem } from './Reviews/ReviewSystem';
import { ApprovalWorkflow as ApprovalWorkflowComponent } from './Approvals/ApprovalWorkflow';
import { Dashboard } from './Dashboard/Dashboard';
import { NotificationCenter } from './Notifications/NotificationCenter';
import { Sidebar } from './components/Sidebar';
import { LoadingSpinner } from '../ui/LoadingSpinner';
import { ErrorBoundary } from '../common/ErrorBoundary';

type ViewType = 'dashboard' | 'trainees' | 'programs' | 'reviews' | 'approvals' | 'analytics' | 'notifications' | 'people' | 'school' | 'assessment';

const GraduateTraineeTracker: React.FC<GraduateTraineeTrackerProps> = ({ currentUser, onNavigate }) => {
  const [state, setState] = useState<GraduateTraineeTrackerState>({
    currentUser: null,
    trainees: [],
    programs: [],
    reviews: [],
    workflows: [],
    notifications: [],
    competencies: [],
    loading: true,
    error: null,
    selectedTrainee: null,
    selectedProgram: null,
    filters: {
      status: [],
      department: [],
      program: [],
      mentor: [],
      dateRange: {
        startDate: '',
        endDate: ''
      }
    },
    pagination: {
      page: 1,
      pageSize: 10,
      total: 0
    }
  });

  const [activeView, setActiveView] = useState<ViewType>('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(true);

  // Mock API functions
  const fetchTrainees = async (): Promise<Trainee[]> => {
    // Mock data for trainees
    return [
      {
        id: 'trainee-001',
        employeeId: 'EMP001',
        firstName: 'Sarah',
        lastName: 'Johnson',
        email: '<EMAIL>',
        department: 'Engineering',
        startDate: '2024-01-15',
        endDate: '2024-12-15',
        programId: 'program-001',
        mentorId: 'mentor-001',
        status: TraineeStatus.ACTIVE,
        currentStage: 3,
        profilePicture: 'https://images.unsplash.com/photo-1494790108755-2616b332-3c5f-4b5a-9b3a-5b3a5b3a5b3a',
        phoneNumber: '******-0123',
        emergencyContact: {
          name: 'John Johnson',
          relationship: 'Father',
          phoneNumber: '******-0124',
          email: '<EMAIL>'
        },
        skillsMatrix: [
          {
            competencyId: 'comp-001',
            currentLevel: 3,
            targetLevel: 4,
            assessmentDate: '2024-03-15',
            assessorId: 'mentor-001',
            evidence: ['Project Alpha deliverables', 'Code review feedback'],
            developmentPlan: 'Complete advanced React course and lead 2 sprint planning sessions'
          },
          {
            competencyId: 'comp-002',
            currentLevel: 2,
            targetLevel: 4,
            assessmentDate: '2024-03-15',
            assessorId: 'mentor-001',
            evidence: ['Team standup facilitation', 'Stakeholder presentation'],
            developmentPlan: 'Attend leadership workshop and mentor junior developers'
          }
        ],
        goals: [
          {
            id: 'goal-001',
            title: 'Master React Development',
            description: 'Become proficient in React, Redux, and modern JavaScript',
            category: GoalCategory.SKILL_DEVELOPMENT,
            targetDate: '2024-06-30',
            status: GoalStatus.IN_PROGRESS,
            progress: 65,
            milestones: [
              {
                id: 'milestone-001',
                title: 'Complete React Fundamentals',
                targetDate: '2024-03-30',
                status: MilestoneStatus.COMPLETED,
                progress: 100
              },
              {
                id: 'milestone-002',
                title: 'Build production-ready application',
                targetDate: '2024-05-30',
                status: MilestoneStatus.IN_PROGRESS,
                progress: 45
              }
            ],
            resources: [
              {
                id: 'res-001',
                title: 'React Documentation',
                type: ResourceType.DOCUMENT,
                url: 'https://react.dev',
                description: 'Official React documentation',
                isRequired: true
              }
            ],
            createdAt: '2024-01-15',
            updatedAt: '2024-03-15'
          }
        ],
        reviews: [],
        createdAt: '2024-01-15',
        updatedAt: '2024-03-15'
      },
      {
        id: 'trainee-002',
        employeeId: 'EMP002',
        firstName: 'Michael',
        lastName: 'Chen',
        email: '<EMAIL>',
        department: 'Data Science',
        startDate: '2024-02-01',
        endDate: '2025-01-31',
        programId: 'program-002',
        mentorId: 'mentor-002',
        status: TraineeStatus.ACTIVE,
        currentStage: 2,
        profilePicture: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d',
        phoneNumber: '******-0125',
        emergencyContact: {
          name: 'Lisa Chen',
          relationship: 'Mother',
          phoneNumber: '******-0126',
          email: '<EMAIL>'
        },
        skillsMatrix: [
          {
            competencyId: 'comp-003',
            currentLevel: 2,
            targetLevel: 4,
            assessmentDate: '2024-03-20',
            assessorId: 'mentor-002',
            evidence: ['Python data analysis project', 'Machine learning model'],
            developmentPlan: 'Complete advanced statistics course and Kaggle competitions'
          }
        ],
        goals: [
          {
            id: 'goal-002',
            title: 'Master Data Science Pipeline',
            description: 'Build end-to-end data science solutions',
            category: GoalCategory.SKILL_DEVELOPMENT,
            targetDate: '2024-08-31',
            status: GoalStatus.IN_PROGRESS,
            progress: 40,
            milestones: [
              {
                id: 'milestone-003',
                title: 'Complete Python for Data Science',
                targetDate: '2024-04-30',
                status: MilestoneStatus.IN_PROGRESS,
                progress: 70
              }
            ],
            resources: [
              {
                id: 'res-002',
                title: 'Python Data Science Handbook',
                type: ResourceType.DOCUMENT,
                description: 'Comprehensive guide to data science with Python',
                isRequired: true
              }
            ],
            createdAt: '2024-02-01',
            updatedAt: '2024-03-20'
          }
        ],
        reviews: [],
        createdAt: '2024-02-01',
        updatedAt: '2024-03-20'
      }
    ];
  };

  const fetchPrograms = async (): Promise<TrainingProgram[]> => {
    return [
      {
        id: 'program-001',
        title: 'Software Engineering Graduate Program',
        description: 'Comprehensive 12-month program for aspiring software engineers',
        duration: 12,
        objectives: [
          'Master modern web development technologies',
          'Develop strong problem-solving skills',
          'Build production-ready applications',
          'Learn agile development practices'
        ],
        competencies: [
          {
            id: 'comp-001',
            name: 'React Development',
            description: 'Proficiency in React framework and ecosystem',
            category: CompetencyCategory.TECHNICAL,
            level: CompetencyLevel.PROFICIENT,
            weight: 0.25,
            assessmentCriteria: [
              'Component design and architecture',
              'State management patterns',
              'Performance optimization',
              'Testing strategies'
            ]
          },
          {
            id: 'comp-002',
            name: 'Team Collaboration',
            description: 'Effective teamwork and communication skills',
            category: CompetencyCategory.BEHAVIORAL,
            level: CompetencyLevel.ADVANCED,
            weight: 0.20,
            assessmentCriteria: [
              'Active participation in team activities',
              'Clear communication of technical concepts',
              'Constructive feedback delivery',
              'Conflict resolution skills'
            ]
          }
        ],
        milestones: [
          {
            id: 'milestone-001',
            title: 'Orientation & Setup',
            description: 'Complete onboarding and development environment setup',
            targetDate: '2024-01-31',
            status: MilestoneStatus.COMPLETED,
            deliverables: ['Development environment', 'Access credentials', 'Initial project setup'],
            dependencies: []
          },
          {
            id: 'milestone-002',
            title: 'Frontend Fundamentals',
            description: 'Master HTML, CSS, and JavaScript fundamentals',
            targetDate: '2024-03-15',
            status: MilestoneStatus.IN_PROGRESS,
            deliverables: ['Portfolio website', 'Interactive web applications', 'Code samples'],
            dependencies: ['Orientation & Setup']
          }
        ],
        resources: [
          {
            id: 'res-001',
            title: 'React Official Documentation',
            type: ResourceType.DOCUMENT,
            url: 'https://react.dev',
            description: 'Comprehensive React documentation and tutorials',
            isRequired: true
          },
          {
            id: 'res-002',
            title: 'JavaScript: The Good Parts',
            type: ResourceType.DOCUMENT,
            description: 'Essential JavaScript concepts and best practices',
            isRequired: true
          }
        ],
        status: ProgramStatus.ACTIVE,
        approvalWorkflow: [
          {
            id: 'stage-001',
            stageNumber: 1,
            stageName: 'LD Officer Review',
            approverType: ApproverType.LD_OFFICER,
            approverId: 'ld-001',
            status: StageStatus.APPROVED,
            approvedAt: '2024-01-10'
          }
        ],
        createdBy: 'admin-001',
        createdAt: '2024-01-01',
        updatedAt: '2024-03-15',
        maxParticipants: 20
      },
      {
        id: 'program-002',
        title: 'Data Science Graduate Program',
        description: 'Intensive program for data science and machine learning',
        duration: 12,
        objectives: [
          'Master Python and R for data analysis',
          'Build machine learning models',
          'Develop data visualization skills',
          'Learn big data technologies'
        ],
        competencies: [
          {
            id: 'comp-003',
            name: 'Python Programming',
            description: 'Advanced Python programming for data science',
            category: CompetencyCategory.TECHNICAL,
            level: CompetencyLevel.ADVANCED,
            weight: 0.30,
            assessmentCriteria: [
              'Data manipulation with pandas',
              'Statistical analysis with scipy',
              'Machine learning with scikit-learn',
              'Deep learning with tensorflow'
            ]
          }
        ],
        milestones: [
          {
            id: 'milestone-003',
            title: 'Python Fundamentals',
            description: 'Complete Python programming fundamentals',
            targetDate: '2024-03-30',
            status: MilestoneStatus.IN_PROGRESS,
            deliverables: ['Python scripts', 'Data analysis projects', 'Code repositories'],
            dependencies: []
          }
        ],
        resources: [
          {
            id: 'res-003',
            title: 'Python for Data Analysis',
            type: ResourceType.DOCUMENT,
            description: 'Comprehensive guide to data analysis with Python',
            isRequired: true
          }
        ],
        status: ProgramStatus.ACTIVE,
        approvalWorkflow: [
          {
            id: 'stage-002',
            stageNumber: 1,
            stageName: 'Technical Lead Approval',
            approverType: ApproverType.MANAGER,
            approverId: 'manager-001',
            status: StageStatus.APPROVED,
            approvedAt: '2024-01-15'
          }
        ],
        createdBy: 'admin-001',
        createdAt: '2024-01-05',
        updatedAt: '2024-03-20',
        maxParticipants: 15
      }
    ];
  };

  const fetchReviews = async (): Promise<QuarterlyReview[]> => {
    return [
      {
        id: 'review-001',
        traineeId: 'trainee-001',
        reviewerId: 'mentor-001',
        type: ReviewType.QUARTERLY,
        quarter: 1,
        year: 2024,
        reviewPeriod: {
          quarter: 1,
          year: 2024,
          startDate: '2024-01-01',
          endDate: '2024-03-31'
        },
        scheduledDate: '2024-03-30',
        dueDate: '2024-03-30',
        completedDate: '2024-03-28',
        status: ReviewStatus.COMPLETED,
        selfAssessment: {
          id: 'assessment-001',
          assessorId: 'trainee-001',
          assessorType: AssessorType.SELF,
          competencyRatings: [
            {
              competencyId: 'comp-001',
              rating: 3,
              evidence: ['Project deliverables', 'Code reviews'],
              comments: 'Good progress on React skills'
            }
          ],
          overallRating: 3.5,
          strengths: ['Quick learner', 'Good problem solver'],
          areasForImprovement: ['Needs more experience with large codebases'],
          comments: 'Making good progress overall',
          recommendations: ['Continue with advanced React patterns'],
          completedAt: '2024-03-28'
        },
        mentorAssessment: {
          id: 'assessment-002',
          assessorId: 'mentor-001',
          assessorType: AssessorType.MENTOR,
          competencyRatings: [
            {
              competencyId: 'comp-001',
              rating: 3,
              evidence: ['Code quality', 'Team collaboration'],
              comments: 'Shows good understanding of concepts'
            }
          ],
          overallRating: 3.5,
          strengths: ['Strong technical aptitude', 'Good communication'],
          areasForImprovement: ['Could improve on code documentation'],
          comments: 'Excellent progress for first quarter',
          recommendations: ['Focus on testing strategies', 'Improve documentation'],
          completedAt: '2024-03-28'
        },
        overallRating: 3.5,
        feedback: [
          {
            id: 'feedback-001',
            authorId: 'mentor-001',
            authorType: AssessorType.MENTOR,
            content: 'Great progress on React fundamentals. Keep pushing yourself!',
            category: FeedbackCategory.POSITIVE,
            isPrivate: false,
            createdAt: '2024-03-28'
          }
        ],
        actionItems: [
          {
            id: 'action-001',
            title: 'Complete React Testing Course',
            description: 'Complete comprehensive testing course for React applications',
            assigneeId: 'trainee-001',
            dueDate: '2024-04-30',
            status: ActionItemStatus.PENDING,
            priority: Priority.HIGH,
            createdAt: '2024-03-28'
          }
        ],
        nextReviewDate: '2024-06-30',
        overallScore: 3.5,
        createdAt: '2024-03-28',
        updatedAt: '2024-03-28'
      }
    ];
  };

  const fetchWorkflows = async (): Promise<ApprovalWorkflow[]> => {
    return [
      {
        id: 'workflow-001',
        programId: 'program-001',
        traineeId: 'trainee-001',
        currentStage: 2,
        stages: [
          {
            id: 'stage-001',
            stageNumber: 1,
            stageName: 'Mentor Approval',
            approverType: ApproverType.MENTOR,
            approverId: 'mentor-001',
            status: StageStatus.APPROVED,
            approvedAt: '2024-03-15',
            comments: 'Trainee is ready for next stage'
          },
          {
            id: 'stage-002',
            stageNumber: 2,
            stageName: 'Manager Review',
            approverType: ApproverType.MANAGER,
            approverId: 'manager-001',
            status: StageStatus.IN_PROGRESS,
            comments: 'Under review'
          },
          {
            id: 'stage-003',
            stageNumber: 3,
            stageName: 'LD Officer Final Approval',
            approverType: ApproverType.LD_OFFICER,
            approverId: 'ld-001',
            status: StageStatus.PENDING
          }
        ],
        status: ApprovalStatus.IN_PROGRESS,
        initiatedBy: 'trainee-001',
        initiatedAt: '2024-03-15',
        completedAt: undefined
      },
      {
        id: 'workflow-002',
        programId: 'program-002',
        traineeId: 'trainee-002',
        currentStage: 1,
        stages: [
          {
            id: 'stage-004',
            stageNumber: 1,
            stageName: 'Mentor Approval',
            approverType: ApproverType.MENTOR,
            approverId: 'mentor-002',
            status: StageStatus.PENDING
          },
          {
            id: 'stage-005',
            stageNumber: 2,
            stageName: 'Technical Lead Review',
            approverType: ApproverType.MANAGER,
            approverId: 'manager-002',
            status: StageStatus.PENDING
          }
        ],
        status: ApprovalStatus.PENDING,
        initiatedBy: 'trainee-002',
        initiatedAt: '2024-03-20',
        completedAt: undefined
      },
      {
        id: 'workflow-003',
        programId: 'program-001',
        traineeId: 'trainee-003',
        currentStage: 3,
        stages: [
          {
            id: 'stage-006',
            stageNumber: 1,
            stageName: 'Mentor Approval',
            approverType: ApproverType.MENTOR,
            approverId: 'mentor-003',
            status: StageStatus.APPROVED,
            approvedAt: '2024-03-10'
          },
          {
            id: 'stage-007',
            stageNumber: 2,
            stageName: 'Manager Review',
            approverType: ApproverType.MANAGER,
            approverId: 'manager-003',
            status: StageStatus.APPROVED,
            approvedAt: '2024-03-12'
          },
          {
            id: 'stage-008',
            stageNumber: 3,
            stageName: 'Final Approval',
            approverType: ApproverType.LD_OFFICER,
            approverId: 'ld-003',
            status: StageStatus.APPROVED,
            approvedAt: '2024-03-14'
          }
        ],
        status: ApprovalStatus.APPROVED,
        initiatedBy: 'trainee-003',
        initiatedAt: '2024-03-10',
        completedAt: '2024-03-14'
      }
    ];
  };

  const fetchNotifications = async (): Promise<Notification[]> => {
    return [
      {
        id: 'notification-001',
        userId: 'trainee-001',
        recipientId: 'trainee-001',
        type: NotificationType.REVIEW_DUE,
        title: 'Q1 Review Due',
        message: 'Your Q1 quarterly review is scheduled for March 30th',
        priority: NotificationPriority.HIGH,
        isRead: false,
        read: false,
        actionRequired: true,
        actionUrl: '/reviews/q1-2024',
        createdAt: '2024-03-25',
        expiresAt: '2024-03-30'
      },
      {
        id: 'notification-002',
        userId: 'mentor-001',
        recipientId: 'mentor-001',
        type: NotificationType.APPROVAL_REQUEST,
        title: 'Review Assessment Required',
        message: 'Sarah Johnson Q1 review needs your assessment',
        priority: NotificationPriority.MEDIUM,
        isRead: false,
        read: false,
        actionRequired: true,
        actionUrl: '/reviews/assess/review-001',
        createdAt: '2024-03-26',
        expiresAt: '2024-03-30'
      }
    ];
  };

  const fetchCompetencies = async (): Promise<Competency[]> => {
    return [
      {
        id: 'comp-001',
        name: 'React Development',
        description: 'Proficiency in React framework and ecosystem',
        category: CompetencyCategory.TECHNICAL,
        level: CompetencyLevel.PROFICIENT,
        weight: 0.25,
        assessmentCriteria: [
          'Component design and architecture',
          'State management patterns',
          'Performance optimization',
          'Testing strategies'
        ]
      },
      {
        id: 'comp-002',
        name: 'Team Collaboration',
        description: 'Effective teamwork and communication skills',
        category: CompetencyCategory.BEHAVIORAL,
        level: CompetencyLevel.ADVANCED,
        weight: 0.20,
        assessmentCriteria: [
          'Active participation in team activities',
          'Clear communication of technical concepts',
          'Constructive feedback delivery',
          'Conflict resolution skills'
        ]
      },
      {
        id: 'comp-003',
        name: 'Python Programming',
        description: 'Advanced Python programming for data science',
        category: CompetencyCategory.TECHNICAL,
        level: CompetencyLevel.ADVANCED,
        weight: 0.30,
        assessmentCriteria: [
          'Data manipulation with pandas',
          'Statistical analysis with scipy',
          'Machine learning with scikit-learn',
          'Deep learning with tensorflow'
        ]
      }
    ];
  };

  useEffect(() => {
    const loadData = async () => {
      try {
        setState(prev => ({ ...prev, loading: true }));
        
        const [trainees, programs, reviews, workflows, notifications, competencies] = await Promise.all([
          fetchTrainees(),
          fetchPrograms(),
          fetchReviews(),
          fetchWorkflows(),
          fetchNotifications(),
          fetchCompetencies()
        ]);

        setState(prev => ({
          ...prev,
          trainees,
          programs,
          reviews,
          workflows,
          notifications,
          competencies,
          loading: false,
          currentUser
        }));
      } catch (error) {
        setState(prev => ({
          ...prev,
          loading: false,
          error: error instanceof Error ? error.message : 'Failed to load data'
        }));
      }
    };

    loadData();
  }, [currentUser]);

  const handleViewChange = (view: ViewType) => {
    setActiveView(view);
  };

  const renderView = () => {
    switch (activeView) {
      case 'dashboard':
        return (
          <Dashboard
            currentUser={state.currentUser!}
            state={state}
            onStateUpdate={setState}
          />
        );
      case 'trainees':
        return (
          <TraineeTracker
            trainees={state.trainees}
            programs={state.programs}
            currentUser={state.currentUser!}
            onUpdateTrainee={(id, updates) => {
              setState(prev => ({
                ...prev,
                trainees: prev.trainees.map(t => 
                  t.id === id ? { ...t, ...updates, updatedAt: new Date().toISOString() } : t
                )
              }));
            }}
          />
        );
      case 'programs':
        return (
          <ProgramManagement
            programs={state.programs}
            onCreateProgram={(program) => {
              const newProgram: TrainingProgram = {
                id: `program-${Date.now()}`,
                title: program.title || 'New Program',
                description: program.description || '',
                duration: program.duration || 12,
                objectives: program.objectives || [],
                competencies: program.competencies || [],
                milestones: program.milestones || [],
                resources: program.resources || [],
                status: program.status || ProgramStatus.DRAFT,
                approvalWorkflow: program.approvalWorkflow || [],
                createdBy: currentUser?.id || 'unknown',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                maxParticipants: program.maxParticipants || 20
              };
              setState(prev => ({
                ...prev,
                programs: [...prev.programs, newProgram]
              }));
            }}
            onUpdateProgram={(id, updates) => {
              setState(prev => ({
                ...prev,
                programs: prev.programs.map(p => 
                  p.id === id ? { ...p, ...updates, updatedAt: new Date().toISOString() } : p
                )
              }));
            }}
            onDeleteProgram={(id) => {
              setState(prev => ({
                ...prev,
                programs: prev.programs.filter(p => p.id !== id)
              }));
            }}
          />
        );
      case 'reviews':
        return (
          <ReviewSystem
            reviews={state.reviews}
            trainees={state.trainees}
            currentUser={state.currentUser!}
            onSubmitReview={(review) => {
              setState(prev => ({
                ...prev,
                reviews: [...prev.reviews, review]
              }));
            }}
          />
        );
      case 'approvals':
        return (
          <ApprovalWorkflowComponent
            workflows={state.workflows}
            currentUser={state.currentUser!}
            onApprove={(workflowId, stageId, comments) => {
              setState(prev => ({
                ...prev,
                workflows: prev.workflows.map(w => 
                  w.id === workflowId 
                    ? {
                        ...w,
                        stages: w.stages.map(s => 
                          s.id === stageId 
                            ? { ...s, status: StageStatus.APPROVED, approvedAt: new Date().toISOString(), comments }
                            : s
                        ),
                        status: w.stages.every(s => s.status === StageStatus.APPROVED) 
                          ? ApprovalStatus.APPROVED 
                          : w.status
                      }
                    : w
                )
              }));
            }}
            onReject={(workflowId, stageId, reason) => {
              setState(prev => ({
                ...prev,
                workflows: prev.workflows.map(w => 
                  w.id === workflowId 
                    ? {
                        ...w,
                        stages: w.stages.map(s => 
                          s.id === stageId 
                            ? { ...s, status: StageStatus.REJECTED, rejectedAt: new Date().toISOString(), rejectionReason: reason }
                            : s
                        ),
                        status: ApprovalStatus.REJECTED
                      }
                    : w
                )
              }));
            }}
          />
        );
      case 'notifications':
        return (
          <NotificationCenter
            notifications={state.notifications}
            currentUser={state.currentUser!}
            onMarkAsRead={(notificationId) => {
              setState(prev => ({
                ...prev,
                notifications: prev.notifications.map(n => 
                  n.id === notificationId ? { ...n, isRead: true, read: true } : n
                )
              }));
            }}
          />
        );
      case 'analytics':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold mb-4">Analytics Dashboard</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-white p-4 rounded-lg shadow">
                <h3 className="text-lg font-semibold">Total Trainees</h3>
                <p className="text-3xl font-bold text-blue-600">{state.trainees.length}</p>
              </div>
              <div className="bg-white p-4 rounded-lg shadow">
                <h3 className="text-lg font-semibold">Active Programs</h3>
                <p className="text-3xl font-bold text-green-600">
                  {state.programs.filter(p => p.status === ProgramStatus.ACTIVE).length}
                </p>
              </div>
              <div className="bg-white p-4 rounded-lg shadow">
                <h3 className="text-lg font-semibold">Completed Reviews</h3>
                <p className="text-3xl font-bold text-purple-600">
                  {state.reviews.filter(r => r.status === ReviewStatus.COMPLETED).length}
                </p>
              </div>
              <div className="bg-white p-4 rounded-lg shadow">
                <h3 className="text-lg font-semibold">Pending Approvals</h3>
                <p className="text-3xl font-bold text-orange-600">
                  {state.workflows.filter(w => w.status === ApprovalStatus.PENDING).length}
                </p>
              </div>
            </div>
          </div>
        );
      case 'people':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold mb-6">People Management</h2>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Trainees List */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold mb-4">Trainees</h3>
                <div className="space-y-3">
                  {state.trainees.map(trainee => (
                    <div key={trainee.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 font-semibold">
                            {trainee.firstName[0]}{trainee.lastName[0]}
                          </span>
                        </div>
                        <div>
                          <p className="font-medium">{trainee.firstName} {trainee.lastName}</p>
                          <p className="text-sm text-gray-500">{trainee.email}</p>
                        </div>
                      </div>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        trainee.status === TraineeStatus.ACTIVE ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                      }`}>
                        {trainee.status}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
              
              {/* Mentors/Staff List */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold mb-4">Mentors & Staff</h3>
                <div className="space-y-3">
                  {/* Mock mentors/staff data for now */}
                  {[].map((user: any) => (
                    <div key={user.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                          <span className="text-purple-600 font-semibold">
                            {user.firstName[0]}{user.lastName[0]}
                          </span>
                        </div>
                        <div>
                          <p className="font-medium">{user.firstName} {user.lastName}</p>
                          <p className="text-sm text-gray-500">{user.role}</p>
                        </div>
                      </div>
                      <button className="text-blue-600 hover:text-blue-800 text-sm">
                        View Profile
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        );
      case 'school':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold mb-6">Training Management</h2>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
              <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-6 rounded-lg">
                <h3 className="text-lg font-semibold mb-2">Active Sessions</h3>
                <p className="text-3xl font-bold">12</p>
              </div>
              <div className="bg-gradient-to-r from-green-500 to-green-600 text-white p-6 rounded-lg">
                <h3 className="text-lg font-semibold mb-2">Completed Modules</h3>
                <p className="text-3xl font-bold">48</p>
              </div>
              <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-6 rounded-lg">
                <h3 className="text-lg font-semibold mb-2">Average Score</h3>
                <p className="text-3xl font-bold">85%</p>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-4">Training Programs</h3>
              <div className="space-y-4">
                {state.programs.map(program => (
                  <div key={program.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="font-semibold">{program.title}</h4>
                        <p className="text-sm text-gray-600">{program.description}</p>
                      </div>
                      <span className={`px-3 py-1 text-sm rounded-full ${
                        program.status === ProgramStatus.ACTIVE ? 'bg-green-100 text-green-800' : 
                        program.status === ProgramStatus.ARCHIVED ? 'bg-blue-100 text-blue-800' : 
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {program.status}
                      </span>
                    </div>
                    <div className="mt-3 flex items-center space-x-4 text-sm text-gray-500">
                      <span>Duration: {program.duration} months</span>
                      <span>•</span>
                      <span>Trainees: {state.trainees.filter(t => t.programId === program.id).length}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );
      case 'assessment':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold mb-6">Assessment Center</h2>
            
            <div className="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-white p-4 rounded-lg shadow">
                <h3 className="text-sm font-medium text-gray-500">Pending Assessments</h3>
                <p className="text-2xl font-bold text-orange-600 mt-2">
                  {state.reviews.filter(r => r.status === ReviewStatus.SCHEDULED).length}
                </p>
              </div>
              <div className="bg-white p-4 rounded-lg shadow">
                <h3 className="text-sm font-medium text-gray-500">In Progress</h3>
                <p className="text-2xl font-bold text-blue-600 mt-2">
                  {state.reviews.filter(r => r.status === ReviewStatus.IN_PROGRESS).length}
                </p>
              </div>
              <div className="bg-white p-4 rounded-lg shadow">
                <h3 className="text-sm font-medium text-gray-500">Completed</h3>
                <p className="text-2xl font-bold text-green-600 mt-2">
                  {state.reviews.filter(r => r.status === ReviewStatus.COMPLETED).length}
                </p>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow">
              <div className="p-6">
                <h3 className="text-lg font-semibold mb-4">Recent Assessments</h3>
                <div className="space-y-4">
                  {state.reviews.slice(0, 5).map(review => {
                    const trainee = state.trainees.find(t => t.id === review.traineeId);
                    return (
                      <div key={review.id} className="border rounded-lg p-4">
                        <div className="flex justify-between items-start">
                          <div>
                            <h4 className="font-medium">
                              {trainee ? `${trainee.firstName} ${trainee.lastName}` : 'Unknown Trainee'}
                            </h4>
                            <p className="text-sm text-gray-600">
                              {review.type} - Quarter {review.quarter}
                            </p>
                            <div className="mt-2 flex items-center space-x-4 text-sm">
                              <span className="text-gray-500">
                                Overall Score: <span className="font-semibold">{review.overallScore || 'N/A'}</span>
                              </span>
                              <span className="text-gray-500">
                                Date: {new Date(review.scheduledDate).toLocaleDateString()}
                              </span>
                            </div>
                          </div>
                          <span className={`px-3 py-1 text-xs rounded-full ${
                            review.status === ReviewStatus.COMPLETED ? 'bg-green-100 text-green-800' :
                            review.status === ReviewStatus.IN_PROGRESS ? 'bg-blue-100 text-blue-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {review.status}
                          </span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  if (state.loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (state.error) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-2">Error</h2>
          <p className="text-gray-600">{state.error}</p>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="flex h-screen bg-gray-50">
        <Sidebar
          isOpen={sidebarOpen}
          onToggle={() => setSidebarOpen(!sidebarOpen)}
          activeView={activeView}
          onViewChange={(view) => handleViewChange(view as ViewType)}
          currentUser={state.currentUser!}
        />
        
        <div className="flex-1 flex flex-col overflow-hidden">
          <main className="flex-1 overflow-y-auto">
            <AnimatePresence mode="wait">
              <motion.div
                key={activeView}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className="h-full"
              >
                {renderView()}
              </motion.div>
            </AnimatePresence>
          </main>
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default GraduateTraineeTracker;