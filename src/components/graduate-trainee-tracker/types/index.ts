// Graduate Trainee Tracker - Type Definitions

export interface Trainee {
  id: string;
  employeeId: string;
  firstName: string;
  lastName: string;
  email: string;
  department: string;
  startDate: string;
  endDate: string;
  programId: string;
  mentorId: string;
  status: TraineeStatus;
  currentStage: number;
  profilePicture?: string;
  phoneNumber?: string;
  emergencyContact?: EmergencyContact;
  skillsMatrix: SkillAssessment[];
  goals: DevelopmentGoal[];
  reviews: QuarterlyReview[];
  createdAt: string;
  updatedAt: string;
}

export interface TrainingProgram {
  id: string;
  title: string;
  description: string;
  duration: number; // in months
  objectives: string[];
  competencies: Competency[];
  milestones: Milestone[];
  resources: Resource[];
  templateId?: string;
  status: ProgramStatus;
  approvalWorkflow: ApprovalStage[];
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  maxParticipants?: number;
}

export interface QuarterlyReview {
  id: string;
  traineeId: string;
  reviewerId: string;
  type: ReviewType;
  quarter: number;
  year: number;
  reviewPeriod: ReviewPeriod;
  scheduledDate: string;
  dueDate: string;
  completedDate?: string;
  status: ReviewStatus;
  selfAssessment?: Assessment;
  mentorAssessment?: Assessment;
  ldOfficerAssessment?: Assessment;
  overallRating: number;
  feedback: Feedback[];
  actionItems: ActionItem[];
  nextReviewDate: string;
  overallScore?: number;
  createdAt: string;
  updatedAt: string;
}

export interface Assessment {
  id: string;
  assessorId: string;
  assessorType: AssessorType;
  competencyRatings: CompetencyRating[];
  overallRating: number;
  strengths: string[];
  areasForImprovement: string[];
  comments: string;
  recommendations: string[];
  completedAt: string;
}

export interface ApprovalWorkflow {
  id: string;
  programId: string;
  traineeId: string;
  currentStage: number;
  stages: ApprovalStage[];
  status: ApprovalStatus;
  initiatedBy: string;
  initiatedAt: string;
  completedAt?: string;
}

export interface ApprovalStage {
  id: string;
  stageNumber: number;
  stageName: string;
  approverType: ApproverType;
  approverId?: string;
  status: StageStatus;
  comments?: string;
  approvedAt?: string;
  rejectedAt?: string;
  rejectionReason?: string;
}

export interface Competency {
  id: string;
  name: string;
  description: string;
  category: CompetencyCategory;
  level: CompetencyLevel;
  weight: number;
  assessmentCriteria: string[];
}

export interface SkillAssessment {
  competencyId: string;
  currentLevel: number;
  targetLevel: number;
  assessmentDate: string;
  assessorId: string;
  evidence: string[];
  developmentPlan: string;
}

export interface DevelopmentGoal {
  id: string;
  title: string;
  description: string;
  category: GoalCategory;
  targetDate: string;
  status: GoalStatus;
  progress: number;
  milestones: GoalMilestone[];
  resources: Resource[];
  createdAt: string;
  updatedAt: string;
}

export interface Milestone {
  id: string;
  title: string;
  description: string;
  targetDate: string;
  status: MilestoneStatus;
  deliverables: string[];
  dependencies: string[];
}

export interface Notification {
  id: string;
  userId: string;
  recipientId: string;
  type: NotificationType;
  title: string;
  message: string;
  priority: NotificationPriority;
  isRead: boolean;
  read: boolean;
  actionRequired: boolean;
  actionUrl?: string;
  createdAt: string;
  expiresAt?: string;
}

export interface User {
  id: string;
  employeeId: string;
  firstName: string;
  lastName: string;
  email: string;
  role: UserRole;
  department: string;
  permissions: Permission[];
  isActive: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

// Enums
export enum TraineeStatus {
  PENDING = 'pending',
  ACTIVE = 'active',
  ON_HOLD = 'on_hold',
  COMPLETED = 'completed',
  TERMINATED = 'terminated'
}

export enum ProgramStatus {
  DRAFT = 'draft',
  PENDING_APPROVAL = 'pending_approval',
  APPROVED = 'approved',
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  ARCHIVED = 'archived'
}

export enum ReviewStatus {
  SCHEDULED = 'scheduled',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  OVERDUE = 'overdue',
  CANCELLED = 'cancelled'
}

export enum ReviewType {
  QUARTERLY = 'quarterly',
  MID_TERM = 'mid_term',
  FINAL = 'final',
  SPECIAL = 'special'
}

export enum ApprovalStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled'
}

export enum StageStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  SKIPPED = 'skipped'
}

export enum AssessorType {
  SELF = 'self',
  MENTOR = 'mentor',
  LD_OFFICER = 'ld_officer',
  MANAGER = 'manager',
  PEER = 'peer'
}

export enum ApproverType {
  TRAINEE = 'trainee',
  MENTOR = 'mentor',
  MANAGER = 'manager',
  LD_OFFICER = 'ld_officer'
}

export enum CompetencyCategory {
  TECHNICAL = 'technical',
  BEHAVIORAL = 'behavioral',
  LEADERSHIP = 'leadership',
  COMMUNICATION = 'communication',
  PROBLEM_SOLVING = 'problem_solving'
}

export enum CompetencyLevel {
  BEGINNER = 1,
  DEVELOPING = 2,
  PROFICIENT = 3,
  ADVANCED = 4,
  EXPERT = 5
}

export enum GoalCategory {
  SKILL_DEVELOPMENT = 'skill_development',
  CAREER_ADVANCEMENT = 'career_advancement',
  PROJECT_DELIVERY = 'project_delivery',
  NETWORKING = 'networking',
  CERTIFICATION = 'certification'
}

export enum GoalStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  ON_HOLD = 'on_hold',
  CANCELLED = 'cancelled'
}

export enum MilestoneStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  OVERDUE = 'overdue',
  CANCELLED = 'cancelled'
}

export enum NotificationType {
  REVIEW_DUE = 'review_due',
  REVIEW_REMINDER = 'review_reminder',
  APPROVAL_REQUEST = 'approval_request',
  MILESTONE_REACHED = 'milestone_reached',
  MILESTONE_ACHIEVED = 'milestone_achieved',
  DEADLINE_APPROACHING = 'deadline_approaching',
  PROGRAM_UPDATE = 'program_update',
  SYSTEM_ALERT = 'system_alert'
}

export enum NotificationPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

export enum UserRole {
  TRAINEE = 'trainee',
  MENTOR = 'mentor',
  LD_OFFICER = 'ld_officer',
  MANAGER = 'manager',
  ADMIN = 'admin'
}

// Supporting interfaces
export interface EmergencyContact {
  name: string;
  relationship: string;
  phoneNumber: string;
  email?: string;
}

export interface ReviewPeriod {
  quarter: number;
  year: number;
  startDate: string;
  endDate: string;
}

export interface CompetencyRating {
  competencyId: string;
  rating: number;
  evidence: string[];
  comments: string;
}

export interface Feedback {
  id: string;
  authorId: string;
  authorType: AssessorType;
  content: string;
  category: FeedbackCategory;
  isPrivate: boolean;
  createdAt: string;
}

export interface ActionItem {
  id: string;
  title: string;
  description: string;
  assigneeId: string;
  dueDate: string;
  status: ActionItemStatus;
  priority: Priority;
  createdAt: string;
  completedAt?: string;
}

export interface GoalMilestone {
  id: string;
  title: string;
  targetDate: string;
  status: MilestoneStatus;
  progress: number;
}

export interface Resource {
  id: string;
  title: string;
  type: ResourceType;
  url?: string;
  description: string;
  isRequired: boolean;
}

export interface Permission {
  id: string;
  name: string;
  description: string;
  module: string;
}

// Additional enums
export enum FeedbackCategory {
  POSITIVE = 'positive',
  CONSTRUCTIVE = 'constructive',
  DEVELOPMENTAL = 'developmental',
  RECOGNITION = 'recognition'
}

export enum ActionItemStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  OVERDUE = 'overdue',
  CANCELLED = 'cancelled'
}

export enum Priority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum ResourceType {
  DOCUMENT = 'document',
  VIDEO = 'video',
  COURSE = 'course',
  TOOL = 'tool',
  REFERENCE = 'reference'
}

// Component Props interfaces
export interface GraduateTraineeTrackerProps {
  currentUser: User;
  onNavigate?: (path: string) => void;
}

export interface TraineeDashboardProps {
  trainee: Trainee;
  program: TrainingProgram;
  upcomingReviews: QuarterlyReview[];
  notifications: Notification[];
  onUpdateProfile: (updates: Partial<Trainee>) => void;
}

export interface ProgramManagementProps {
  programs: TrainingProgram[];
  onCreateProgram: (program: Omit<TrainingProgram, 'id' | 'createdAt' | 'updatedAt'>) => void;
  onUpdateProgram: (id: string, updates: Partial<TrainingProgram>) => void;
  onDeleteProgram: (id: string) => void;
}

export interface ApprovalWorkflowProps {
  workflow: ApprovalWorkflow;
  currentUser: User;
  onApprove: (stageId: string, comments?: string) => void;
  onReject: (stageId: string, reason: string) => void;
}

export interface ReviewSystemProps {
  review: QuarterlyReview;
  trainee: Trainee;
  currentUser: User;
  onSubmitAssessment: (assessment: Omit<Assessment, 'id' | 'completedAt'>) => void;
  onScheduleReview: (reviewId: string, date: string) => void;
}

export interface PerformanceTrackingProps {
  trainee: Trainee;
  reviews: QuarterlyReview[];
  competencies: Competency[];
  onUpdateSkillAssessment: (assessment: SkillAssessment) => void;
}

// State management interfaces
export interface GraduateTraineeTrackerState {
  currentUser: User | null;
  trainees: Trainee[];
  programs: TrainingProgram[];
  reviews: QuarterlyReview[];
  workflows: ApprovalWorkflow[];
  notifications: Notification[];
  competencies: Competency[];
  approvalWorkflows?: ApprovalWorkflow[];
  users?: User[];
  loading: boolean;
  error: string | null;
  selectedTrainee: Trainee | null;
  selectedProgram: TrainingProgram | null;
  filters: FilterState;
  pagination: PaginationState;
}

export interface FilterState {
  status: TraineeStatus[];
  department: string[];
  program: string[];
  mentor: string[];
  dateRange: DateRange;
}

export interface PaginationState {
  page: number;
  pageSize: number;
  total: number;
}

export interface DateRange {
  startDate: string;
  endDate: string;
}

// API response interfaces
export interface ApiResponse<T> {
  data: T;
  message: string;
  success: boolean;
  timestamp: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  message: string;
  success: boolean;
  timestamp: string;
}

// Form interfaces
export interface TraineeFormData {
  firstName: string;
  lastName: string;
  email: string;
  department: string;
  programId: string;
  mentorId: string;
  startDate: string;
  endDate: string;
  emergencyContact?: EmergencyContact;
}

export interface ProgramFormData {
  title: string;
  description: string;
  duration: number;
  objectives: string[];
  competencies: string[];
  resources: Resource[];
}

export interface ReviewFormData {
  competencyRatings: CompetencyRating[];
  overallRating: number;
  strengths: string[];
  areasForImprovement: string[];
  comments: string;
  recommendations: string[];
}

// Analytics interfaces
export interface ProgramAnalytics {
  totalTrainees: number;
  activeTrainees: number;
  completionRate: number;
  averageRating: number;
  performanceTrends: PerformanceTrend[];
  competencyDistribution: CompetencyDistribution[];
}

export interface PerformanceTrend {
  period: string;
  averageRating: number;
  completionRate: number;
  traineeCount: number;
}

export interface CompetencyDistribution {
  competencyId: string;
  competencyName: string;
  averageRating: number;
  distribution: LevelDistribution[];
}

export interface LevelDistribution {
  level: CompetencyLevel;
  count: number;
  percentage: number;
}

// Sorting types
export type SortDirection = 'asc' | 'desc';

export interface SortConfig {
  field: string;
  direction: SortDirection;
}