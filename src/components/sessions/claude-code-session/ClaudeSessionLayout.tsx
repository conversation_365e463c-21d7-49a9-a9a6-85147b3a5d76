import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { SessionHeader } from './SessionHeader';
import { MessageList } from './MessageList';
import { SessionInput } from '../../inputs/Promptinput';
import { SplitPane } from '@/components/ui/split-pane';
import { WebviewPreview } from '../../display/WebviewPreview';

interface ClaudeSessionLayoutProps {
  // Header props
  headerProps: any;
  
  // Messages props
  messagesProps: any;
  
  // Input props
  inputProps: any;
  
  // Preview props
  previewProps?: {
    showPreview: boolean;
    previewUrl: string;
    isMaximized: boolean;
    splitPosition: number;
    onClose: () => void;
    onToggleMaximize: () => void;
    onUrlChange: (url: string) => void;
    onSplitChange: (position: number) => void;
  };
  
  // Layout state
  showTimeline: boolean;
  showSuperClaude: boolean;
  showAgentMCP: boolean;
  
  // Panels
  timelinePanel?: React.ReactNode;
  superClaudePanel?: React.ReactNode;
  agentMCPPanel?: React.ReactNode;
  
  // Project input
  projectPathInput?: React.ReactNode;
  
  className?: string;
}

export const ClaudeSessionLayout: React.FC<ClaudeSessionLayoutProps> = ({
  headerProps,
  messagesProps,
  inputProps,
  previewProps,
  showTimeline,
  showSuperClaude,
  showAgentMCP,
  timelinePanel,
  superClaudePanel,
  agentMCPPanel,
  projectPathInput,
  className
}) => {
  const hasRightPanel = showTimeline || showSuperClaude;
  
  // Full screen preview
  if (previewProps?.showPreview && previewProps.isMaximized) {
    return (
      <AnimatePresence>
        <motion.div 
          className="fixed inset-0 z-50 bg-background"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
        >
          <WebviewPreview
            initialUrl={previewProps.previewUrl}
            onClose={previewProps.onClose}
            isMaximized={previewProps.isMaximized}
            onToggleMaximize={previewProps.onToggleMaximize}
            onUrlChange={previewProps.onUrlChange}
            className="h-full"
          />
        </motion.div>
      </AnimatePresence>
    );
  }

  return (
    <div className={cn("flex flex-col h-full bg-background", className)}>
      {/* Header */}
      <SessionHeader {...headerProps} />
      
      {/* Main Content Area */}
      <div className="flex-1 flex overflow-hidden">
        <div className={cn(
          "flex-1 overflow-hidden transition-all duration-300",
          hasRightPanel && "sm:mr-96"
        )}>
          {previewProps?.showPreview ? (
            // Split pane layout when preview is active
            <SplitPane
              left={
                <div className="h-full flex flex-col">
                  {projectPathInput}
                  <MessageList {...messagesProps} />
                </div>
              }
              right={
                <WebviewPreview
                  initialUrl={previewProps.previewUrl}
                  onClose={previewProps.onClose}
                  isMaximized={previewProps.isMaximized}
                  onToggleMaximize={previewProps.onToggleMaximize}
                  onUrlChange={previewProps.onUrlChange}
                />
              }
              initialSplit={previewProps.splitPosition}
              onSplitChange={previewProps.onSplitChange}
              minLeftWidth={400}
              minRightWidth={400}
              className="h-full"
            />
          ) : (
            // Original layout when no preview
            <div className="h-full flex flex-col max-w-5xl mx-auto">
              {projectPathInput}
              <MessageList {...messagesProps} />
            </div>
          )}
        </div>
        
        {/* Agent & MCP Panel */}
        {showAgentMCP && agentMCPPanel}
      </div>

      {/* Floating Input */}
      <div className={cn(
        "fixed bottom-0 left-0 right-0 transition-all duration-300 z-50",
        hasRightPanel && "sm:right-96"
      )}>
        <SessionInput {...inputProps} />
      </div>

      {/* Right Panels */}
      <AnimatePresence>
        {showTimeline && timelinePanel}
        {showSuperClaude && superClaudePanel}
      </AnimatePresence>
    </div>
  );
};