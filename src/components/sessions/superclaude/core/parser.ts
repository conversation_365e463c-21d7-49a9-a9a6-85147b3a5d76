import { SuperClaudeCommand, ParsedCommand, CommandSuggestion } from './types';
import { SUPERCLAUDE_COMMANDS, getCommandByTrigger, searchCommands } from './commands';

// Debug: Check what we imported
console.log('[SuperClaude Parser] Imported commands count:', Object.keys(SUPERCLAUDE_COMMANDS).length);
console.log('[SuperClaude Parser] Imported command keys:', Object.keys(SUPERCLAUDE_COMMANDS));
import { detectPersonas } from './personas';

// Parse a SuperClaude command from user input
export const parseCommand = (input: string): ParsedCommand | null => {
  // Check if input starts with /sc: prefix
  if (!input.startsWith('/sc:') && !input.startsWith('/')) {
    return null;
  }
  
  // Extract command and arguments
  const parts = input.split(' ');
  const trigger = parts[0];
  const args = parts.slice(1).join(' ');
  
  // Find matching command
  const command = getCommandByTrigger(trigger);
  if (!command) {
    return null;
  }
  
  // Parse parameters from args
  const parameters = parseParameters(args);
  
  // Detect personas from the command and args
  const detectedPersonas = detectPersonas(args) || [];
  const commandPersonas = command.personas || [];
  
  // Combine command personas with detected personas
  const allPersonas = Array.from(new Set([...commandPersonas, ...detectedPersonas]));
  
  return {
    command: command.name,
    trigger: command.trigger,
    args: args || '',
    personas: allPersonas.slice(0, 3) || [], // Limit to top 3 personas, ensure always array
    category: command.category || 'meta',
    parameters: parameters || {}
  };
};

// Parse command parameters from argument string
const parseParameters = (args: string): Record<string, any> => {
  const params: Record<string, any> = {};
  
  // Parse flag-style parameters (--key value or --flag)
  const flagPattern = /--(\w+)(?:\s+([^\s-][^\s]*)|(?=\s|$))/g;
  let match;
  
  while ((match = flagPattern.exec(args)) !== null) {
    const key = match[1];
    const value = match[2];
    
    if (value === undefined) {
      // Boolean flag
      params[key] = true;
    } else if (value === 'true' || value === 'false') {
      // Boolean value
      params[key] = value === 'true';
    } else if (!isNaN(Number(value))) {
      // Numeric value
      params[key] = Number(value);
    } else {
      // String value
      params[key] = value;
    }
  }
  
  // Extract main content (everything not part of flags)
  const mainContent = args.replace(flagPattern, '').trim();
  if (mainContent) {
    params.content = mainContent;
  }
  
  return params;
};

// Get command suggestions based on partial input
export const getCommandSuggestions = (input: string, limit = 10): CommandSuggestion[] => {
  const suggestions: CommandSuggestion[] = [];
  
  
  // Normalize input - handle various formats
  let query = input.toLowerCase().trim();
  
  // Remove leading slash if present
  if (query.startsWith('/')) {
    query = query.substring(1);
  }
  
  // If input is just 'sc:' or starts with 'sc:', handle it
  if (query === 'sc:' || query.startsWith('sc:')) {
    query = query.substring(3).trim(); // Remove 'sc:' prefix
  }
  
  
  // If empty or just 'sc:', return all commands (or up to limit)
  if (!query) {
    // Debug: Let's see how many commands we have
    const commandArray = Object.values(SUPERCLAUDE_COMMANDS);
    console.log('[SuperClaude] Total commands available:', commandArray.length);
    
    const allCommands = commandArray
      .map(cmd => ({
        command: cmd,
        score: 1,
        matchType: 'exact' as const
      }));
    
    const result = allCommands.slice(0, Math.min(limit, allCommands.length));
    console.log('[SuperClaude] Returning', result.length, 'commands (limit:', limit, ')');
    return result;
  }
  
  // Check for exact matches
  Object.values(SUPERCLAUDE_COMMANDS).forEach(cmd => {
    const trigger = cmd.trigger.toLowerCase().replace(/^\//, '');
    const name = cmd.name.toLowerCase();
    
    // Remove sc: prefix from trigger for comparison
    const triggerWithoutPrefix = trigger.replace('sc:', '');
    
    // Exact match on trigger (with or without sc: prefix)
    if (trigger === query || triggerWithoutPrefix === query || trigger === 'sc:' + query) {
      suggestions.push({
        command: cmd,
        score: 1,
        matchType: 'exact'
      });
      return;
    }
    
    // Exact match on name
    if (name === query) {
      suggestions.push({
        command: cmd,
        score: 0.95,
        matchType: 'exact'
      });
      return;
    }
    
    // Check aliases
    if (cmd.aliases) {
      for (const alias of cmd.aliases) {
        const normalizedAlias = alias.toLowerCase().replace(/^\//, '');
        if (normalizedAlias === query) {
          suggestions.push({
            command: cmd,
            score: 0.9,
            matchType: 'exact'
          });
          return;
        }
      }
    }
  });
  
  // Check for prefix matches
  Object.values(SUPERCLAUDE_COMMANDS).forEach(cmd => {
    const trigger = cmd.trigger.toLowerCase().replace(/^\//, '');
    const name = cmd.name.toLowerCase();
    
    // Skip if already added as exact match
    if (suggestions.some(s => s.command === cmd)) {
      return;
    }
    
    // Remove sc: prefix from trigger for comparison
    const triggerWithoutPrefix = trigger.replace('sc:', '');
    
    // Prefix match on trigger (with or without sc: prefix)
    if (trigger.startsWith(query) || triggerWithoutPrefix.startsWith(query) || trigger.startsWith('sc:' + query)) {
      suggestions.push({
        command: cmd,
        score: 0.8,
        matchType: 'prefix'
      });
      return;
    }
    
    // Prefix match on name
    if (name.startsWith(query)) {
      suggestions.push({
        command: cmd,
        score: 0.75,
        matchType: 'prefix'
      });
      return;
    }
    
    // Check aliases for prefix match
    if (cmd.aliases) {
      for (const alias of cmd.aliases) {
        const normalizedAlias = alias.toLowerCase().replace(/^\//, '');
        if (normalizedAlias.startsWith(query)) {
          suggestions.push({
            command: cmd,
            score: 0.7,
            matchType: 'prefix'
          });
          return;
        }
      }
    }
  });
  
  // Fuzzy search on description
  if (suggestions.length < limit) {
    Object.values(SUPERCLAUDE_COMMANDS).forEach(cmd => {
      // Skip if already added
      if (suggestions.some(s => s.command === cmd)) {
        return;
      }
      
      if (cmd.description.toLowerCase().includes(query)) {
        suggestions.push({
          command: cmd,
          score: 0.5,
          matchType: 'fuzzy'
        });
      }
    });
  }
  
  // Sort by score and return limited results
  return suggestions
    .sort((a, b) => b.score - a.score)
    .slice(0, limit);
};

// Check if input is a SuperClaude command or might be one
export const isSuperClaudeCommand = (input: string): boolean => {
  // Always return true for /sc: prefix to show suggestions while typing
  if (input.startsWith('/sc:')) {
    return true;
  }
  
  // Check if it's a known command
  if (input.startsWith('/')) {
    const trigger = input.split(' ')[0];
    return getCommandByTrigger(trigger) !== undefined;
  }
  
  return false;
};

// Extract command trigger from input
export const extractCommandTrigger = (input: string): string | null => {
  const parts = input.split(' ');
  if (parts.length === 0) return null;
  
  const trigger = parts[0];
  if (!trigger.startsWith('/')) return null;
  
  // Return the trigger even if it's just /sc: to enable suggestions
  return trigger;
};

// Build command prompt with personas
export const buildCommandPrompt = (parsed: ParsedCommand): string => {
  if (!parsed) {
    console.error('[SuperClaude] buildCommandPrompt called with null/undefined parsed command');
    return '';
  }
  
  const lines: string[] = [];
  
  // Add command header
  lines.push(`[SuperClaude Command: ${parsed.command}]`);
  
  // Add personas if any (check if personas exists and is an array)
  if (parsed.personas && Array.isArray(parsed.personas) && parsed.personas.length > 0) {
    lines.push(`Active Personas: ${parsed.personas.join(', ')}`);
  }
  
  // Add parameters
  if (parsed.parameters && Object.keys(parsed.parameters).length > 0) {
    lines.push('Parameters:');
    Object.entries(parsed.parameters).forEach(([key, value]) => {
      if (key !== 'content') {
        lines.push(`  - ${key}: ${value}`);
      }
    });
  }
  
  // Add main content
  if (parsed.parameters?.content) {
    lines.push('');
    lines.push(parsed.parameters.content);
  } else if (parsed.args) {
    lines.push('');
    lines.push(parsed.args);
  }
  
  return lines.join('\n');
};