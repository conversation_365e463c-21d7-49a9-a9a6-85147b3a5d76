import { SuperClaudeCommand, CommandCategory } from './types';

// Debug: Log the commands as they're defined
const COMMANDS_TEMP: Record<string, SuperClaudeCommand> = {
  // Development Commands
  build: {
    trigger: '/sc:build',
    name: 'Build',
    description: 'Build, compile, and package projects with smart error handling',
    category: 'development',
    personas: ['architect', 'frontend', 'backend'],
    aliases: ['/build', '/compile'],
    examples: [
      '/sc:build --target production',
      '/sc:build --watch',
      '/sc:build --clean'
    ]
  },
  
  implement: {
    trigger: '/sc:implement',
    name: 'Implement',
    description: 'Implement features, components, and functionality with intelligent expert activation',
    category: 'development',
    personas: ['frontend', 'backend', 'architect'],
    aliases: ['/implement', '/create'],
    examples: [
      '/sc:implement user authentication system',
      '/sc:implement responsive navigation component',
      '/sc:implement REST API endpoints'
    ]
  },
  
  // Analysis Commands
  analyze: {
    trigger: '/sc:analyze',
    name: 'Analyze',
    description: 'Perform multi-dimensional system analysis with appropriate expert auto-activation',
    category: 'analysis',
    personas: ['analyzer', 'architect', 'security'],
    aliases: ['/analyze', '/inspect'],
    examples: [
      '/sc:analyze performance bottlenecks',
      '/sc:analyze security vulnerabilities',
      '/sc:analyze code complexity'
    ]
  },
  
  troubleshoot: {
    trigger: '/sc:troubleshoot',
    name: 'Troubleshoot',
    description: 'Debug specialist and security expert coordination for issue resolution',
    category: 'analysis',
    personas: ['analyzer', 'qa', 'security'],
    aliases: ['/debug', '/fix'],
    examples: [
      '/sc:troubleshoot memory leak',
      '/sc:troubleshoot API timeout issues',
      '/sc:troubleshoot authentication failures'
    ]
  },
  
  explain: {
    trigger: '/sc:explain',
    name: 'Explain',
    description: 'Educational explanations with mentor guidance',
    category: 'analysis',
    personas: ['mentor', 'scribe'],
    aliases: ['/explain', '/teach'],
    examples: [
      '/sc:explain how React hooks work',
      '/sc:explain microservices architecture',
      '/sc:explain SOLID principles'
    ]
  },
  
  // Quality Commands
  improve: {
    trigger: '/sc:improve',
    name: 'Improve',
    description: 'Optimize code with performance optimizer activation',
    category: 'quality',
    personas: ['refactorer', 'performance', 'architect'],
    aliases: ['/optimize', '/enhance'],
    examples: [
      '/sc:improve database query performance',
      '/sc:improve component rendering',
      '/sc:improve code readability'
    ]
  },
  
  cleanup: {
    trigger: '/sc:cleanup',
    name: 'Cleanup',
    description: 'Reduce technical debt and improve code quality',
    category: 'quality',
    personas: ['refactorer', 'qa'],
    aliases: ['/refactor', '/clean'],
    examples: [
      '/sc:cleanup unused dependencies',
      '/sc:cleanup duplicate code',
      '/sc:cleanup legacy patterns'
    ]
  },
  
  test: {
    trigger: '/sc:test',
    name: 'Test',
    description: 'Create and execute comprehensive testing strategies',
    category: 'quality',
    personas: ['qa', 'analyzer'],
    aliases: ['/test', '/verify'],
    examples: [
      '/sc:test unit tests for auth module',
      '/sc:test integration test suite',
      '/sc:test e2e user workflows'
    ]
  },
  
  // Documentation Commands
  document: {
    trigger: '/sc:document',
    name: 'Document',
    description: 'Generate comprehensive documentation',
    category: 'documentation',
    personas: ['scribe', 'mentor'],
    aliases: ['/doc', '/docs'],
    examples: [
      '/sc:document API endpoints',
      '/sc:document component library',
      '/sc:document architecture decisions'
    ]
  },
  
  // Development Workflow Commands
  git: {
    trigger: '/sc:git',
    name: 'Git',
    description: 'Git workflow assistance and best practices',
    category: 'development',
    personas: ['devops', 'architect'],
    aliases: ['/git', '/version'],
    examples: [
      '/sc:git create feature branch',
      '/sc:git write commit message',
      '/sc:git resolve merge conflicts'
    ]
  },
  
  design: {
    trigger: '/sc:design',
    name: 'Design',
    description: 'Design system architecture and patterns',
    category: 'development',
    personas: ['architect', 'frontend'],
    aliases: ['/design', '/architect'],
    examples: [
      '/sc:design microservices architecture',
      '/sc:design component hierarchy',
      '/sc:design database schema'
    ]
  },
  
  estimate: {
    trigger: '/sc:estimate',
    name: 'Estimate',
    description: 'Evidence-based project estimation',
    category: 'meta',
    personas: ['architect', 'analyzer'],
    aliases: ['/estimate', '/plan'],
    examples: [
      '/sc:estimate project timeline',
      '/sc:estimate resource requirements',
      '/sc:estimate complexity points'
    ]
  },
  
  task: {
    trigger: '/sc:task',
    name: 'Task',
    description: 'Long-term project and task management',
    category: 'meta',
    personas: ['architect', 'mentor'],
    aliases: ['/task', '/project'],
    examples: [
      '/sc:task break down epic',
      '/sc:task prioritize backlog',
      '/sc:task create sprint plan'
    ]
  },
  
  // Meta Commands
  index: {
    trigger: '/sc:index',
    name: 'Index',
    description: 'Browse available commands and capabilities',
    category: 'meta',
    personas: ['mentor'],
    aliases: ['/help', '/commands'],
    examples: [
      '/sc:index',
      '/sc:index development',
      '/sc:index personas'
    ]
  },
  
  load: {
    trigger: '/sc:load',
    name: 'Load',
    description: 'Load project context and configurations',
    category: 'meta',
    personas: ['architect'],
    aliases: ['/load', '/context'],
    examples: [
      '/sc:load project context',
      '/sc:load configuration',
      '/sc:load workspace settings'
    ]
  },
  
  spawn: {
    trigger: '/sc:spawn',
    name: 'Spawn',
    description: 'Orchestrate complex multi-step tasks',
    category: 'meta',
    personas: ['architect', 'devops'],
    aliases: ['/spawn', '/orchestrate'],
    examples: [
      '/sc:spawn deployment pipeline',
      '/sc:spawn testing workflow',
      '/sc:spawn build chain'
    ]
  }
};

// Debug: Check how many commands we actually have
console.log('[SuperClaude Commands] Total commands defined:', Object.keys(COMMANDS_TEMP).length);
console.log('[SuperClaude Commands] Command names:', Object.keys(COMMANDS_TEMP));

export const SUPERCLAUDE_COMMANDS = COMMANDS_TEMP;

// Helper function to get commands by category
export const getCommandsByCategory = (category: CommandCategory): SuperClaudeCommand[] => {
  return Object.values(SUPERCLAUDE_COMMANDS).filter(cmd => cmd.category === category);
};

// Helper function to search commands
export const searchCommands = (query: string): SuperClaudeCommand[] => {
  const lowerQuery = query.toLowerCase();
  return Object.values(SUPERCLAUDE_COMMANDS).filter(cmd => 
    cmd.trigger.toLowerCase().includes(lowerQuery) ||
    cmd.name.toLowerCase().includes(lowerQuery) ||
    cmd.description.toLowerCase().includes(lowerQuery) ||
    cmd.aliases?.some(alias => alias.toLowerCase().includes(lowerQuery))
  );
};

// Helper function to get command by trigger or alias
export const getCommandByTrigger = (trigger: string): SuperClaudeCommand | undefined => {
  return Object.values(SUPERCLAUDE_COMMANDS).find(cmd =>
    cmd.trigger === trigger || cmd.aliases?.includes(trigger)
  );
};