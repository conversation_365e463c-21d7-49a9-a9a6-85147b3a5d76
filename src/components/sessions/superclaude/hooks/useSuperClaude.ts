import { useState, useCallback, useEffect, useRef } from 'react';
import { 
  SuperClaudeContext, 
  ParsedCommand, 
  CommandSuggestion,
  PersonaActivation 
} from '../core/types';
import { 
  parseCommand, 
  getCommandSuggestions, 
  isSuperClaudeCommand,
  extractCommandTrigger,
  buildCommandPrompt
} from '../core/parser';
import { detectPersonas, buildPersonaContext } from '../core/personas';
import { api } from '@/lib/api';
import { integrationManager } from '../integrated/SuperClaudeIntegrationManager';

interface UseSuperClaudeOptions {
  sessionId?: string;
  projectId?: string;
  onCommandExecute?: (command: ParsedCommand) => void;
  onPersonaActivate?: (personas: string[]) => void;
  maxHistorySize?: number;
  useBackend?: boolean; // Flag to enable backend integration
}

export const useSuperClaude = (options: UseSuperClaudeOptions = {}) => {
  const {
    sessionId,
    projectId,
    onCommandExecute,
    onPersonaActivate,
    maxHistorySize = 10,
    useBackend = false
  } = options;

  // Track if we're loading from backend
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const loadedRef = useRef(false);

  // State - will be populated from backend if available
  const [context, setContext] = useState<SuperClaudeContext>({
    activeCommand: null,
    activePersonas: [],
    commandHistory: [],
    personaHistory: []
  });

  const [suggestions, setSuggestions] = useState<CommandSuggestion[]>([]);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(0);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Initialize integration manager on mount
  useEffect(() => {
    integrationManager.initialize().catch(console.error);
  }, []);

  // Load context from backend on mount if sessionId and projectId are provided
  useEffect(() => {
    if (useBackend && sessionId && projectId && !loadedRef.current) {
      loadedRef.current = true;
      setIsLoading(true);
      setError(null);

      api.loadSuperClaudeContext(sessionId, projectId)
        .then((backendContext) => {
          // Map backend command history to ParsedCommand format
          const mappedHistory = (backendContext.command_history || []).map((entry: any) => ({
            command: entry.command,
            trigger: entry.command, // Use command as trigger fallback
            args: entry.parameters?.join(' ') || '',
            personas: entry.detected_personas || [],
            category: 'meta' as const, // Default category
            parameters: {}
          }));
          
          setContext({
            activeCommand: null, // Will be set when command is executed
            activePersonas: backendContext.active_personas || [],
            commandHistory: mappedHistory,
            personaHistory: [] // Frontend-only for now
          });
        })
        .catch((err) => {
          const errorMessage = err instanceof Error ? err.message : 'Failed to load context';
          setError(errorMessage);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [useBackend, sessionId, projectId]);

  // Process input for commands and personas
  const processInput = useCallback(async (input: string) => {
    
    // Check for SuperClaude command
    if (isSuperClaudeCommand(input)) {
      const trigger = extractCommandTrigger(input);
      
      if (trigger) {
        // Always try local suggestions first for immediate feedback
        // Pass the trigger as-is since getCommandSuggestions handles normalization
        const localSuggestions = getCommandSuggestions(trigger, 30); // Show all available commands
        setSuggestions(localSuggestions);
        setShowSuggestions(localSuggestions.length > 0);
        setSelectedSuggestionIndex(0);
        
        // Skip backend suggestions for now to ensure all local commands show
        if (false && useBackend && sessionId && projectId) {
          try {
            const backendSuggestions = await api.getSuperClaudeSuggestions(trigger);
            
            // The backend might return a different format, let's log the first item
            if (backendSuggestions.length > 0) {
              // Log the nested command object if it exists
              if (backendSuggestions[0].command) {
              }
            }
            
            // Validate and map backend suggestions - backend returns nested structure
            if (Array.isArray(backendSuggestions) && backendSuggestions.length > 0) {
              const mappedSuggestions = backendSuggestions.map((suggestion: any) => {
                // Backend returns { command: Object, score: number, match_type: string }
                // We need to extract the nested command object
                const cmd = suggestion.command || suggestion;
                
                // Check both camelCase and snake_case formats
                const hasRequiredFields = (cmd && typeof cmd === 'object') && 
                  ((cmd.trigger && cmd.name) || (cmd.trigger && cmd.command_name));
                
                if (hasRequiredFields) {
                  // Map to our expected format
                  const command = {
                    trigger: cmd.trigger,
                    name: cmd.name || cmd.command_name || cmd.trigger,
                    description: cmd.description || '',
                    category: cmd.category || 'meta',
                    personas: cmd.personas || [],
                    aliases: cmd.aliases || [],
                    examples: cmd.examples || []
                  };
                  
                  return {
                    command,
                    score: suggestion.score || 1,
                    matchType: (suggestion.match_type || suggestion.matchType || 'exact') as 'exact' | 'prefix' | 'partial'
                  };
                }
                // If backend returns invalid format, skip this suggestion
                return null;
              }).filter(Boolean) as CommandSuggestion[];
              
              setSuggestions(mappedSuggestions);
              setShowSuggestions(mappedSuggestions.length > 0);
              setSelectedSuggestionIndex(0);
            } else {
              // Backend returned no suggestions, keep using local
            }
          } catch (err) {
            // Local suggestions already set, no need to re-fetch
          }
        }
      }
    } else {
      // Clear suggestions if not a command
      setShowSuggestions(false);
      setSuggestions([]);
      
      // Detect personas from natural language
      if (useBackend && sessionId && projectId) {
        try {
          const detectedPersonas = await api.detectPersonasFromText(input);
          if (detectedPersonas.length > 0 && detectedPersonas.join(',') !== context.activePersonas.join(',')) {
            await api.activatePersonas(sessionId, projectId, detectedPersonas);
            setContext(prev => ({
              ...prev,
              activePersonas: detectedPersonas
            }));
            onPersonaActivate?.(detectedPersonas);
          }
        } catch (err) {
          // Fall back to local detection
          const detectedPersonas = detectPersonas(input);
          if (detectedPersonas.length > 0 && detectedPersonas.join(',') !== context.activePersonas.join(',')) {
            setContext(prev => ({
              ...prev,
              activePersonas: detectedPersonas
            }));
            onPersonaActivate?.(detectedPersonas);
          }
        }
      } else {
        // Use local persona detection
        const detectedPersonas = detectPersonas(input);
        if (detectedPersonas.length > 0 && detectedPersonas.join(',') !== context.activePersonas.join(',')) {
          setContext(prev => ({
            ...prev,
            activePersonas: detectedPersonas
          }));
          onPersonaActivate?.(detectedPersonas);
        }
      }
      setShowSuggestions(false);
    }
  }, [context.activePersonas, onPersonaActivate, useBackend, sessionId, projectId]);

  // Execute a command
  const executeCommand = useCallback(async (input: string) => {
    // Use backend if available
    if (useBackend && sessionId && projectId) {
      try {
        const backendParsed = await api.executeSuperClaudeCommand(sessionId, projectId, input);
        
        // Backend returns ParsedCommand directly with: command, trigger, args, personas, category, parameters
        // Update local context with backend result
        setContext(prev => ({
          ...prev,
          activeCommand: backendParsed,
          activePersonas: backendParsed.personas || [],
          commandHistory: prev.commandHistory // Will be updated from backend on next load
        }));

        // Save to command history
        if (backendParsed.command) {
          await api.saveCommandHistory(
            sessionId,
            projectId,
            backendParsed.command,
            backendParsed.trigger,
            backendParsed.args || '',
            backendParsed.personas || [],
            true,
            undefined
          );
        }

        // The backend already returns ParsedCommand format
        const parsed: ParsedCommand = {
          command: backendParsed.command,
          trigger: backendParsed.trigger,
          args: backendParsed.args || '',
          personas: backendParsed.personas || [],
          category: backendParsed.category || 'meta',
          parameters: backendParsed.parameters || {}
        };
        
        onCommandExecute?.(parsed);
        if (parsed.personas && parsed.personas.length > 0) {
          onPersonaActivate?.(parsed.personas);
        }

        // Hide suggestions
        setShowSuggestions(false);
        setSuggestions([]);

        return parsed;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to execute command';
        setError(errorMessage);
        // Fall back to local execution
      }
    }

    // Local execution (fallback or when backend not available)
    const parsed = parseCommand(input);
    if (!parsed) return null;

    // Update context
    setContext(prev => {
      const newHistory = [parsed, ...prev.commandHistory].slice(0, maxHistorySize);
      const newPersonaHistory = parsed.personas.map(p => ({
        persona: { id: p } as any,
        confidence: 1,
        reason: 'Command activation'
      } as PersonaActivation));

      return {
        activeCommand: parsed,
        activePersonas: parsed.personas,
        commandHistory: newHistory,
        personaHistory: [...newPersonaHistory, ...prev.personaHistory].slice(0, maxHistorySize * 2)
      };
    });

    // Notify callbacks
    onCommandExecute?.(parsed);
    if (parsed.personas.length > 0) {
      onPersonaActivate?.(parsed.personas);
    }

    // Hide suggestions
    setShowSuggestions(false);
    setSuggestions([]);

    return parsed;
  }, [maxHistorySize, onCommandExecute, onPersonaActivate, useBackend, sessionId, projectId]);

  // Handle suggestion selection
  const selectSuggestion = useCallback((suggestion: CommandSuggestion) => {
    const command = suggestion.command;
    const input = command.trigger + ' ';
    
    // Return the command trigger for the input field
    setShowSuggestions(false);
    setSuggestions([]);
    
    return input;
  }, []);

  // Navigate suggestions
  const navigateSuggestions = useCallback((direction: 'up' | 'down') => {
    if (!showSuggestions || suggestions.length === 0) return;

    setSelectedSuggestionIndex(prev => {
      if (direction === 'up') {
        return prev > 0 ? prev - 1 : suggestions.length - 1;
      } else {
        return prev < suggestions.length - 1 ? prev + 1 : 0;
      }
    });
  }, [showSuggestions, suggestions.length]);

  // Select current suggestion
  const selectCurrentSuggestion = useCallback(() => {
    if (!showSuggestions || suggestions.length === 0) return null;
    
    const suggestion = suggestions[selectedSuggestionIndex];
    if (suggestion) {
      return selectSuggestion(suggestion);
    }
    return null;
  }, [showSuggestions, suggestions, selectedSuggestionIndex, selectSuggestion]);

  // Clear suggestions
  const clearSuggestions = useCallback(() => {
    setShowSuggestions(false);
    setSuggestions([]);
    setSelectedSuggestionIndex(0);
  }, []);

  // Execute agent through integration manager
  const executeAgent = useCallback(async (agentId: string, context: any) => {
    try {
      return await integrationManager.executeAgent(agentId, context);
    } catch (error) {
      console.error(`Failed to execute agent ${agentId}:`, error);
      throw error;
    }
  }, []);

  // Execute MCP command through integration manager
  const executeMCPCommand = useCallback(async (serverId: string, command: string, args: any) => {
    try {
      return await integrationManager.executeMCPCommand(serverId, command, args);
    } catch (error) {
      console.error(`Failed to execute MCP command ${command}:`, error);
      throw error;
    }
  }, []);

  // Get available agents
  const getAgents = useCallback(() => {
    return Array.from(integrationManager.getAgents().values());
  }, []);

  // Get available MCP servers
  const getMCPServers = useCallback(() => {
    return Array.from(integrationManager.getMCPServers().values());
  }, []);

  // Build enhanced prompt with personas
  const enhancePrompt = useCallback((prompt: string): string => {
    // Check if it's a command
    const parsed = parseCommand(prompt);
    if (parsed) {
      return buildCommandPrompt(parsed);
    }

    // Otherwise, add persona context if active
    if (context.activePersonas.length > 0) {
      const personaContext = buildPersonaContext(context.activePersonas);
      return `${personaContext}\n\n${prompt}`;
    }

    return prompt;
  }, [context.activePersonas]);

  // Clear active personas
  const clearActivePersonas = useCallback(() => {
    setContext(prev => ({
      ...prev,
      activePersonas: []
    }));
  }, []);

  // Remove a specific persona
  const removePersona = useCallback((personaId: string) => {
    setContext(prev => ({
      ...prev,
      activePersonas: prev.activePersonas.filter(p => p !== personaId)
    }));
  }, []);

  // Get command history
  const getCommandHistory = useCallback(() => {
    return context.commandHistory;
  }, [context.commandHistory]);

  // Check if input might be a command
  const mightBeCommand = useCallback((input: string): boolean => {
    return input.startsWith('/') || input.startsWith('sc:');
  }, []);

  return {
    // Context
    context,
    activePersonas: context.activePersonas,
    activeCommand: context.activeCommand,
    
    // Suggestions
    suggestions,
    selectedSuggestionIndex,
    showSuggestions,
    
    // Loading states
    isLoading,
    error,
    
    // Actions
    processInput,
    executeCommand,
    selectSuggestion,
    navigateSuggestions,
    selectCurrentSuggestion,
    clearSuggestions,
    enhancePrompt,
    clearActivePersonas,
    removePersona,
    getCommandHistory,
    mightBeCommand,
    
    // Integration Manager Methods
    executeAgent,
    executeMCPCommand,
    getAgents,
    getMCPServers
  };
};