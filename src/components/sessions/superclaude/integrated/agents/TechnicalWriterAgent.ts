import { BaseAgent } from './types';

export class TechnicalWriterAgent extends BaseAgent {
  constructor() {
    super(
      'technical-writer',
      'Technical Writer',
      'Creates clear documentation and technical content',
      ['documentation', 'api-docs', 'tutorials', 'readme', 'technical-writing']
    );
  }

  async execute(context: any): Promise<any> {
    const { task, code, requirements } = context;
    
    // Technical writing specific logic
    const analysis = await this.analyzeDocumentationNeeds(code, requirements);
    const structure = await this.createDocumentStructure(analysis);
    const content = await this.generateDocumentation(structure, code);
    const formatted = await this.formatDocumentation(content);
    
    return {
      agent: this.name,
      analysis,
      structure,
      content,
      formatted,
      artifacts: this.createDocumentationArtifacts(formatted)
    };
  }

  private async analyzeDocumentationNeeds(code: any, requirements: any) {
    return {
      audience: this.identifyAudience(requirements),
      scope: this.determineScope(requirements),
      format: this.selectFormat(requirements),
      components: this.identifyComponents(code)
    };
  }

  private async createDocumentStructure(analysis: any) {
    return {
      sections: this.defineSections(analysis),
      hierarchy: this.defineHierarchy(analysis),
      navigation: this.defineNavigation(analysis),
      crossReferences: this.defineCrossReferences(analysis)
    };
  }

  private async generateDocumentation(structure: any, code: any) {
    return {
      overview: this.writeOverview(structure),
      gettingStarted: this.writeGettingStarted(structure),
      apiReference: this.writeAPIReference(code),
      examples: this.writeExamples(code),
      troubleshooting: this.writeTroubleshooting()
    };
  }

  private async formatDocumentation(content: any) {
    return {
      markdown: this.formatAsMarkdown(content),
      html: this.formatAsHTML(content),
      pdf: this.formatAsPDF(content),
      docusaurus: this.formatForDocusaurus(content)
    };
  }

  private identifyAudience(requirements: any) {
    return {
      primary: 'Developers',
      secondary: 'DevOps Engineers',
      level: 'Intermediate to Advanced',
      background: 'Software Development'
    };
  }

  private determineScope(requirements: any) {
    return {
      coverage: 'Complete API and Integration Guide',
      depth: 'Detailed with Examples',
      breadth: 'All Features and Use Cases',
      maintenance: 'Living Document'
    };
  }

  private selectFormat(requirements: any) {
    return {
      primary: 'Markdown',
      secondary: 'HTML',
      interactive: 'Swagger/OpenAPI',
      searchable: 'Algolia DocSearch'
    };
  }

  private identifyComponents(code: any) {
    return [
      'Installation Guide',
      'Configuration',
      'API Reference',
      'Code Examples',
      'Best Practices',
      'Troubleshooting'
    ];
  }

  private defineSections(analysis: any) {
    return [
      { id: 'intro', title: 'Introduction', level: 1 },
      { id: 'quickstart', title: 'Quick Start', level: 1 },
      { id: 'installation', title: 'Installation', level: 2 },
      { id: 'configuration', title: 'Configuration', level: 2 },
      { id: 'usage', title: 'Usage', level: 1 },
      { id: 'api', title: 'API Reference', level: 1 },
      { id: 'examples', title: 'Examples', level: 1 },
      { id: 'faq', title: 'FAQ', level: 1 }
    ];
  }

  private defineHierarchy(analysis: any) {
    return {
      root: 'Documentation',
      guides: ['Getting Started', 'User Guide', 'Developer Guide'],
      reference: ['API', 'CLI', 'Configuration'],
      resources: ['Examples', 'Tutorials', 'Best Practices']
    };
  }

  private defineNavigation(analysis: any) {
    return {
      sidebar: true,
      breadcrumbs: true,
      search: true,
      toc: true,
      pagination: true
    };
  }

  private defineCrossReferences(analysis: any) {
    return {
      internal: ['See also', 'Related topics', 'Prerequisites'],
      external: ['Official docs', 'Community resources', 'Stack Overflow'],
      glossary: true,
      index: true
    };
  }

  private writeOverview(structure: any) {
    return `# Project Overview

## Introduction
This project provides a comprehensive solution for...

## Key Features
- Feature 1: Description
- Feature 2: Description
- Feature 3: Description

## Architecture
The system follows a microservices architecture...`;
  }

  private writeGettingStarted(structure: any) {
    return `# Getting Started

## Prerequisites
- Node.js >= 14.0.0
- npm or yarn
- Git

## Installation
\`\`\`bash
npm install package-name
\`\`\`

## Basic Usage
\`\`\`javascript
const package = require('package-name');
package.init();
\`\`\``;
  }

  private writeAPIReference(code: any) {
    return `# API Reference

## Core API

### Method: initialize()
Initializes the application.

**Parameters:**
- \`config\` (Object): Configuration object

**Returns:**
- \`Promise<void>\`: Resolves when initialized

**Example:**
\`\`\`javascript
await app.initialize({ debug: true });
\`\`\``;
  }

  private writeExamples(code: any) {
    return `# Examples

## Basic Example
\`\`\`javascript
// Simple usage example
const result = await api.getData();
console.log(result);
\`\`\`

## Advanced Example
\`\`\`javascript
// Complex usage with error handling
try {
  const data = await api.complexOperation({
    option1: true,
    option2: 'value'
  });
} catch (error) {
  console.error('Operation failed:', error);
}
\`\`\``;
  }

  private writeTroubleshooting() {
    return `# Troubleshooting

## Common Issues

### Issue: Connection Failed
**Solution:** Check network settings and API endpoint.

### Issue: Authentication Error
**Solution:** Verify API keys and permissions.

### Issue: Performance Problems
**Solution:** Enable caching and optimize queries.`;
  }

  private formatAsMarkdown(content: any) {
    return Object.values(content).join('\n\n---\n\n');
  }

  private formatAsHTML(content: any) {
    return `<!DOCTYPE html>
<html>
<head><title>Documentation</title></head>
<body>${this.formatAsMarkdown(content)}</body>
</html>`;
  }

  private formatAsPDF(content: any) {
    return 'PDF generation configuration';
  }

  private formatForDocusaurus(content: any) {
    return {
      sidebar: 'sidebars.js',
      docs: content,
      config: 'docusaurus.config.js'
    };
  }

  private createDocumentationArtifacts(formatted: any) {
    return {
      readme: 'README.md',
      changelog: 'CHANGELOG.md',
      contributing: 'CONTRIBUTING.md',
      license: 'LICENSE',
      apiDocs: 'docs/api/',
      guides: 'docs/guides/',
      examples: 'examples/'
    };
  }
}