export abstract class BaseMCPServer {
  protected id: string;
  protected name: string;
  protected description: string;
  protected capabilities: string[];
  protected status: 'connected' | 'connecting' | 'disconnected' | 'error';
  protected lastConnected?: Date;
  protected error?: string;
  protected metrics: {
    totalRequests: number;
    failedRequests: number;
    averageLatency: number;
    uptime: number;
  };

  constructor(id: string, name: string, description: string, capabilities: string[]) {
    this.id = id;
    this.name = name;
    this.description = description;
    this.capabilities = capabilities;
    this.status = 'disconnected';
    this.metrics = {
      totalRequests: 0,
      failedRequests: 0,
      averageLatency: 0,
      uptime: 0
    };
  }

  abstract connect(): Promise<boolean>;
  abstract disconnect(): Promise<void>;
  abstract executeCommand(command: string, args: any): Promise<any>;
  abstract getTools(): MCPTool[];
  abstract getResources(): MCPResource[];

  getId(): string {
    return this.id;
  }

  getName(): string {
    return this.name;
  }

  getDescription(): string {
    return this.description;
  }

  getCapabilities(): string[] {
    return this.capabilities;
  }

  getStatus(): string {
    return this.status;
  }

  getMetrics(): typeof this.metrics {
    return this.metrics;
  }

  protected updateMetrics(latency: number, success: boolean): void {
    this.metrics.totalRequests++;
    
    if (!success) {
      this.metrics.failedRequests++;
    }
    
    this.metrics.averageLatency = 
      ((this.metrics.averageLatency * (this.metrics.totalRequests - 1)) + latency) / 
      this.metrics.totalRequests;
  }
}

export interface MCPTool {
  name: string;
  description: string;
  parameters: Record<string, any>;
  returns?: any;
}

export interface MCPResource {
  uri: string;
  name: string;
  description?: string;
  mimeType?: string;
  size?: number;
  lastModified?: Date;
  metadata?: Record<string, any>;
}

export interface MCPServerConfig {
  id: string;
  name: string;
  enabled: boolean;
  autoConnect: boolean;
  retryAttempts: number;
  timeout: number;
  credentials?: Record<string, any>;
  customSettings?: Record<string, any>;
}

export interface MCPServerMetrics {
  serverId: string;
  timestamp: Date;
  requests: {
    total: number;
    successful: number;
    failed: number;
  };
  performance: {
    averageLatency: number;
    p95Latency: number;
    p99Latency: number;
  };
  availability: {
    uptime: number;
    downtime: number;
    lastError?: string;
  };
}

export interface MCPServerEvent {
  serverId: string;
  type: 'connected' | 'disconnected' | 'error' | 'command' | 'resource';
  timestamp: Date;
  data?: any;
  error?: string;
}