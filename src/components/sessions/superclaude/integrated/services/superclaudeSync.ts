import { superclaudeStorage } from './superclaudeStorage';
import { superclaudeCache } from './superclaudeCache';
import { superclaudeConfig } from '../config/superclaude-config';

interface SyncState {
  lastSync: number;
  pending: SyncOperation[];
  conflicts: SyncConflict[];
  syncing: boolean;
  error?: string;
}

interface SyncOperation {
  id: string;
  type: 'create' | 'update' | 'delete';
  entity: 'agent' | 'command' | 'mcp' | 'config';
  data: any;
  timestamp: number;
  retries: number;
}

interface SyncConflict {
  id: string;
  local: any;
  remote: any;
  resolution?: 'local' | 'remote' | 'merge';
  timestamp: number;
}

interface SyncOptions {
  force?: boolean;
  resolveConflicts?: 'local' | 'remote' | 'merge' | 'manual';
  includeCache?: boolean;
}

export class SuperClaudeSync {
  private static instance: SuperClaudeSync;
  private state: SyncState;
  private syncInterval: NodeJS.Timeout | null;
  private websocket: WebSocket | null;
  private eventListeners: Map<string, Set<Function>>;
  private syncQueue: SyncOperation[];
  private maxRetries: number;

  private constructor() {
    this.state = {
      lastSync: 0,
      pending: [],
      conflicts: [],
      syncing: false
    };
    this.syncInterval = null;
    this.websocket = null;
    this.eventListeners = new Map();
    this.syncQueue = [];
    this.maxRetries = 3;
    this.initialize();
  }

  static getInstance(): SuperClaudeSync {
    if (!SuperClaudeSync.instance) {
      SuperClaudeSync.instance = new SuperClaudeSync();
    }
    return SuperClaudeSync.instance;
  }

  private async initialize(): Promise<void> {
    // Load sync state from storage
    await this.loadState();
    
    // Initialize WebSocket connection for real-time sync
    this.initializeWebSocket();
    
    // Start periodic sync
    this.startPeriodicSync();
    
    // Process pending operations
    await this.processPendingOperations();
  }

  // Core Sync Methods
  async sync(options: SyncOptions = {}): Promise<boolean> {
    if (this.state.syncing && !options.force) {
      console.log('Sync already in progress');
      return false;
    }

    this.state.syncing = true;
    this.emit('sync:start');

    try {
      // Pull remote changes
      const remoteChanges = await this.pullRemoteChanges();
      
      // Detect conflicts
      const conflicts = await this.detectConflicts(remoteChanges);
      
      // Resolve conflicts
      if (conflicts.length > 0) {
        await this.resolveConflicts(conflicts, options.resolveConflicts || 'manual');
      }
      
      // Apply remote changes
      await this.applyRemoteChanges(remoteChanges);
      
      // Push local changes
      await this.pushLocalChanges();
      
      // Sync cache if requested
      if (options.includeCache) {
        await this.syncCache();
      }
      
      // Update sync state
      this.state.lastSync = Date.now();
      this.state.pending = [];
      await this.saveState();
      
      this.emit('sync:complete', { timestamp: this.state.lastSync });
      return true;
    } catch (error) {
      this.state.error = error instanceof Error ? error.message : 'Sync failed';
      this.emit('sync:error', error);
      return false;
    } finally {
      this.state.syncing = false;
    }
  }

  // Real-time Sync
  private initializeWebSocket(): void {
    if (!superclaudeConfig.api.baseUrl) return;

    const wsUrl = superclaudeConfig.api.baseUrl.replace('http', 'ws') + '/sync';
    
    try {
      this.websocket = new WebSocket(wsUrl);
      
      this.websocket.onopen = () => {
        console.log('WebSocket connected for real-time sync');
        this.emit('websocket:connected');
      };
      
      this.websocket.onmessage = async (event) => {
        const message = JSON.parse(event.data);
        await this.handleWebSocketMessage(message);
      };
      
      this.websocket.onerror = (error) => {
        console.error('WebSocket error:', error);
        this.emit('websocket:error', error);
      };
      
      this.websocket.onclose = () => {
        console.log('WebSocket disconnected');
        this.emit('websocket:disconnected');
        
        // Attempt reconnection after delay
        setTimeout(() => this.initializeWebSocket(), 5000);
      };
    } catch (error) {
      console.error('Failed to initialize WebSocket:', error);
    }
  }

  private async handleWebSocketMessage(message: any): Promise<void> {
    switch (message.type) {
      case 'change':
        await this.handleRemoteChange(message.data);
        break;
      case 'conflict':
        await this.handleRemoteConflict(message.data);
        break;
      case 'sync-request':
        await this.sync();
        break;
      default:
        console.log('Unknown WebSocket message type:', message.type);
    }
  }

  // Change Tracking
  trackChange(entity: string, operation: 'create' | 'update' | 'delete', data: any): void {
    const syncOp: SyncOperation = {
      id: this.generateId(),
      type: operation,
      entity: entity as any,
      data,
      timestamp: Date.now(),
      retries: 0
    };
    
    this.syncQueue.push(syncOp);
    this.state.pending.push(syncOp);
    
    // Trigger sync if real-time is enabled
    if (this.websocket?.readyState === WebSocket.OPEN) {
      this.pushOperation(syncOp);
    }
  }

  // Conflict Resolution
  private async detectConflicts(remoteChanges: any[]): Promise<SyncConflict[]> {
    const conflicts: SyncConflict[] = [];
    
    for (const change of remoteChanges) {
      const localVersion = await this.getLocalVersion(change.entity, change.id);
      
      if (localVersion && this.hasConflict(localVersion, change)) {
        conflicts.push({
          id: change.id,
          local: localVersion,
          remote: change,
          timestamp: Date.now()
        });
      }
    }
    
    return conflicts;
  }

  private async resolveConflicts(conflicts: SyncConflict[], strategy: string): Promise<void> {
    for (const conflict of conflicts) {
      switch (strategy) {
        case 'local':
          conflict.resolution = 'local';
          break;
        case 'remote':
          conflict.resolution = 'remote';
          break;
        case 'merge':
          conflict.resolution = 'merge';
          await this.mergeConflict(conflict);
          break;
        case 'manual':
          this.state.conflicts.push(conflict);
          this.emit('conflict:detected', conflict);
          break;
      }
    }
  }

  async resolveConflict(conflictId: string, resolution: 'local' | 'remote' | 'merge'): Promise<void> {
    const conflict = this.state.conflicts.find(c => c.id === conflictId);
    if (!conflict) return;
    
    conflict.resolution = resolution;
    
    if (resolution === 'merge') {
      await this.mergeConflict(conflict);
    }
    
    // Apply resolution
    await this.applyConflictResolution(conflict);
    
    // Remove from conflicts
    this.state.conflicts = this.state.conflicts.filter(c => c.id !== conflictId);
    
    this.emit('conflict:resolved', conflict);
  }

  // Data Operations
  private async pullRemoteChanges(): Promise<any[]> {
    const response = await fetch(`${superclaudeConfig.api.baseUrl}/api/sync/pull`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...superclaudeConfig.api.headers
      },
      body: JSON.stringify({
        lastSync: this.state.lastSync,
        entities: ['agent', 'command', 'mcp', 'config']
      })
    });
    
    if (!response.ok) {
      throw new Error('Failed to pull remote changes');
    }
    
    return response.json();
  }

  private async pushLocalChanges(): Promise<void> {
    if (this.state.pending.length === 0) return;
    
    const response = await fetch(`${superclaudeConfig.api.baseUrl}/api/sync/push`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...superclaudeConfig.api.headers
      },
      body: JSON.stringify({
        changes: this.state.pending,
        timestamp: Date.now()
      })
    });
    
    if (!response.ok) {
      throw new Error('Failed to push local changes');
    }
    
    // Clear pending operations
    this.state.pending = [];
  }

  private async applyRemoteChanges(changes: any[]): Promise<void> {
    for (const change of changes) {
      await this.applyChange(change);
    }
  }

  private async applyChange(change: any): Promise<void> {
    const bucket = this.getBucketForEntity(change.entity);
    
    switch (change.type) {
      case 'create':
      case 'update':
        await superclaudeStorage.set(bucket, change.id, change.data);
        break;
      case 'delete':
        await superclaudeStorage.delete(bucket, change.id);
        break;
    }
    
    // Invalidate cache
    superclaudeCache.delete(`${change.entity}:${change.id}`);
  }

  // Cache Sync
  private async syncCache(): Promise<void> {
    // Get cache stats
    const stats = superclaudeCache.getStats();
    
    // Push cache metrics
    await this.pushCacheMetrics(stats);
    
    // Sync frequently accessed items
    const hotKeys = await this.getHotCacheKeys();
    await this.syncHotCacheItems(hotKeys);
  }

  private async pushCacheMetrics(stats: any): Promise<void> {
    await fetch(`${superclaudeConfig.api.baseUrl}/api/sync/metrics`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...superclaudeConfig.api.headers
      },
      body: JSON.stringify({
        cache: stats,
        timestamp: Date.now()
      })
    });
  }

  // Periodic Sync
  private startPeriodicSync(): void {
    const interval = superclaudeConfig.storage.type === 'cloud' ? 60000 : 300000; // 1 min for cloud, 5 min for local
    
    this.syncInterval = setInterval(async () => {
      if (!this.state.syncing) {
        await this.sync({ includeCache: true });
      }
    }, interval);
  }

  // State Management
  private async loadState(): Promise<void> {
    const savedState = await superclaudeStorage.get<SyncState>('system', 'sync-state');
    if (savedState) {
      this.state = savedState;
    }
  }

  private async saveState(): Promise<void> {
    await superclaudeStorage.set('system', 'sync-state', this.state);
  }

  // Helper Methods
  private async getLocalVersion(entity: string, id: string): Promise<any> {
    const bucket = this.getBucketForEntity(entity);
    return superclaudeStorage.get(bucket, id);
  }

  private hasConflict(local: any, remote: any): boolean {
    // Simple conflict detection based on timestamps
    return local.lastModified !== remote.lastModified && 
           local.version !== remote.version;
  }

  private async mergeConflict(conflict: SyncConflict): Promise<void> {
    // Implement merge logic based on entity type
    const merged = this.mergeData(conflict.local, conflict.remote);
    conflict.local = merged;
  }

  private mergeData(local: any, remote: any): any {
    // Simple merge strategy - can be customized per entity type
    return {
      ...remote,
      ...local,
      merged: true,
      mergedAt: Date.now()
    };
  }

  private async applyConflictResolution(conflict: SyncConflict): Promise<void> {
    const data = conflict.resolution === 'local' ? conflict.local : conflict.remote;
    await this.applyChange({
      id: conflict.id,
      type: 'update',
      entity: data.entity,
      data
    });
  }

  private getBucketForEntity(entity: string): string {
    const bucketMap: Record<string, string> = {
      agent: 'agents',
      command: 'commands',
      mcp: 'mcp',
      config: 'config'
    };
    return bucketMap[entity] || 'cache';
  }

  private async processPendingOperations(): Promise<void> {
    for (const operation of this.state.pending) {
      if (operation.retries < this.maxRetries) {
        try {
          await this.pushOperation(operation);
          
          // Remove from pending if successful
          this.state.pending = this.state.pending.filter(op => op.id !== operation.id);
        } catch (error) {
          operation.retries++;
          console.error(`Failed to sync operation ${operation.id}:`, error);
        }
      }
    }
  }

  private async pushOperation(operation: SyncOperation): Promise<void> {
    if (this.websocket?.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify({
        type: 'operation',
        data: operation
      }));
    }
  }

  private async handleRemoteChange(change: any): Promise<void> {
    await this.applyChange(change);
    this.emit('remote:change', change);
  }

  private async handleRemoteConflict(conflict: any): Promise<void> {
    this.state.conflicts.push(conflict);
    this.emit('remote:conflict', conflict);
  }

  private async getHotCacheKeys(): Promise<string[]> {
    // Get most frequently accessed cache keys
    const keys = superclaudeCache.keys();
    return keys.slice(0, 100); // Top 100 keys
  }

  private async syncHotCacheItems(keys: string[]): Promise<void> {
    const items = keys.map(key => ({
      key,
      value: superclaudeCache.get(key)
    }));
    
    // Push hot cache items to remote
    await fetch(`${superclaudeConfig.api.baseUrl}/api/sync/cache`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...superclaudeConfig.api.headers
      },
      body: JSON.stringify({ items })
    });
  }

  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // Event Emitter
  on(event: string, listener: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event)!.add(listener);
  }

  off(event: string, listener: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.delete(listener);
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(listener => listener(data));
    }
  }

  // Public API
  getState(): SyncState {
    return { ...this.state };
  }

  getPendingOperations(): SyncOperation[] {
    return [...this.state.pending];
  }

  getConflicts(): SyncConflict[] {
    return [...this.state.conflicts];
  }

  isConnected(): boolean {
    return this.websocket?.readyState === WebSocket.OPEN;
  }

  isSyncing(): boolean {
    return this.state.syncing;
  }

  getLastSync(): number {
    return this.state.lastSync;
  }

  // Cleanup
  destroy(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
    
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }
    
    this.eventListeners.clear();
  }
}

// Export singleton instance
export const superclaudeSync = SuperClaudeSync.getInstance();