import { superclaudeConfig } from '../config/superclaude-config';

interface StorageItem<T = any> {
  key: string;
  value: T;
  timestamp: number;
  ttl?: number;
  metadata?: Record<string, any>;
}

interface StorageBucket {
  name: string;
  items: Map<string, StorageItem>;
  maxSize: number;
  currentSize: number;
  ttl: number;
}

export class SuperClaudeStorage {
  private static instance: SuperClaudeStorage;
  private buckets: Map<string, StorageBucket>;
  private localStorage: Storage | null;
  private indexedDB: IDBDatabase | null;

  private constructor() {
    this.buckets = new Map();
    this.localStorage = typeof window !== 'undefined' ? window.localStorage : null;
    this.indexedDB = null;
    this.initialize();
  }

  static getInstance(): SuperClaudeStorage {
    if (!SuperClaudeStorage.instance) {
      SuperClaudeStorage.instance = new SuperClaudeStorage();
    }
    return SuperClaudeStorage.instance;
  }

  private async initialize(): Promise<void> {
    // Initialize storage buckets
    this.initializeBuckets();
    
    // Initialize IndexedDB for large data
    await this.initializeIndexedDB();
    
    // Start cleanup interval
    this.startCleanupInterval();
  }

  private initializeBuckets(): void {
    const bucketConfigs = superclaudeConfig.storage.buckets;
    
    for (const [name, config] of Object.entries(bucketConfigs)) {
      this.buckets.set(name, {
        name,
        items: new Map(),
        maxSize: config.maxSize,
        currentSize: 0,
        ttl: config.ttl
      });
    }
  }

  private async initializeIndexedDB(): Promise<void> {
    if (typeof window === 'undefined' || !window.indexedDB) {
      return;
    }

    return new Promise((resolve, reject) => {
      const request = window.indexedDB.open('SuperClaudeDB', 1);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.indexedDB = request.result;
        resolve();
      };
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        // Create object stores for each bucket
        for (const bucketName of this.buckets.keys()) {
          if (!db.objectStoreNames.contains(bucketName)) {
            db.createObjectStore(bucketName, { keyPath: 'key' });
          }
        }
      };
    });
  }

  private startCleanupInterval(): void {
    setInterval(() => {
      this.cleanupExpiredItems();
    }, 60000); // Clean up every minute
  }

  // Storage Operations
  async set<T>(bucketName: string, key: string, value: T, ttl?: number): Promise<void> {
    const bucket = this.buckets.get(bucketName);
    if (!bucket) {
      throw new Error(`Bucket ${bucketName} not found`);
    }

    const item: StorageItem<T> = {
      key,
      value,
      timestamp: Date.now(),
      ttl: ttl || bucket.ttl
    };

    const itemSize = this.calculateSize(item);
    
    // Check if item fits in bucket
    if (itemSize > bucket.maxSize) {
      throw new Error(`Item too large for bucket ${bucketName}`);
    }

    // Make room if necessary
    while (bucket.currentSize + itemSize > bucket.maxSize && bucket.items.size > 0) {
      this.evictOldestItem(bucket);
    }

    // Store in memory
    bucket.items.set(key, item);
    bucket.currentSize += itemSize;

    // Store in persistent storage
    await this.persistItem(bucketName, item);
  }

  async get<T>(bucketName: string, key: string): Promise<T | null> {
    const bucket = this.buckets.get(bucketName);
    if (!bucket) {
      return null;
    }

    // Check memory first
    let item = bucket.items.get(key);
    
    // If not in memory, check persistent storage
    if (!item) {
      item = await this.retrieveItem(bucketName, key);
      if (item) {
        bucket.items.set(key, item);
      }
    }

    if (!item) {
      return null;
    }

    // Check if expired
    if (this.isExpired(item)) {
      await this.delete(bucketName, key);
      return null;
    }

    return item.value as T;
  }

  async delete(bucketName: string, key: string): Promise<boolean> {
    const bucket = this.buckets.get(bucketName);
    if (!bucket) {
      return false;
    }

    const item = bucket.items.get(key);
    if (item) {
      bucket.currentSize -= this.calculateSize(item);
      bucket.items.delete(key);
    }

    // Remove from persistent storage
    await this.removePersistedItem(bucketName, key);
    
    return true;
  }

  async clear(bucketName?: string): Promise<void> {
    if (bucketName) {
      const bucket = this.buckets.get(bucketName);
      if (bucket) {
        bucket.items.clear();
        bucket.currentSize = 0;
        await this.clearPersistedBucket(bucketName);
      }
    } else {
      // Clear all buckets
      for (const bucket of this.buckets.values()) {
        bucket.items.clear();
        bucket.currentSize = 0;
      }
      await this.clearAllPersistedData();
    }
  }

  async list(bucketName: string): Promise<string[]> {
    const bucket = this.buckets.get(bucketName);
    if (!bucket) {
      return [];
    }

    const keys = Array.from(bucket.items.keys());
    
    // Also get keys from persistent storage
    const persistedKeys = await this.listPersistedKeys(bucketName);
    
    // Merge and deduplicate
    return Array.from(new Set([...keys, ...persistedKeys]));
  }

  // Batch Operations
  async setMany<T>(bucketName: string, items: Array<{ key: string; value: T; ttl?: number }>): Promise<void> {
    for (const item of items) {
      await this.set(bucketName, item.key, item.value, item.ttl);
    }
  }

  async getMany<T>(bucketName: string, keys: string[]): Promise<Map<string, T | null>> {
    const results = new Map<string, T | null>();
    
    for (const key of keys) {
      const value = await this.get<T>(bucketName, key);
      results.set(key, value);
    }
    
    return results;
  }

  async deleteMany(bucketName: string, keys: string[]): Promise<number> {
    let deleted = 0;
    
    for (const key of keys) {
      if (await this.delete(bucketName, key)) {
        deleted++;
      }
    }
    
    return deleted;
  }

  // Query Operations
  async query<T>(bucketName: string, predicate: (item: StorageItem<T>) => boolean): Promise<T[]> {
    const bucket = this.buckets.get(bucketName);
    if (!bucket) {
      return [];
    }

    const results: T[] = [];
    
    for (const item of bucket.items.values()) {
      if (!this.isExpired(item) && predicate(item as StorageItem<T>)) {
        results.push(item.value as T);
      }
    }
    
    return results;
  }

  // Statistics
  getStats(bucketName?: string): any {
    if (bucketName) {
      const bucket = this.buckets.get(bucketName);
      if (!bucket) {
        return null;
      }
      
      return {
        name: bucket.name,
        itemCount: bucket.items.size,
        currentSize: bucket.currentSize,
        maxSize: bucket.maxSize,
        utilization: (bucket.currentSize / bucket.maxSize) * 100
      };
    }
    
    // Get stats for all buckets
    const stats: any = {};
    
    for (const [name, bucket] of this.buckets) {
      stats[name] = {
        itemCount: bucket.items.size,
        currentSize: bucket.currentSize,
        maxSize: bucket.maxSize,
        utilization: (bucket.currentSize / bucket.maxSize) * 100
      };
    }
    
    return stats;
  }

  // Private Helper Methods
  private calculateSize(item: StorageItem): number {
    const str = JSON.stringify(item);
    return str.length * 2; // Approximate size in bytes (UTF-16)
  }

  private isExpired(item: StorageItem): boolean {
    if (!item.ttl) {
      return false;
    }
    
    return Date.now() - item.timestamp > item.ttl;
  }

  private evictOldestItem(bucket: StorageBucket): void {
    let oldestKey: string | null = null;
    let oldestTimestamp = Infinity;
    
    for (const [key, item] of bucket.items) {
      if (item.timestamp < oldestTimestamp) {
        oldestTimestamp = item.timestamp;
        oldestKey = key;
      }
    }
    
    if (oldestKey) {
      const item = bucket.items.get(oldestKey);
      if (item) {
        bucket.currentSize -= this.calculateSize(item);
        bucket.items.delete(oldestKey);
      }
    }
  }

  private cleanupExpiredItems(): void {
    for (const bucket of this.buckets.values()) {
      const expiredKeys: string[] = [];
      
      for (const [key, item] of bucket.items) {
        if (this.isExpired(item)) {
          expiredKeys.push(key);
        }
      }
      
      for (const key of expiredKeys) {
        this.delete(bucket.name, key);
      }
    }
  }

  // Persistence Methods
  private async persistItem(bucketName: string, item: StorageItem): Promise<void> {
    // Try IndexedDB first
    if (this.indexedDB) {
      try {
        const transaction = this.indexedDB.transaction([bucketName], 'readwrite');
        const store = transaction.objectStore(bucketName);
        await this.promisifyRequest(store.put(item));
        return;
      } catch (error) {
        console.error('IndexedDB persist failed:', error);
      }
    }
    
    // Fallback to localStorage
    if (this.localStorage) {
      const key = `superclaude:${bucketName}:${item.key}`;
      try {
        this.localStorage.setItem(key, JSON.stringify(item));
      } catch (error) {
        console.error('LocalStorage persist failed:', error);
      }
    }
  }

  private async retrieveItem(bucketName: string, key: string): Promise<StorageItem | null> {
    // Try IndexedDB first
    if (this.indexedDB) {
      try {
        const transaction = this.indexedDB.transaction([bucketName], 'readonly');
        const store = transaction.objectStore(bucketName);
        const item = await this.promisifyRequest(store.get(key));
        if (item) {
          return item;
        }
      } catch (error) {
        console.error('IndexedDB retrieve failed:', error);
      }
    }
    
    // Fallback to localStorage
    if (this.localStorage) {
      const storageKey = `superclaude:${bucketName}:${key}`;
      const data = this.localStorage.getItem(storageKey);
      if (data) {
        try {
          return JSON.parse(data);
        } catch (error) {
          console.error('LocalStorage parse failed:', error);
        }
      }
    }
    
    return null;
  }

  private async removePersistedItem(bucketName: string, key: string): Promise<void> {
    // Remove from IndexedDB
    if (this.indexedDB) {
      try {
        const transaction = this.indexedDB.transaction([bucketName], 'readwrite');
        const store = transaction.objectStore(bucketName);
        await this.promisifyRequest(store.delete(key));
      } catch (error) {
        console.error('IndexedDB remove failed:', error);
      }
    }
    
    // Remove from localStorage
    if (this.localStorage) {
      const storageKey = `superclaude:${bucketName}:${key}`;
      this.localStorage.removeItem(storageKey);
    }
  }

  private async clearPersistedBucket(bucketName: string): Promise<void> {
    // Clear IndexedDB
    if (this.indexedDB) {
      try {
        const transaction = this.indexedDB.transaction([bucketName], 'readwrite');
        const store = transaction.objectStore(bucketName);
        await this.promisifyRequest(store.clear());
      } catch (error) {
        console.error('IndexedDB clear failed:', error);
      }
    }
    
    // Clear localStorage
    if (this.localStorage) {
      const prefix = `superclaude:${bucketName}:`;
      const keysToRemove: string[] = [];
      
      for (let i = 0; i < this.localStorage.length; i++) {
        const key = this.localStorage.key(i);
        if (key?.startsWith(prefix)) {
          keysToRemove.push(key);
        }
      }
      
      for (const key of keysToRemove) {
        this.localStorage.removeItem(key);
      }
    }
  }

  private async clearAllPersistedData(): Promise<void> {
    for (const bucketName of this.buckets.keys()) {
      await this.clearPersistedBucket(bucketName);
    }
  }

  private async listPersistedKeys(bucketName: string): Promise<string[]> {
    const keys: string[] = [];
    
    // Get from IndexedDB
    if (this.indexedDB) {
      try {
        const transaction = this.indexedDB.transaction([bucketName], 'readonly');
        const store = transaction.objectStore(bucketName);
        const allKeys = await this.promisifyRequest(store.getAllKeys());
        keys.push(...(allKeys as string[]));
      } catch (error) {
        console.error('IndexedDB list failed:', error);
      }
    }
    
    // Get from localStorage
    if (this.localStorage) {
      const prefix = `superclaude:${bucketName}:`;
      
      for (let i = 0; i < this.localStorage.length; i++) {
        const key = this.localStorage.key(i);
        if (key?.startsWith(prefix)) {
          keys.push(key.substring(prefix.length));
        }
      }
    }
    
    return keys;
  }

  private promisifyRequest<T>(request: IDBRequest<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }
}

// Export singleton instance
export const superclaudeStorage = SuperClaudeStorage.getInstance();