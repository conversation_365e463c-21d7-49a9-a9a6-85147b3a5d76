interface CacheEntry<T = any> {
  key: string;
  value: T;
  timestamp: number;
  ttl: number;
  hits: number;
  lastAccessed: number;
  size: number;
  tags?: string[];
}

interface CacheOptions {
  ttl?: number;
  tags?: string[];
  compress?: boolean;
}

interface CacheStats {
  hits: number;
  misses: number;
  evictions: number;
  size: number;
  itemCount: number;
  hitRate: number;
}

export class SuperClaudeCache {
  private static instance: SuperClaudeCache;
  private cache: Map<string, CacheEntry>;
  private maxSize: number;
  private currentSize: number;
  private defaultTTL: number;
  private stats: CacheStats;
  private tagIndex: Map<string, Set<string>>;
  private accessQueue: string[];
  private cleanupInterval: NodeJS.Timeout | null;

  private constructor() {
    this.cache = new Map();
    this.maxSize = 100 * 1024 * 1024; // 100MB default
    this.currentSize = 0;
    this.defaultTTL = 3600000; // 1 hour default
    this.stats = {
      hits: 0,
      misses: 0,
      evictions: 0,
      size: 0,
      itemCount: 0,
      hitRate: 0
    };
    this.tagIndex = new Map();
    this.accessQueue = [];
    this.cleanupInterval = null;
    this.initialize();
  }

  static getInstance(): SuperClaudeCache {
    if (!SuperClaudeCache.instance) {
      SuperClaudeCache.instance = new SuperClaudeCache();
    }
    return SuperClaudeCache.instance;
  }

  private initialize(): void {
    // Start cleanup interval
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 60000); // Cleanup every minute
  }

  // Core Cache Operations
  set<T>(key: string, value: T, options: CacheOptions = {}): boolean {
    try {
      const ttl = options.ttl || this.defaultTTL;
      const tags = options.tags || [];
      const size = this.calculateSize(value);

      // Check if item is too large
      if (size > this.maxSize) {
        return false;
      }

      // Make room if necessary
      while (this.currentSize + size > this.maxSize && this.cache.size > 0) {
        this.evictLRU();
      }

      // Remove old entry if exists
      if (this.cache.has(key)) {
        this.delete(key);
      }

      // Create new entry
      const entry: CacheEntry<T> = {
        key,
        value: options.compress ? this.compress(value) : value,
        timestamp: Date.now(),
        ttl,
        hits: 0,
        lastAccessed: Date.now(),
        size,
        tags
      };

      // Add to cache
      this.cache.set(key, entry);
      this.currentSize += size;
      this.stats.itemCount++;
      this.stats.size = this.currentSize;

      // Update tag index
      for (const tag of tags) {
        if (!this.tagIndex.has(tag)) {
          this.tagIndex.set(tag, new Set());
        }
        this.tagIndex.get(tag)!.add(key);
      }

      // Add to access queue
      this.updateAccessQueue(key);

      return true;
    } catch (error) {
      console.error('Cache set error:', error);
      return false;
    }
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);

    if (!entry) {
      this.stats.misses++;
      this.updateHitRate();
      return null;
    }

    // Check if expired
    if (this.isExpired(entry)) {
      this.delete(key);
      this.stats.misses++;
      this.updateHitRate();
      return null;
    }

    // Update stats
    entry.hits++;
    entry.lastAccessed = Date.now();
    this.stats.hits++;
    this.updateHitRate();

    // Update access queue
    this.updateAccessQueue(key);

    // Return value (decompress if needed)
    return this.isCompressed(entry.value) ? this.decompress(entry.value) : entry.value;
  }

  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    if (this.isExpired(entry)) {
      this.delete(key);
      return false;
    }
    
    return true;
  }

  delete(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;

    // Remove from cache
    this.cache.delete(key);
    this.currentSize -= entry.size;
    this.stats.itemCount--;
    this.stats.size = this.currentSize;

    // Remove from tag index
    if (entry.tags) {
      for (const tag of entry.tags) {
        const tagSet = this.tagIndex.get(tag);
        if (tagSet) {
          tagSet.delete(key);
          if (tagSet.size === 0) {
            this.tagIndex.delete(tag);
          }
        }
      }
    }

    // Remove from access queue
    const index = this.accessQueue.indexOf(key);
    if (index > -1) {
      this.accessQueue.splice(index, 1);
    }

    return true;
  }

  clear(): void {
    this.cache.clear();
    this.tagIndex.clear();
    this.accessQueue = [];
    this.currentSize = 0;
    this.stats.itemCount = 0;
    this.stats.size = 0;
  }

  // Batch Operations
  mget<T>(keys: string[]): Map<string, T | null> {
    const results = new Map<string, T | null>();
    
    for (const key of keys) {
      results.set(key, this.get<T>(key));
    }
    
    return results;
  }

  mset<T>(entries: Array<{ key: string; value: T; options?: CacheOptions }>): Map<string, boolean> {
    const results = new Map<string, boolean>();
    
    for (const { key, value, options } of entries) {
      results.set(key, this.set(key, value, options));
    }
    
    return results;
  }

  mdelete(keys: string[]): number {
    let deleted = 0;
    
    for (const key of keys) {
      if (this.delete(key)) {
        deleted++;
      }
    }
    
    return deleted;
  }

  // Tag Operations
  getByTag<T>(tag: string): Map<string, T | null> {
    const keys = this.tagIndex.get(tag);
    if (!keys) return new Map();
    
    const results = new Map<string, T | null>();
    
    for (const key of keys) {
      results.set(key, this.get<T>(key));
    }
    
    return results;
  }

  deleteByTag(tag: string): number {
    const keys = this.tagIndex.get(tag);
    if (!keys) return 0;
    
    let deleted = 0;
    
    for (const key of Array.from(keys)) {
      if (this.delete(key)) {
        deleted++;
      }
    }
    
    return deleted;
  }

  // Pattern Matching
  keys(pattern?: string): string[] {
    const keys: string[] = [];
    
    for (const [key, entry] of this.cache) {
      if (!this.isExpired(entry)) {
        if (!pattern || this.matchPattern(key, pattern)) {
          keys.push(key);
        }
      }
    }
    
    return keys;
  }

  values<T>(pattern?: string): T[] {
    const values: T[] = [];
    
    for (const [key, entry] of this.cache) {
      if (!this.isExpired(entry)) {
        if (!pattern || this.matchPattern(key, pattern)) {
          const value = this.isCompressed(entry.value) 
            ? this.decompress(entry.value) 
            : entry.value;
          values.push(value);
        }
      }
    }
    
    return values;
  }

  // TTL Management
  ttl(key: string): number {
    const entry = this.cache.get(key);
    if (!entry) return -1;
    
    const remaining = entry.ttl - (Date.now() - entry.timestamp);
    return remaining > 0 ? remaining : -1;
  }

  expire(key: string, ttl: number): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    entry.ttl = ttl;
    entry.timestamp = Date.now();
    return true;
  }

  persist(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    entry.ttl = Infinity;
    return true;
  }

  // Statistics
  getStats(): CacheStats {
    return { ...this.stats };
  }

  getSize(): number {
    return this.currentSize;
  }

  getItemCount(): number {
    return this.cache.size;
  }

  getMaxSize(): number {
    return this.maxSize;
  }

  setMaxSize(size: number): void {
    this.maxSize = size;
    
    // Evict items if necessary
    while (this.currentSize > this.maxSize && this.cache.size > 0) {
      this.evictLRU();
    }
  }

  // Memory Management
  memory(): { used: number; available: number; usage: number } {
    return {
      used: this.currentSize,
      available: this.maxSize - this.currentSize,
      usage: (this.currentSize / this.maxSize) * 100
    };
  }

  trim(maxSize?: number): number {
    const targetSize = maxSize || this.maxSize;
    let evicted = 0;
    
    while (this.currentSize > targetSize && this.cache.size > 0) {
      this.evictLRU();
      evicted++;
    }
    
    return evicted;
  }

  // Private Helper Methods
  private calculateSize(value: any): number {
    const str = JSON.stringify(value);
    return str.length * 2; // Approximate size in bytes
  }

  private isExpired(entry: CacheEntry): boolean {
    if (entry.ttl === Infinity) return false;
    return Date.now() - entry.timestamp > entry.ttl;
  }

  private evictLRU(): void {
    if (this.accessQueue.length === 0) return;
    
    const key = this.accessQueue.shift();
    if (key) {
      this.delete(key);
      this.stats.evictions++;
    }
  }

  private updateAccessQueue(key: string): void {
    // Remove from current position
    const index = this.accessQueue.indexOf(key);
    if (index > -1) {
      this.accessQueue.splice(index, 1);
    }
    
    // Add to end (most recently used)
    this.accessQueue.push(key);
  }

  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0;
  }

  private cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];
    
    for (const [key, entry] of this.cache) {
      if (this.isExpired(entry)) {
        keysToDelete.push(key);
      }
    }
    
    for (const key of keysToDelete) {
      this.delete(key);
    }
  }

  private matchPattern(key: string, pattern: string): boolean {
    // Simple wildcard pattern matching
    const regex = new RegExp('^' + pattern.replace(/\*/g, '.*') + '$');
    return regex.test(key);
  }

  private compress(value: any): any {
    // Simplified compression (in real implementation, use a proper compression library)
    return { compressed: true, data: JSON.stringify(value) };
  }

  private decompress(value: any): any {
    // Simplified decompression
    if (value && value.compressed) {
      return JSON.parse(value.data);
    }
    return value;
  }

  private isCompressed(value: any): boolean {
    return value && value.compressed === true;
  }

  // Cleanup
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.clear();
  }
}

// Export singleton instance
export const superclaudeCache = SuperClaudeCache.getInstance();