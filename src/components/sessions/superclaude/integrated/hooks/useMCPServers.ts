import { useState, useEffect, useCallback } from 'react';
import { api } from '@/lib/api';
import { mcpConfigStorage, MCPServerConfig } from '../services/mcpConfigStorage';

export interface MCPServer {
  id: string;
  name: string;
  status: 'connected' | 'connecting' | 'disconnected' | 'error';
  latency?: number;
  operations?: number;
  error?: string;
  lastConnected?: Date;
  capabilities?: string[];
  version?: string;
  config?: any;
}

export interface MCPResource {
  uri: string;
  name: string;
  mimeType?: string;
  description?: string;
  server?: string;
  tags?: string[];
  size?: number;
  lastModified?: Date;
  metadata?: Record<string, any>;
}

export interface MCPTool {
  name: string;
  description?: string;
  inputSchema?: any;
}

// Cache for resources and tools
const resourceCache = new Map<string, { data: MCPResource[], timestamp: number }>();
const toolCache = new Map<string, { data: MCPTool[], timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

export const useMCPServers = () => {
  const [servers, setServers] = useState<MCPServer[]>([]);
  const [resources, setResources] = useState<MCPResource[]>([]);
  const [tools, setTools] = useState<MCPTool[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  // Load saved configurations on mount
  useEffect(() => {
    const savedConfig = mcpConfigStorage.load();
    if (savedConfig && savedConfig.servers.length > 0) {
      // Apply saved configurations
      savedConfig.servers.forEach(config => {
        if (config.autoConnect) {
          // Mark for auto-connection
          console.log('Auto-connecting to saved server:', config.name);
        }
      });
    }
  }, []);

  // Fetch MCP servers and their status
  const fetchServers = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Get configured MCP servers
      const configuredServers = await api.getMCPServers();
      
      // Load saved configurations
      const savedConfig = mcpConfigStorage.load();
      
      // Transform to our format and check health
      const serverPromises = configuredServers.map(async (server: any) => {
        let status: MCPServer['status'] = 'disconnected';
        let latency: number | undefined;
        let error: string | undefined;
        
        // Check if we have saved config for this server
        const savedServerConfig = savedConfig?.servers.find(s => s.id === server.name);
        const shouldAutoConnect = savedServerConfig?.autoConnect ?? true;
        
        try {
          // Only connect if auto-connect is enabled
          if (shouldAutoConnect) {
            // Try to connect/ping the server
            const startTime = Date.now();
            const health = await api.checkMCPServerHealth(server.name);
            latency = Date.now() - startTime;
            
            if (health.connected) {
              status = 'connected';
            } else if (health.connecting) {
              status = 'connecting';
            } else {
              status = 'disconnected';
              error = health.error;
            }
          }
        } catch (err) {
          status = 'error';
          error = err instanceof Error ? err.message : 'Unknown error';
        }
        
        // Save configuration if it's new
        if (!savedServerConfig) {
          mcpConfigStorage.updateServerConfig(server.name, {
            name: server.name,
            enabled: true,
            autoConnect: true,
            retryAttempts: 3,
            timeout: 30000
          });
        }
        
        return {
          id: server.name,
          name: server.name,
          status,
          latency,
          operations: 0, // Will be updated from metrics
          error,
          lastConnected: status === 'connected' ? new Date() : undefined,
          capabilities: server.capabilities || [],
          version: server.version,
          config: savedServerConfig || server.config
        };
      });
      
      const updatedServers = await Promise.all(serverPromises);
      setServers(updatedServers);
      
      // Fetch resources and tools from connected servers
      const connectedServers = updatedServers.filter(s => s.status === 'connected');
      if (connectedServers.length > 0) {
        await fetchResourcesAndTools(connectedServers);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch MCP servers');
      console.error('Error fetching MCP servers:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Check if cache is valid
  const isCacheValid = (timestamp: number): boolean => {
    return Date.now() - timestamp < CACHE_TTL;
  };

  // Fetch resources and tools from connected servers with caching
  const fetchResourcesAndTools = async (connectedServers: MCPServer[]) => {
    try {
      const resourcePromises = connectedServers.map(async (server) => {
        const cacheKey = `resources:${server.name}`;
        const cached = resourceCache.get(cacheKey);
        
        if (cached && isCacheValid(cached.timestamp)) {
          return cached.data;
        }
        
        const data = await api.getMCPServerResources(server.name).catch(() => []);
        resourceCache.set(cacheKey, { data, timestamp: Date.now() });
        return data;
      });
      
      const toolPromises = connectedServers.map(async (server) => {
        const cacheKey = `tools:${server.name}`;
        const cached = toolCache.get(cacheKey);
        
        if (cached && isCacheValid(cached.timestamp)) {
          return cached.data;
        }
        
        const data = await api.getMCPServerTools(server.name).catch(() => []);
        toolCache.set(cacheKey, { data, timestamp: Date.now() });
        return data;
      });
      
      const [resourceResults, toolResults] = await Promise.all([
        Promise.all(resourcePromises),
        Promise.all(toolPromises)
      ]);
      
      // Flatten and deduplicate resources, adding server info
      const allResources = resourceResults.flatMap((resources, index) => 
        resources.map(r => ({ ...r, server: connectedServers[index].name }))
      );
      const uniqueResources = Array.from(
        new Map(allResources.map(r => [r.uri, r])).values()
      );
      setResources(uniqueResources);
      
      // Flatten and deduplicate tools
      const allTools = toolResults.flat();
      const uniqueTools = Array.from(
        new Map(allTools.map(t => [t.name, t])).values()
      );
      setTools(uniqueTools);
      
      setLastRefresh(new Date());
    } catch (err) {
      console.error('Error fetching resources and tools:', err);
    }
  };

  // Clear cache
  const clearCache = useCallback(() => {
    resourceCache.clear();
    toolCache.clear();
    setLastRefresh(new Date());
  }, []);

  // Reconnect to a server
  const reconnectServer = useCallback(async (serverId: string) => {
    const server = servers.find(s => s.id === serverId);
    if (!server) return;
    
    // Clear cache for this server
    resourceCache.delete(`resources:${server.name}`);
    toolCache.delete(`tools:${server.name}`);
    
    // Update status to connecting
    setServers(prev => prev.map(s => 
      s.id === serverId ? { ...s, status: 'connecting' as const } : s
    ));
    
    try {
      await api.reconnectMCPServer(server.name);
      
      // Check health after reconnection
      const health = await api.checkMCPServerHealth(server.name);
      
      setServers(prev => prev.map(s => 
        s.id === serverId 
          ? { 
              ...s, 
              status: health.connected ? 'connected' : 'disconnected',
              error: health.error,
              lastConnected: health.connected ? new Date() : s.lastConnected
            } 
          : s
      ));
      
      // Refresh resources and tools if connected
      if (health.connected) {
        const connectedServers = servers.filter(s => 
          s.id === serverId || s.status === 'connected'
        );
        await fetchResourcesAndTools(connectedServers);
      }
    } catch (err) {
      setServers(prev => prev.map(s => 
        s.id === serverId 
          ? { 
              ...s, 
              status: 'error',
              error: err instanceof Error ? err.message : 'Reconnection failed'
            } 
          : s
      ));
    }
  }, [servers]);

  // Configure a server
  const configureServer = useCallback(async (serverId: string, config: any) => {
    const server = servers.find(s => s.id === serverId);
    if (!server) return;
    
    try {
      // Save configuration to storage
      mcpConfigStorage.updateServerConfig(serverId, config);
      
      // Apply configuration via API
      await api.configureMCPServer(server.name, config);
      
      // Update local state
      setServers(prev => prev.map(s => 
        s.id === serverId ? { ...s, config } : s
      ));
      
      // Reconnect with new config
      await reconnectServer(serverId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Configuration failed');
    }
  }, [servers, reconnectServer]);

  // Execute a tool
  const executeTool = useCallback(async (toolName: string, args: any) => {
    try {
      const result = await api.executeMCPTool(toolName, args);
      return result;
    } catch (err) {
      throw err;
    }
  }, []);

  // Read a resource
  const readResource = useCallback(async (uri: string) => {
    try {
      const content = await api.readMCPResource(uri);
      return content;
    } catch (err) {
      throw err;
    }
  }, []);

  // Auto-refresh server status
  useEffect(() => {
    fetchServers();
    
    // Refresh every 30 seconds
    const interval = setInterval(fetchServers, 30000);
    
    return () => clearInterval(interval);
  }, [fetchServers]);

  // Update operation counts from metrics
  useEffect(() => {
    const updateMetrics = async () => {
      try {
        const metrics = await api.getMCPMetrics();
        
        setServers(prev => prev.map(server => {
          const serverMetrics = metrics[server.name];
          if (serverMetrics) {
            return {
              ...server,
              operations: serverMetrics.totalOperations || 0,
              latency: serverMetrics.avgLatency || server.latency
            };
          }
          return server;
        }));
      } catch (err) {
        console.error('Error updating metrics:', err);
      }
    };
    
    updateMetrics();
    const interval = setInterval(updateMetrics, 5000);
    
    return () => clearInterval(interval);
  }, []);

  return {
    servers,
    resources,
    tools,
    isLoading,
    error,
    lastRefresh,
    reconnectServer,
    configureServer,
    executeTool,
    readResource,
    refresh: fetchServers,
    clearCache
  };
};