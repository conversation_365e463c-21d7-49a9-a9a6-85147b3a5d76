// SuperClaude Configuration
export const superclaudeConfig = {
  // Agent Configuration
  agents: {
    enabled: true,
    maxConcurrent: 3,
    timeout: 30000,
    retryAttempts: 2,
    
    // Individual agent settings
    settings: {
      'backend-architect': {
        enabled: true,
        priority: 1,
        maxExecutionTime: 60000
      },
      'frontend-architect': {
        enabled: true,
        priority: 1,
        maxExecutionTime: 60000
      },
      'security-engineer': {
        enabled: true,
        priority: 2,
        maxExecutionTime: 45000
      },
      'quality-engineer': {
        enabled: true,
        priority: 2,
        maxExecutionTime: 45000
      },
      'python-expert': {
        enabled: true,
        priority: 1,
        maxExecutionTime: 30000
      },
      'system-architect': {
        enabled: true,
        priority: 1,
        maxExecutionTime: 60000
      },
      'refactoring-expert': {
        enabled: true,
        priority: 2,
        maxExecutionTime: 45000
      },
      'learning-guide': {
        enabled: true,
        priority: 3,
        maxExecutionTime: 30000
      },
      'technical-writer': {
        enabled: true,
        priority: 3,
        maxExecutionTime: 30000
      },
      'socratic-mentor': {
        enabled: true,
        priority: 3,
        maxExecutionTime: 30000
      },
      'root-cause-analyst': {
        enabled: true,
        priority: 1,
        maxExecutionTime: 60000
      },
      'performance-engineer': {
        enabled: true,
        priority: 1,
        maxExecutionTime: 45000
      },
      'requirements-analyst': {
        enabled: true,
        priority: 2,
        maxExecutionTime: 45000
      },
      'devops-architect': {
        enabled: true,
        priority: 1,
        maxExecutionTime: 60000
      }
    }
  },

  // MCP Server Configuration
  mcpServers: {
    enabled: true,
    autoConnect: true,
    reconnectInterval: 5000,
    maxReconnectAttempts: 3,
    
    // Individual server settings
    servers: {
      github: {
        enabled: true,
        autoConnect: true,
        priority: 1,
        timeout: 30000,
        credentials: {
          token: (import.meta as any).env?.VITE_GITHUB_TOKEN || ''
        }
      },
      database: {
        enabled: true,
        autoConnect: true,
        priority: 1,
        timeout: 10000,
        credentials: {
          postgresql: (import.meta as any).env?.VITE_POSTGRES_URL || '',
          mysql: (import.meta as any).env?.VITE_MYSQL_URL || '',
          mongodb: (import.meta as any).env?.VITE_MONGODB_URL || ''
        }
      },
      filesystem: {
        enabled: true,
        autoConnect: true,
        priority: 1,
        timeout: 5000,
        paths: {
          allowed: ['./src', './config', './public'],
          restricted: ['./node_modules', './.git']
        }
      },
      api: {
        enabled: true,
        autoConnect: false,
        priority: 2,
        timeout: 30000,
        rateLimits: {
          default: { requests: 100, window: 60000 }
        }
      },
      docker: {
        enabled: true,
        autoConnect: false,
        priority: 2,
        timeout: 60000,
        socketPath: typeof window !== 'undefined' && window.navigator.platform.includes('Win')
          ? '//./pipe/docker_engine'
          : '/var/run/docker.sock'
      },
      ml: {
        enabled: true,
        autoConnect: false,
        priority: 3,
        timeout: 120000,
        gpu: {
          enabled: true,
          memory: 8192
        }
      }
    }
  },

  // Command Configuration
  commands: {
    prefix: '/sc:',
    suggestionsLimit: 30,
    historyLimit: 100,
    
    // Command categories
    categories: {
      orchestration: {
        enabled: true,
        commands: ['spawn', 'delegate', 'coordinate']
      },
      analysis: {
        enabled: true,
        commands: ['analyze', 'review', 'optimize']
      },
      generation: {
        enabled: true,
        commands: ['generate', 'create', 'scaffold']
      },
      management: {
        enabled: true,
        commands: ['plan', 'track', 'report']
      },
      learning: {
        enabled: true,
        commands: ['explain', 'teach', 'document']
      }
    }
  },

  // Behavioral Modes Configuration
  modes: {
    default: 'orchestration',
    available: [
      {
        id: 'brainstorming',
        name: 'Brainstorming',
        description: 'Creative ideation and exploration',
        settings: {
          creativity: 0.8,
          diversity: 0.9,
          constraints: 'minimal'
        }
      },
      {
        id: 'orchestration',
        name: 'Orchestration',
        description: 'Coordinated multi-agent execution',
        settings: {
          parallelism: true,
          coordination: 'high',
          efficiency: 0.7
        }
      },
      {
        id: 'token-efficiency',
        name: 'Token Efficiency',
        description: 'Optimized for minimal token usage',
        settings: {
          compression: 'high',
          verbosity: 'low',
          summaries: true
        }
      },
      {
        id: 'task-management',
        name: 'Task Management',
        description: 'Project and task organization',
        settings: {
          tracking: true,
          prioritization: true,
          automation: 0.6
        }
      },
      {
        id: 'introspection',
        name: 'Introspection',
        description: 'Deep analysis and reflection',
        settings: {
          depth: 'deep',
          reasoning: 'explicit',
          validation: true
        }
      }
    ]
  },

  // Storage Configuration
  storage: {
    type: 'local',
    path: '.superclaude',
    encryption: false,
    
    // Storage buckets
    buckets: {
      commands: {
        maxSize: 10485760, // 10MB
        ttl: 604800000 // 7 days
      },
      agents: {
        maxSize: 52428800, // 50MB
        ttl: 2592000000 // 30 days
      },
      mcp: {
        maxSize: 104857600, // 100MB
        ttl: 86400000 // 1 day
      },
      cache: {
        maxSize: 209715200, // 200MB
        ttl: 3600000 // 1 hour
      }
    }
  },

  // Performance Configuration
  performance: {
    caching: {
      enabled: true,
      strategy: 'lru',
      maxItems: 1000,
      ttl: 3600000
    },
    throttling: {
      enabled: true,
      maxRequestsPerSecond: 10,
      burstLimit: 20
    },
    monitoring: {
      enabled: true,
      metricsInterval: 60000,
      alertThresholds: {
        errorRate: 0.05,
        latency: 5000,
        memoryUsage: 0.9
      }
    }
  },

  // Security Configuration
  security: {
    authentication: {
      required: false,
      method: 'token',
      tokenExpiry: 86400000
    },
    encryption: {
      enabled: false,
      algorithm: 'aes-256-gcm'
    },
    permissions: {
      agents: ['execute', 'configure'],
      mcp: ['connect', 'execute', 'configure'],
      commands: ['execute', 'create', 'modify']
    }
  },

  // UI Configuration
  ui: {
    theme: {
      mode: 'dark',
      accent: '#8B5CF6',
      animations: true
    },
    layout: {
      sidebar: true,
      commandPalette: true,
      statusBar: true,
      floatingInput: true
    },
    notifications: {
      enabled: true,
      position: 'top-right',
      duration: 5000,
      maxVisible: 3
    }
  },

  // Feature Flags
  features: {
    experimentalAgents: false,
    advancedMCP: true,
    commandChaining: true,
    visualWorkflows: false,
    aiSuggestions: true,
    collaborativeMode: false,
    voiceCommands: false,
    codeGeneration: true,
    autoCompletion: true,
    semanticSearch: true
  },

  // Logging Configuration
  logging: {
    level: 'info',
    console: true,
    file: true,
    path: './logs/superclaude.log',
    maxFiles: 5,
    maxSize: 10485760, // 10MB
    format: 'json'
  },

  // API Configuration
  api: {
    baseUrl: (import.meta as any).env?.VITE_SUPERCLAUDE_API_URL || 'http://localhost:3000',
    version: 'v1',
    timeout: 30000,
    retries: 3,
    headers: {
      'X-SuperClaude-Version': '1.0.0'
    }
  },

  // Development Configuration
  development: {
    debug: (import.meta as any).env?.MODE === 'development',
    hotReload: true,
    verboseLogging: false,
    mockData: false,
    simulateLatency: false,
    errorReporting: true
  }
};

// Configuration validation
export function validateConfig(config: typeof superclaudeConfig): boolean {
  // Validate required fields
  if (!config.agents || !config.mcpServers || !config.commands) {
    console.error('Missing required configuration sections');
    return false;
  }

  // Validate agent settings
  for (const [agentId, settings] of Object.entries(config.agents.settings)) {
    if (!settings.enabled === undefined || !settings.priority || !settings.maxExecutionTime) {
      console.error(`Invalid configuration for agent: ${agentId}`);
      return false;
    }
  }

  // Validate MCP server settings
  for (const [serverId, settings] of Object.entries(config.mcpServers.servers)) {
    if (!settings.enabled === undefined || !settings.priority || !settings.timeout) {
      console.error(`Invalid configuration for MCP server: ${serverId}`);
      return false;
    }
  }

  return true;
}

// Configuration loader
export async function loadConfig(): Promise<typeof superclaudeConfig> {
  // Load from environment variables
  const envConfig = {
    agents: {
      enabled: (import.meta as any).env?.VITE_SUPERCLAUDE_AGENTS_ENABLED !== 'false',
      maxConcurrent: parseInt((import.meta as any).env?.VITE_SUPERCLAUDE_MAX_CONCURRENT || '3')
    },
    mcpServers: {
      enabled: (import.meta as any).env?.VITE_SUPERCLAUDE_MCP_ENABLED !== 'false',
      autoConnect: (import.meta as any).env?.VITE_SUPERCLAUDE_MCP_AUTOCONNECT !== 'false'
    }
  };

  // Merge with default config
  const config = { ...superclaudeConfig, ...envConfig };

  // Validate configuration
  if (!validateConfig(config)) {
    throw new Error('Invalid SuperClaude configuration');
  }

  return config;
}

// Configuration hot reload
export function watchConfig(callback: (config: typeof superclaudeConfig) => void): () => void {
  if (!superclaudeConfig.development.hotReload) {
    return () => {};
  }

  // Watch for configuration changes
  const interval = setInterval(async () => {
    const newConfig = await loadConfig();
    callback(newConfig);
  }, 5000);

  return () => clearInterval(interval);
}

export default superclaudeConfig;