import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useSuperClaudeStore } from './stores/superclaudeStore';
import { SuperClaudePanel } from './ui/SuperClaudePanel';
import { AgentOrchestrator } from './core/AgentOrchestrator';
import { CommandRegistry } from './commands/CommandRegistry';
import { MCPIntegration } from './mcp/MCPIntegration';
import { BehavioralMode } from './modes';

export interface SuperClaudeIntegrationProps {
  sessionId: string;
  onCommandProcessed?: (command: any) => void;
  onAgentResponse?: (agentId: string, response: any) => void;
  onStateChange?: (status: string) => void;
  initialMode?: BehavioralMode;
  enableMCP?: boolean;
  className?: string;
}

export const SuperClaudeIntegration: React.FC<SuperClaudeIntegrationProps> = ({
  sessionId,
  onCommandProcessed,
  onAgentResponse,
  onStateChange,
  initialMode = 'orchestration',
  enableMCP = true,
  className = '',
}) => {
  const {
    currentMode,
    activeAgents,
    commandHistory,
    isProcessing,
    setMode,
    addCommand,
    setProcessing,
    updateAgent,
    clearHistory,
    initialize
  } = useSuperClaudeStore();

  const [isInitialized, setIsInitialized] = useState(false);
  const [orchestrator, setOrchestrator] = useState<AgentOrchestrator | null>(null);
  const [commandRegistry, setCommandRegistry] = useState<CommandRegistry | null>(null);
  const [mcpIntegration, setMcpIntegration] = useState<MCPIntegration | null>(null);

  // Initialize core components
  useEffect(() => {
    const initializeComponents = async () => {
      try {
        onStateChange?.('Initializing SuperClaude system...');
        
        // Initialize store
        await initialize(sessionId);
        
        // Initialize orchestrator
        const newOrchestrator = new AgentOrchestrator(sessionId);
        await newOrchestrator.initialize();
        setOrchestrator(newOrchestrator);

        // Initialize command registry
        const newRegistry = new CommandRegistry();
        await newRegistry.initialize();
        setCommandRegistry(newRegistry);

        // Initialize MCP if enabled (non-fatal if it fails)
        if (enableMCP) {
          try {
            const newMCP = new MCPIntegration();
            await newMCP.connect();
            setMcpIntegration(newMCP);
            console.log('MCP integration initialized successfully');
          } catch (mcpError) {
            console.warn('MCP integration failed (non-fatal):', mcpError);
            // Continue without MCP - it's optional
          }
        }

        // Set initial mode
        setMode(initialMode);
        
        setIsInitialized(true);
        onStateChange?.('SuperClaude system ready');
      } catch (error) {
        console.error('Failed to initialize SuperClaude components:', error);
        onStateChange?.('Failed to initialize SuperClaude');
      }
    };

    initializeComponents();

    // Cleanup on unmount
    return () => {
      onStateChange?.('Shutting down SuperClaude...');
      orchestrator?.dispose();
      mcpIntegration?.disconnect();
    };
  }, [sessionId, enableMCP, initialMode, initialize, setMode, onStateChange]);

  // Handle command execution
  const handleCommand = useCallback(async (commandText: string) => {
    if (!orchestrator || !commandRegistry || isProcessing) {
      return;
    }

    setProcessing(true);
    
    try {
      // Add command to history
      const command = {
        id: Date.now().toString(),
        text: commandText,
        timestamp: new Date(),
        mode: currentMode,
        status: 'processing' as const,
      };
      
      addCommand(command);

      // Parse and validate command
      const parsedCommand = commandRegistry.parse(commandText);
      
      if (!parsedCommand.isValid) {
        throw new Error(`Invalid command: ${parsedCommand.error}`);
      }

      // Execute through orchestrator
      const result = await orchestrator.execute(parsedCommand, {
        mode: currentMode,
        mcp: mcpIntegration,
      });

      // Update command status
      addCommand({
        ...command,
        status: 'completed',
        result,
      });

      // Notify parent component
      if (onCommandProcessed) {
        onCommandProcessed({
          command: parsedCommand,
          result,
          mode: currentMode,
        });
      }

      // Handle agent responses
      if (result.agentResponses && onAgentResponse) {
        Object.entries(result.agentResponses).forEach(([agentId, response]) => {
          onAgentResponse(agentId, response);
        });
      }

      return result;
    } catch (error) {
      console.error('Command execution failed:', error);
      
      // Update command with error
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      addCommand({
        id: Date.now().toString(),
        text: commandText,
        timestamp: new Date(),
        mode: currentMode,
        status: 'error',
        error: errorMessage,
      });
      
      throw error;
    } finally {
      setProcessing(false);
    }
  }, [
    orchestrator,
    commandRegistry,
    isProcessing,
    currentMode,
    mcpIntegration,
    addCommand,
    setProcessing,
    onCommandProcessed,
    onAgentResponse,
  ]);

  // Handle mode change
  const handleModeChange = useCallback(async (newMode: BehavioralMode) => {
    if (!orchestrator || isProcessing) {
      return;
    }

    try {
      setProcessing(true);
      
      // Notify orchestrator of mode change
      await orchestrator.changeMode(newMode);
      
      // Update store
      setMode(newMode);
      
      // Add mode change to history
      addCommand({
        id: Date.now().toString(),
        text: `[Mode Changed: ${newMode}]`,
        timestamp: new Date(),
        mode: newMode,
        status: 'completed',
        isSystemMessage: true,
      });
    } catch (error) {
      console.error('Failed to change mode:', error);
    } finally {
      setProcessing(false);
    }
  }, [orchestrator, isProcessing, setProcessing, setMode, addCommand]);

  // Handle agent activation/deactivation
  const handleAgentToggle = useCallback(async (agentId: string, active: boolean) => {
    if (!orchestrator || isProcessing) {
      return;
    }

    try {
      if (active) {
        await orchestrator.activateAgent(agentId);
      } else {
        await orchestrator.deactivateAgent(agentId);
      }
      
      updateAgent(agentId, { active });
    } catch (error) {
      console.error(`Failed to ${active ? 'activate' : 'deactivate'} agent:`, error);
    }
  }, [orchestrator, isProcessing, updateAgent]);

  // Handle clear history
  const handleClearHistory = useCallback(() => {
    clearHistory();
  }, [clearHistory]);

  // Get available commands for current mode
  const availableCommands = useMemo(() => {
    if (!commandRegistry) return [];
    
    return commandRegistry.getCommandsForMode(currentMode);
  }, [commandRegistry, currentMode]);

  // Get active agents for current mode
  const modeAgents = useMemo(() => {
    if (!orchestrator) return [];
    
    return orchestrator.getAgentsForMode(currentMode);
  }, [orchestrator, currentMode]);

  if (!isInitialized) {
    return (
      <div className={`superclaude-loading ${className}`}>
        <div className="spinner" />
        <p>Initializing SuperClaude...</p>
      </div>
    );
  }

  return (
    <div className={`superclaude-integration ${className}`}>
      <SuperClaudePanel
        sessionId={sessionId}
        currentMode={currentMode}
        isProcessing={isProcessing}
        commandHistory={commandHistory}
        activeAgents={activeAgents}
        availableCommands={availableCommands}
        modeAgents={modeAgents}
        mcpEnabled={!!mcpIntegration}
        onCommandSubmit={handleCommand}
        onModeChange={handleModeChange}
        onAgentToggle={handleAgentToggle}
        onClearHistory={handleClearHistory}
      />
    </div>
  );
};

// Export types for external use
export type { BehavioralMode } from './modes';
export type { Agent } from './agents/types';
export type { Command } from './commands/types';
export type { MCPServer } from './mcp/types';