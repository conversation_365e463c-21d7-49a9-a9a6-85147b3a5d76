import { BaseAgent } from './agents/types';
import { BaseMCPServer } from './mcp-servers/types';
import { superclaudeConfig } from './config/superclaude-config';
import { superclaudeStorage } from './services/superclaudeStorage';
import { superclaudeCache } from './services/superclaudeCache';
import { superclaudeSync } from './services/superclaudeSync';

// Import all agents
import { BackendArchitectAgent } from './agents/BackendArchitectAgent';
import { FrontendArchitectAgent } from './agents/FrontendArchitectAgent';
import { SecurityEngineerAgent } from './agents/SecurityEngineerAgent';
import { QualityEngineerAgent } from './agents/QualityEngineerAgent';
import { PythonExpertAgent } from './agents/PythonExpertAgent';
import { SystemArchitectAgent } from './agents/SystemArchitectAgent';
import { RefactoringExpertAgent } from './agents/RefactoringExpertAgent';
import { LearningGuideAgent } from './agents/LearningGuideAgent';
import { TechnicalWriterAgent } from './agents/TechnicalWriterAgent';
import { SocraticMentorAgent } from './agents/SocraticMentorAgent';
import { RootCauseAnalystAgent } from './agents/RootCauseAnalystAgent';
import { PerformanceEngineerAgent } from './agents/PerformanceEngineerAgent';
import { RequirementsAnalystAgent } from './agents/RequirementsAnalystAgent';
import { DevOpsArchitectAgent } from './agents/DevOpsArchitectAgent';

// Import all MCP servers
import { GitHubServer } from './mcp-servers/GitHubServer';
import { DatabaseServer } from './mcp-servers/DatabaseServer';
import { FileSystemServer } from './mcp-servers/FileSystemServer';
import { APIServer } from './mcp-servers/APIServer';
import { DockerServer } from './mcp-servers/DockerServer';
import { MLServer } from './mcp-servers/MLServer';

interface IntegrationState {
  initialized: boolean;
  agents: Map<string, BaseAgent>;
  mcpServers: Map<string, BaseMCPServer>;
  activeMode: string;
  metrics: {
    agentExecutions: number;
    mcpRequests: number;
    cacheHits: number;
    syncOperations: number;
  };
}

export class SuperClaudeIntegrationManager {
  private static instance: SuperClaudeIntegrationManager;
  private state: IntegrationState;
  private config: typeof superclaudeConfig;

  private constructor() {
    this.config = superclaudeConfig;
    this.state = {
      initialized: false,
      agents: new Map(),
      mcpServers: new Map(),
      activeMode: this.config.modes.default,
      metrics: {
        agentExecutions: 0,
        mcpRequests: 0,
        cacheHits: 0,
        syncOperations: 0
      }
    };
  }

  static getInstance(): SuperClaudeIntegrationManager {
    if (!SuperClaudeIntegrationManager.instance) {
      SuperClaudeIntegrationManager.instance = new SuperClaudeIntegrationManager();
    }
    return SuperClaudeIntegrationManager.instance;
  }

  async initialize(): Promise<void> {
    if (this.state.initialized) {
      console.log('SuperClaude Integration Manager already initialized');
      return;
    }

    console.log('Initializing SuperClaude Integration Manager...');

    // Initialize agents
    await this.initializeAgents();

    // Initialize MCP servers
    await this.initializeMCPServers();

    // Start sync service
    if (this.config.storage.type === 'cloud') {
      superclaudeSync.on('sync:complete', () => {
        this.state.metrics.syncOperations++;
      });
    }

    this.state.initialized = true;
    console.log('SuperClaude Integration Manager initialized successfully');
  }

  private async initializeAgents(): Promise<void> {
    const agentClasses = [
      BackendArchitectAgent,
      FrontendArchitectAgent,
      SecurityEngineerAgent,
      QualityEngineerAgent,
      PythonExpertAgent,
      SystemArchitectAgent,
      RefactoringExpertAgent,
      LearningGuideAgent,
      TechnicalWriterAgent,
      SocraticMentorAgent,
      RootCauseAnalystAgent,
      PerformanceEngineerAgent,
      RequirementsAnalystAgent,
      DevOpsArchitectAgent
    ];

    for (const AgentClass of agentClasses) {
      const agent = new AgentClass();
      const agentId = agent.getId();
      
      if (this.config.agents.settings[agentId]?.enabled) {
        this.state.agents.set(agentId, agent);
        console.log(`Initialized agent: ${agent.getName()}`);
      }
    }
  }

  private async initializeMCPServers(): Promise<void> {
    const serverClasses = [
      GitHubServer,
      DatabaseServer,
      FileSystemServer,
      APIServer,
      DockerServer,
      MLServer
    ];

    for (const ServerClass of serverClasses) {
      const server = new ServerClass();
      const serverId = server.getId();
      
      if (this.config.mcpServers.servers[serverId]?.enabled) {
        this.state.mcpServers.set(serverId, server);
        
        if (this.config.mcpServers.servers[serverId].autoConnect) {
          await server.connect();
        }
        
        console.log(`Initialized MCP server: ${server.getName()}`);
      }
    }
  }

  // Agent Operations
  async executeAgent(agentId: string, context: any): Promise<any> {
    const agent = this.state.agents.get(agentId);
    if (!agent) {
      throw new Error(`Agent ${agentId} not found or not enabled`);
    }

    // Check cache first
    const cacheKey = `agent:${agentId}:${JSON.stringify(context)}`;
    const cached = superclaudeCache.get(cacheKey);
    if (cached) {
      this.state.metrics.cacheHits++;
      return cached;
    }

    // Execute agent
    const startTime = Date.now();
    const result = await agent.execute(context);
    const executionTime = Date.now() - startTime;

    // Update metrics
    this.state.metrics.agentExecutions++;
    
    // Cache result
    superclaudeCache.set(cacheKey, result, { ttl: 3600000 }); // 1 hour cache

    // Store execution history
    await superclaudeStorage.set('agents', `execution:${Date.now()}`, {
      agentId,
      context,
      result,
      executionTime,
      timestamp: Date.now()
    });

    // Track change for sync
    superclaudeSync.trackChange('agent', 'create', {
      agentId,
      execution: { context, result, executionTime }
    });

    return result;
  }

  async orchestrateAgents(workflow: any): Promise<any> {
    const { agents, sequence, data } = workflow;
    const results: any[] = [];

    if (sequence === 'parallel') {
      // Execute agents in parallel
      const promises = agents.map((agentId: string) => 
        this.executeAgent(agentId, data)
      );
      const parallelResults = await Promise.all(promises);
      results.push(...parallelResults);
    } else {
      // Execute agents sequentially
      let currentData = data;
      
      for (const agentId of agents) {
        const result = await this.executeAgent(agentId, currentData);
        results.push(result);
        
        // Pass result to next agent
        currentData = { ...currentData, previousResult: result };
      }
    }

    return {
      workflow,
      results,
      timestamp: Date.now()
    };
  }

  // MCP Server Operations
  async executeMCPCommand(serverId: string, command: string, args: any): Promise<any> {
    const server = this.state.mcpServers.get(serverId);
    if (!server) {
      throw new Error(`MCP server ${serverId} not found or not enabled`);
    }

    // Ensure server is connected
    if (server.getStatus() !== 'connected') {
      const connected = await server.connect();
      if (!connected) {
        throw new Error(`Failed to connect to MCP server ${serverId}`);
      }
    }

    // Check cache
    const cacheKey = `mcp:${serverId}:${command}:${JSON.stringify(args)}`;
    const cached = superclaudeCache.get(cacheKey);
    if (cached) {
      this.state.metrics.cacheHits++;
      return cached;
    }

    // Execute command
    const startTime = Date.now();
    const result = await server.executeCommand(command, args);
    const executionTime = Date.now() - startTime;

    // Update metrics
    this.state.metrics.mcpRequests++;

    // Cache result
    superclaudeCache.set(cacheKey, result, { ttl: 300000 }); // 5 min cache

    // Store execution history
    await superclaudeStorage.set('mcp', `execution:${Date.now()}`, {
      serverId,
      command,
      args,
      result,
      executionTime,
      timestamp: Date.now()
    });

    return result;
  }

  async getMCPTools(serverId?: string): Promise<any[]> {
    if (serverId) {
      const server = this.state.mcpServers.get(serverId);
      return server ? server.getTools() : [];
    }

    // Get tools from all servers
    const allTools: any[] = [];
    for (const server of this.state.mcpServers.values()) {
      const tools = server.getTools();
      allTools.push(...tools.map(tool => ({
        ...tool,
        server: server.getId()
      })));
    }
    return allTools;
  }

  async getMCPResources(serverId?: string): Promise<any[]> {
    if (serverId) {
      const server = this.state.mcpServers.get(serverId);
      return server ? server.getResources() : [];
    }

    // Get resources from all servers
    const allResources: any[] = [];
    for (const server of this.state.mcpServers.values()) {
      const resources = server.getResources();
      allResources.push(...resources.map(resource => ({
        ...resource,
        server: server.getId()
      })));
    }
    return allResources;
  }

  // Mode Management
  setMode(modeId: string): void {
    const mode = this.config.modes.available.find(m => m.id === modeId);
    if (!mode) {
      throw new Error(`Mode ${modeId} not found`);
    }

    this.state.activeMode = modeId;
    
    // Store mode change
    superclaudeStorage.set('system', 'active-mode', modeId);
    
    // Track change for sync
    superclaudeSync.trackChange('config', 'update', {
      type: 'mode',
      value: modeId
    });
  }

  getActiveMode(): any {
    return this.config.modes.available.find(m => m.id === this.state.activeMode);
  }

  // State Management
  getState(): IntegrationState {
    return { ...this.state };
  }

  getMetrics(): typeof this.state.metrics {
    return { ...this.state.metrics };
  }

  getAgents(): Map<string, BaseAgent> {
    return new Map(this.state.agents);
  }

  getMCPServers(): Map<string, BaseMCPServer> {
    return new Map(this.state.mcpServers);
  }

  // Configuration
  updateConfig(updates: Partial<typeof superclaudeConfig>): void {
    Object.assign(this.config, updates);
    
    // Save config
    superclaudeStorage.set('config', 'superclaude', this.config);
    
    // Track change for sync
    superclaudeSync.trackChange('config', 'update', {
      type: 'full',
      value: this.config
    });
  }

  getConfig(): typeof superclaudeConfig {
    return { ...this.config };
  }

  // Cleanup
  async destroy(): Promise<void> {
    // Disconnect all MCP servers
    for (const server of this.state.mcpServers.values()) {
      await server.disconnect();
    }

    // Clear caches
    superclaudeCache.clear();

    // Save final state
    await superclaudeStorage.set('system', 'final-state', this.state);

    // Cleanup services
    superclaudeSync.destroy();
    superclaudeCache.destroy();

    console.log('SuperClaude Integration Manager destroyed');
  }
}

// Export singleton instance
export const integrationManager = SuperClaudeIntegrationManager.getInstance();