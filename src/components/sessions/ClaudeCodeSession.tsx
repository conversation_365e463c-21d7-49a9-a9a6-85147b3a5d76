import React, { useState, useEffect, useRef, use<PERSON>emo, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  FolderOpen,
  ChevronUp,
  ChevronDown,
  X,
  Hash,
  MessageCircle,
  Bot,
  Play,
  Sparkles,
  BarChart3,
  <PERSON>rendingUp,
  Minimize2,
  Maximize2,
  <PERSON>pu
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Popover } from "@/components/ui/popover";
import { api, type Session } from "@/lib/api";
import { cn } from "@/lib/utils";
import { open } from "@tauri-apps/plugin-dialog";
import { listen, type UnlistenFn } from "@tauri-apps/api/event";
import { SessionInput as FloatingPromptInput, type SessionInputRef as FloatingPromptInputRef } from "../inputs/Promptinput";
import { ErrorBoundary } from "../common/ErrorBoundary";
import { TimelineNavigator } from "../navigation/TimelineNavigator";
import { CheckpointSettings } from "../settings/CheckpointSettings";
import { SlashCommandsManager } from "../editors/SlashCommandsManager";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { SplitPane } from "@/components/ui/split-pane";
import { WebviewPreview } from "../display/WebviewPreview";
import { PlanModeIndicator } from "../display/PlanModeIndicator";
import { ExtendedThinkingIndicator } from "../display/ExtendedThinkingIndicator";
import type { ClaudeStreamMessage } from "@/types/session";
import { useTrackEvent, useComponentMetrics, useWorkflowTracking } from "@/hooks";

// Import extracted components and hooks
import { MessageList } from "./claude-code-session/MessageList";
import { PromptQueue } from "./claude-code-session/PromptQueue";
import { SessionHeader } from "./claude-code-session/SessionHeader";
import { useCheckpoints } from "./claude-code-session/useCheckpoints";
import { usePlanMode } from "./claude-code-session/PlanModeManager";
import { useExtendedThinking } from "./claude-code-session/ExtendedThinkingManager";
import { useSessionMetrics } from "./claude-code-session/SessionMetricsTracker";
import { usePreview } from "./claude-code-session/PreviewManager";
import { SessionMetricsVisualizer } from "./claude-code-session/SessionMetricsVisualizer";
import { useAgentOrchestration } from "./claude-code-session/useAgentOrchestration";
import { useMCPIntegration } from "./claude-code-session/useMCPIntegration";
// Agent panels removed - they run in background
import { ErrorRecoverySystem, type ErrorDetails } from "./claude-code-session/ErrorRecoverySystem";

interface ClaudeCodeSessionProps {
  /**
   * Optional session to resume (when clicking from SessionList)
   */
  session?: Session;
  /**
   * Initial project path (for new sessions)
   */
  initialProjectPath?: string;
  /**
   * Callback to go back
   */
  onBack: () => void;
  /**
   * Callback to open hooks configuration
   */
  onProjectSettings?: (projectPath: string) => void;
  /**
   * Callback to open usage dashboard
   */
  onUsageDashboard?: () => void;
  /**
   * Optional className for styling
   */
  className?: string;
  /**
   * Callback when streaming state changes
   */
  onStreamingChange?: (isStreaming: boolean, sessionId: string | null) => void;
}

/**
 * ClaudeCodeSession component for interactive Claude Code sessions
 * 
 * @example
 * <ClaudeCodeSession onBack={() => setView('projects')} />
 */
export const ClaudeCodeSession: React.FC<ClaudeCodeSessionProps> = ({
  session,
  initialProjectPath = "",
  onBack,
  onProjectSettings,
  onUsageDashboard,
  className,
  onStreamingChange,
}) => {
  const [projectPath, setProjectPath] = useState(initialProjectPath || session?.project_path || "");
  const [messages, setMessages] = useState<ClaudeStreamMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [rawJsonlOutput, setRawJsonlOutput] = useState<string[]>([]);
  const [copyPopoverOpen, setCopyPopoverOpen] = useState(false);
  const [isFirstPrompt, setIsFirstPrompt] = useState(!session);
  const [resumeFlag, setResumeFlag] = useState(false); // Track if --resume should be used
  const [totalTokens, setTotalTokens] = useState(0);
  const [extractedSessionInfo, setExtractedSessionInfo] = useState<{ sessionId: string; projectId: string } | null>(null);
  const [claudeSessionId, setClaudeSessionId] = useState<string | null>(null);
  const [showTimeline, setShowTimeline] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showForkDialog, setShowForkDialog] = useState(false);
  const [showSlashCommandsSettings, setShowSlashCommandsSettings] = useState(false);
  const [forkCheckpointId, setForkCheckpointId] = useState<string | null>(null);
  const [forkSessionName, setForkSessionName] = useState("");
  const [isInBackground, setIsInBackground] = useState(false);
  const [currentError, setCurrentError] = useState<ErrorDetails | null>(null);
  
  // Agent integration state
  const [activeAgent, setActiveAgent] = useState<{
    type: string;
    name: string;
    taskId?: string;
    success?: boolean;
  } | null>(null);
  const [detectedAgentOpportunity, setDetectedAgentOpportunity] = useState<{
    agentType: string;
    confidence: number;
    reason: string;
  } | null>(null);
  // Workflow panel removed - runs in background
  
  // Queued prompts state
  const [queuedPrompts, setQueuedPrompts] = useState<Array<{ id: string; prompt: string; model: "claude-sonnet-4-20250514" | "claude-opus-4-1-20250805" }>>([]);
  
  // Use the preview hook
  const {
    showPreview,
    previewUrl,
    showPreviewPrompt,
    splitPosition,
    isPreviewMaximized,
    setSplitPosition,
    handleLinkDetected,
    handleClosePreview,
    handlePreviewUrlChange,
    handleTogglePreviewMaximize
  } = usePreview();

  // Agent & MCP panel visibility state
  // Agent MCP and Orchestration panels removed - they run in background
  
  
  // Use the plan mode hook
  const {
    isPlanMode,
    currentPlan,
    setIsPlanMode,
    setCurrentPlan,
    handleMessage: handlePlanMessage
  } = usePlanMode();
  
  // Use the extended thinking hook
  const {
    isExtendedThinking,
    thinkingProgress,
    thinkingStage,
    thinkingEstimatedTime,
    handleMessage: handleThinkingMessage
  } = useExtendedThinking();
  
  // Waiting for response state
  const [isWaitingForResponse, setIsWaitingForResponse] = useState(false);
  
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const unlistenRefs = useRef<UnlistenFn[]>([]);
  const hasActiveSessionRef = useRef(false);
  const floatingPromptRef = useRef<FloatingPromptInputRef>(null);
  const queuedPromptsRef = useRef<Array<{ id: string; prompt: string; model: "claude-sonnet-4-20250514" | "claude-opus-4-1-20250805" }>>([]);
  const isMountedRef = useRef(true);
  const isListeningRef = useRef(false);
  const messagesRef = useRef<ClaudeStreamMessage[]>([]);
  
  // Helper function to detect if a message contains a question
  const detectQuestion = (text: string): boolean => {
    // Common question patterns
    const questionPatterns = [
      /\?$/m,  // Ends with question mark
      /\?\s*$/m,  // Ends with question mark and whitespace
      /^(Would you|Do you|Can you|Should I|May I|Could you|Will you|Is it|Are you|Have you|What|When|Where|How|Why|Which)/mi,
      /(would you like|do you want|should I|can I|may I|shall I|need me to|want me to)/i,
      /(please (confirm|specify|provide|clarify|choose|select|let me know|tell me))/i,
      /(yes or no|y\/n|\(y\/n\))/i,
      /(proceed\?|continue\?|go ahead\?)/i
    ];
    
    return questionPatterns.some(pattern => pattern.test(text));
  };
  const sessionStartTime = useRef<number>(Date.now());
  
  // Get effective session info (from prop or extracted) - use useMemo to ensure it updates
  // MOVED UP before hooks that depend on it
  const effectiveSession = useMemo(() => {
    if (session) return session;
    if (extractedSessionInfo) {
      return {
        id: extractedSessionInfo.sessionId,
        project_id: extractedSessionInfo.projectId,
        project_path: projectPath,
        created_at: Date.now(),
      } as Session;
    }
    // Provide default IDs for SuperClaude to work before session is created
    if (projectPath) {
      return {
        id: 'temp-session-' + Date.now(),
        project_id: projectPath.replace(/[^a-zA-Z0-9]/g, '-'),
        project_path: projectPath,
        created_at: Date.now(),
      } as Session;
    }
    return null;
  }, [session, extractedSessionInfo, projectPath]);
  
  // Use the session metrics hook
  const {
    metrics: sessionMetrics,
    trackPrompt,
    handleMessage: handleMetricsMessage,
    incrementCheckpoints,
    getMetrics
  } = useSessionMetrics({ wasResumed: !!session });

  // Agent orchestration hook
  const {
    agents,
    activeAgents,
    executions,
    orchestrations,
    coordinationTasks,
    isLoading: agentsLoading,
    error: agentsError,
    activateAgent,
    deactivateAgent,
    executeAgent,
    getAgentMetrics,
    chainAgents,
    reloadAgents,
    initOrchestrator,
    createOrchestrationSession,
    addAgentToOrchestration,
    startOrchestration,
    stopOrchestration,
    getOrchestrationStatus,
    listOrchestrationSessions,
    coordinateTask,
    getOrchestrationMetrics,
    optimizeOrchestration,
  } = useAgentOrchestration(effectiveSession?.id || 'temp');

  // MCP integration hook
  const {
    servers: mcpServers,
    activeServers,
    isLoading: mcpLoading,
    error: mcpError,
    connectionStatus,
    healthMetrics,
    autoReconnectEnabled,
    connectServer,
    disconnectServer,
    executeTool,
    fetchResource,
    getAllTools,
    getAllResources,
    reloadServers,
    checkServerHealth,
    getServerHealth,
    setAutoReconnect,
  } = useMCPIntegration(effectiveSession?.id || 'temp');

  // Analytics tracking
  const trackEvent = useTrackEvent();
  useComponentMetrics('ClaudeCodeSession');
  // const aiTracking = useAIInteractionTracking('sonnet'); // Default model
  const workflowTracking = useWorkflowTracking('claude_session');
  
  // Keep refs in sync with state
  useEffect(() => {
    queuedPromptsRef.current = queuedPrompts;
  }, [queuedPrompts]);

  useEffect(() => {
    messagesRef.current = messages;
  }, [messages]);

  // Filter out messages that shouldn't be displayed
  const displayableMessages = useMemo(() => {
    return messages.filter((message, index) => {
      // Skip meta messages that don't have meaningful content
      if (message.isMeta && !message.leafUuid && !message.summary) {
        return false;
      }

      // Skip user messages that only contain tool results that are already displayed
      if (message.type === "user" && message.message) {
        if (message.isMeta) return false;

        const msg = message.message;
        if (!msg.content || (Array.isArray(msg.content) && msg.content.length === 0)) {
          return false;
        }

        if (Array.isArray(msg.content)) {
          let hasVisibleContent = false;
          for (const content of msg.content) {
            if (content.type === "text") {
              hasVisibleContent = true;
              break;
            }
            if (content.type === "tool_result") {
              let willBeSkipped = false;
              if (content.tool_use_id) {
                // Look for the matching tool_use in previous assistant messages
                for (let i = index - 1; i >= 0; i--) {
                  const prevMsg = messages[i];
                  if (prevMsg.type === 'assistant' && prevMsg.message?.content && Array.isArray(prevMsg.message.content)) {
                    const toolUse = prevMsg.message.content.find((c: any) => 
                      c.type === 'tool_use' && c.id === content.tool_use_id
                    );
                    if (toolUse) {
                      const toolName = toolUse.name?.toLowerCase();
                      const toolsWithWidgets = [
                        'task', 'edit', 'multiedit', 'todowrite', 'ls', 'read', 
                        'glob', 'bash', 'write', 'grep'
                      ];
                      if (toolsWithWidgets.includes(toolName) || toolUse.name?.startsWith('mcp__')) {
                        willBeSkipped = true;
                      }
                      break;
                    }
                  }
                }
              }
              if (!willBeSkipped) {
                hasVisibleContent = true;
                break;
              }
            }
          }
          if (!hasVisibleContent) {
            return false;
          }
        }
      }
      return true;
    });
  }, [messages]);


  // Debug logging
  useEffect(() => {
    console.log({
      projectPath,
      session,
      extractedSessionInfo,
      effectiveSession,
      messagesCount: messages.length,
      isLoading
    });
  }, [projectPath, session, extractedSessionInfo, effectiveSession, messages.length, isLoading]);

  // Load session history if resuming
  useEffect(() => {
    if (session) {
      // Set the claudeSessionId immediately when we have a session
      setClaudeSessionId(session.id);
      
      // Load session history first, then check for active session
      const initializeSession = async () => {
        await loadSessionHistory();
        // After loading history, check if the session is still active
        if (isMountedRef.current) {
          await checkForActiveSession();
        }
      };
      
      initializeSession();
    }
  }, [session]); // Remove hasLoadedSession dependency to ensure it runs on mount

  // Initialize agent orchestrator
  useEffect(() => {
    const initializeOrchestrator = async () => {
      try {
        await initOrchestrator();
        // Load existing orchestration sessions
        await listOrchestrationSessions();
      } catch (error) {
        console.warn('Failed to initialize agent orchestrator:', error);
      }
    };
    
    initializeOrchestrator();
  }, [initOrchestrator, listOrchestrationSessions]);

  // Report streaming state changes
  useEffect(() => {
    onStreamingChange?.(isLoading, claudeSessionId);
  }, [isLoading, claudeSessionId, onStreamingChange]);


  // Calculate total tokens from messages
  useEffect(() => {
    const tokens = messages.reduce((total, msg) => {
      if (msg.message?.usage) {
        return total + msg.message.usage.input_tokens + msg.message.usage.output_tokens;
      }
      if (msg.usage) {
        return total + msg.usage.input_tokens + msg.usage.output_tokens;
      }
      return total;
    }, 0);
    setTotalTokens(tokens);
  }, [messages]);

  const loadSessionHistory = async () => {
    if (!session) {
      return;
    }
    
    if (!session.id || !session.project_id) {
      setError("Invalid session data: missing ID or project ID");
      return;
    }
    
    try {
      setIsLoading(true);
      setError(null);
      setCurrentError(null);
      
      const history = await api.loadSessionHistory(session.id, session.project_id);
      
      // Convert history to messages format
      const loadedMessages: ClaudeStreamMessage[] = history.map(entry => ({
        ...entry,
        type: entry.type || "assistant"
      }));
      
      setMessages(loadedMessages);
      setRawJsonlOutput(history.map(h => JSON.stringify(h)));
      
      // After loading history, we're continuing a conversation
      setIsFirstPrompt(false);
    } catch (err) {
      const errorStr = err instanceof Error ? err.message : String(err);
      
      // Check if it's just a missing session file (which is okay for new sessions)
      if (errorStr.includes("Session file not found") || errorStr.includes("not found")) {
        // Clear any error state and continue normally
        setError(null);
        setCurrentError(null);
        setMessages([]);
        setRawJsonlOutput([]);
        setIsFirstPrompt(true);
      } else {
        // It's a real error
        const errorMessage = `Failed to load session history: ${errorStr}`;
        const categorizedError = categorizeError(errorMessage);
        setCurrentError(categorizedError);
        setError(errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const checkForActiveSession = async () => {
    // If we have a session prop, check if it's still active
    if (session) {
      try {
        const activeSessions = await api.listRunningClaudeSessions();
        const activeSession = activeSessions.find((s: any) => {
          if ('process_type' in s && s.process_type && 'ClaudeSession' in s.process_type) {
            return (s.process_type as any).ClaudeSession.session_id === session.id;
          }
          return false;
        });
        
        if (activeSession) {
          // Session is still active, reconnect to its stream
          // IMPORTANT: Set claudeSessionId before reconnecting
          setClaudeSessionId(session.id);
          
          // Don't add buffered messages here - they've already been loaded by loadSessionHistory
          // Just set up listeners for new messages
          
          // Set up listeners for the active session
          reconnectToSession(session.id);
        }
      } catch (err) {
      }
    }
  };

  const reconnectToSession = async (sessionId: string) => {
    
    // Prevent duplicate listeners
    if (isListeningRef.current) {
      return;
    }
    
    // Clean up previous listeners
    unlistenRefs.current.forEach(unlisten => unlisten());
    unlistenRefs.current = [];
    
    // IMPORTANT: Set the session ID before setting up listeners
    setClaudeSessionId(sessionId);
    
    // Mark as listening
    isListeningRef.current = true;
    
    // Set up session-specific listeners
    const outputUnlisten = await listen<string>(`claude-output:${sessionId}`, async (event) => {
      try {
        
        if (!isMountedRef.current) return;
        
        // Store raw JSONL
        setRawJsonlOutput(prev => [...prev, event.payload]);
        
        // Parse and display
        const message = JSON.parse(event.payload) as ClaudeStreamMessage;
        setMessages(prev => [...prev, message]);
      } catch (err) {
      }
    });

    const errorUnlisten = await listen<string>(`claude-error:${sessionId}`, (event) => {
      if (isMountedRef.current) {
        setError(event.payload);
      }
    });

    const completeUnlisten = await listen<boolean>(`claude-complete:${sessionId}`, async (event) => {
      if (isMountedRef.current) {
        setIsLoading(false);
        hasActiveSessionRef.current = false;
      }
    });

    unlistenRefs.current = [outputUnlisten, errorUnlisten, completeUnlisten];
    
    // Mark as loading to show the session is active
    if (isMountedRef.current) {
      setIsLoading(true);
      hasActiveSessionRef.current = true;
    }
  };

  const handleSelectPath = async () => {
    try {
      const selected = await open({
        directory: true,
        multiple: false,
        title: "Select Project Directory"
      });
      
      if (selected) {
        setProjectPath(selected as string);
        setError(null);
        setCurrentError(null);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      const categorizedError = categorizeError(errorMessage);
      setCurrentError(categorizedError);
      setError(errorMessage);
    }
  };

  const handleSendPrompt = async (prompt: string, model: "claude-sonnet-4-20250514" | "claude-opus-4-1-20250805") => {
    
    if (!projectPath) {
      setError("Please select a project directory first");
      return;
    }

    // If we're waiting for a response, clear that state as user is providing input
    if (isWaitingForResponse) {
      setIsWaitingForResponse(false);
    }

    // If already loading, queue the prompt
    if (isLoading) {
      const newPrompt = {
        id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        prompt,
        model
      };
      setQueuedPrompts(prev => [...prev, newPrompt]);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      hasActiveSessionRef.current = true;
      
      // For resuming sessions, ensure we have the session ID
      if (effectiveSession && !claudeSessionId) {
        setClaudeSessionId(effectiveSession.id);
      }
      
      // Only clean up and set up new listeners if not already listening
      if (!isListeningRef.current) {
        // Clean up previous listeners
        unlistenRefs.current.forEach(unlisten => unlisten());
        unlistenRefs.current = [];
        
        // Mark as setting up listeners
        isListeningRef.current = true;
        
        // --------------------------------------------------------------------
        // 1️⃣  Event Listener Setup Strategy
        // --------------------------------------------------------------------
        // Claude Code may emit a *new* session_id even when we pass --resume. If
        // we listen only on the old session-scoped channel we will miss the
        // stream until the user navigates away & back. To avoid this we:
        //   • Always start with GENERIC listeners (no suffix) so we catch the
        //     very first "system:init" message regardless of the session id.
        //   • Once that init message provides the *actual* session_id, we
        //     dynamically switch to session-scoped listeners and stop the
        //     generic ones to prevent duplicate handling.
        // --------------------------------------------------------------------


        let currentSessionId: string | null = claudeSessionId || effectiveSession?.id || null;

        // Helper to attach session-specific listeners **once we are sure**
        const attachSessionSpecificListeners = async (sid: string) => {

          const specificOutputUnlisten = await listen<string>(`claude-output:${sid}`, (evt) => {
            handleStreamMessage(evt.payload);
          });

          const specificErrorUnlisten = await listen<string>(`claude-error:${sid}`, (evt) => {
            setError(evt.payload);
          });

          const specificCompleteUnlisten = await listen<boolean>(`claude-complete:${sid}`, (evt) => {
            processComplete(evt.payload);
          });

          // Replace existing unlisten refs with these new ones (after cleaning up)
          unlistenRefs.current.forEach((u) => u());
          unlistenRefs.current = [specificOutputUnlisten, specificErrorUnlisten, specificCompleteUnlisten];
        };

        // Generic listeners (catch-all)
        const genericOutputUnlisten = await listen<string>('claude-output', async (event) => {
          handleStreamMessage(event.payload);

          // Attempt to extract session_id on the fly (for the very first init)
          try {
            const msg = JSON.parse(event.payload) as ClaudeStreamMessage;
            if (msg.type === 'system' && msg.subtype === 'init' && msg.session_id) {
              if (!currentSessionId || currentSessionId !== msg.session_id) {
                currentSessionId = msg.session_id;
                setClaudeSessionId(msg.session_id);

                // Update extracted session info with the new session ID
                const projectId = projectPath.replace(/[^a-zA-Z0-9]/g, '-');
                setExtractedSessionInfo({ sessionId: msg.session_id, projectId });

                // Switch to session-specific listeners
                await attachSessionSpecificListeners(msg.session_id);
              }
            }
          } catch {
            /* ignore parse errors */
          }
        });

        // Helper to process any JSONL stream message string
        function handleStreamMessage(payload: string) {
          try {
            // Don't process if component unmounted
            if (!isMountedRef.current) return;
            
            // Store raw JSONL
            setRawJsonlOutput((prev) => [...prev, payload]);

            const message = JSON.parse(payload) as ClaudeStreamMessage;
            
            // Handle metrics tracking
            handleMetricsMessage(message);
            
            // Track workflow step for tools
            if (message.type === 'assistant' && message.message?.content) {
              const toolUses = message.message.content.filter((c: any) => c.type === 'tool_use');
              if (toolUses.length > 0) {
                workflowTracking.trackStep();
              }
            }
            
            // Track tool errors for analytics
            if (message.type === 'user' && message.message?.content) {
              const toolResults = message.message.content.filter((c: any) => c.type === 'tool_result');
              toolResults.forEach((result: any) => {
                const isError = result.is_error || false;
                if (isError) {
                  trackEvent.enhancedError({
                    error_type: 'tool_execution',
                    error_code: 'tool_failed',
                    error_message: result.content,
                    context: `Tool execution failed`,
                    user_action_before_error: 'executing_tool',
                    recovery_attempted: false,
                    recovery_successful: false,
                    error_frequency: 1,
                    stack_trace_hash: undefined
                  });
                }
              });
            }
            
            // Handle plan mode updates
            handlePlanMessage(message);
            
            // Handle extended thinking updates
            handleThinkingMessage(message);
            
            // Check for agent-related tool calls
            if (message.type === 'assistant' && message.message?.content) {
              const toolUses = message.message.content.filter((c: any) => c.type === 'tool_use');
              const agentCalls = toolUses.filter((t: any) => 
                t.name === 'Task' || 
                t.name?.toLowerCase().includes('agent') ||
                t.input?.subagent_type
              );
              
              if (agentCalls.length > 0) {
                // Detect agent opportunity
                const firstAgentCall = agentCalls[0];
                setDetectedAgentOpportunity({
                  agentType: firstAgentCall.input?.subagent_type || firstAgentCall.name,
                  confidence: 0.9,
                  reason: `Claude invoked ${firstAgentCall.name} tool`
                });
                
                // Auto-execute agent if confidence is high
                if (firstAgentCall.input?.subagent_type) {
                  setActiveAgent({
                    type: firstAgentCall.input.subagent_type,
                    name: firstAgentCall.input.description || firstAgentCall.input.subagent_type,
                    taskId: firstAgentCall.id
                  });
                }
              }
            }
            
            setMessages((prev) => [...prev, message]);
          } catch (err) {
          }
        }

        // Helper to handle completion events (both generic and scoped)
        const processComplete = async (success: boolean) => {
          // Get the current messages from the DOM or recent state
          // We need to check the last message that was just added
          // Since handleStreamMessage was just called, we can use a small delay
          await new Promise(resolve => setTimeout(resolve, 100));
          
          // Now check the messages through a ref or state read
          const checkForQuestion = () => {
            // Get messages from the component's current state
            const currentMessages = messagesRef.current || messages;
            const lastAssistantMessage = currentMessages.slice().reverse().find((m: any) => m.type === 'assistant');
            
            if (lastAssistantMessage && lastAssistantMessage.message?.content) {
              const textContent = lastAssistantMessage.message.content
                .filter((c: any) => c.type === 'text')
                .map((c: any) => c.text)
                .join(' ');
              
              return detectQuestion(textContent);
            }
            return false;
          };
          
          const hasQuestion = checkForQuestion();
          
          // If Claude asked a question, don't complete the session - wait for user response
          if (hasQuestion) {
            setIsLoading(false);
            setIsWaitingForResponse(true);
            
            // Store the current session ID (prefer claudeSessionId over extractedSessionInfo)
            const currentActiveSessionId = claudeSessionId || extractedSessionInfo?.sessionId;
            if (currentActiveSessionId) {
              sessionStorage.setItem('claudeQuestionSessionId', currentActiveSessionId);
            }
            
            return; // Don't process completion, keep session active
          }
          
          // Otherwise, proceed with normal completion
          setIsLoading(false);
          setIsWaitingForResponse(false);
          hasActiveSessionRef.current = false;
          isListeningRef.current = false; // Reset listening state
          
          // Track enhanced session stopped metrics when session completes
          if (effectiveSession && claudeSessionId) {
            const sessionStartTimeValue = messages.length > 0 ? messages[0].timestamp || Date.now() : Date.now();
            const duration = Date.now() - sessionStartTimeValue;
            const metrics = getMetrics();
            const timeToFirstMessage = metrics.firstMessageTime 
              ? metrics.firstMessageTime - sessionStartTime.current 
              : undefined;
            const idleTime = Date.now() - metrics.lastActivityTime;
            const avgResponseTime = metrics.toolExecutionTimes.length > 0
              ? metrics.toolExecutionTimes.reduce((a, b) => a + b, 0) / metrics.toolExecutionTimes.length
              : undefined;
            
            trackEvent.enhancedSessionStopped({
              // Basic metrics
              duration_ms: duration,
              messages_count: messages.length,
              reason: success ? 'completed' : 'error',
              
              // Timing metrics
              time_to_first_message_ms: timeToFirstMessage,
              average_response_time_ms: avgResponseTime,
              idle_time_ms: idleTime,
              
              // Interaction metrics
              prompts_sent: metrics.promptsSent,
              tools_executed: metrics.toolsExecuted,
              tools_failed: metrics.toolsFailed,
              files_created: metrics.filesCreated,
              files_modified: metrics.filesModified,
              files_deleted: metrics.filesDeleted,
              
              // Content metrics
              total_tokens_used: totalTokens,
              code_blocks_generated: metrics.codeBlocksGenerated,
              errors_encountered: metrics.errorsEncountered,
              
              // Session context
              model: metrics.modelChanges.length > 0 
                ? metrics.modelChanges[metrics.modelChanges.length - 1].to 
                : 'claude-sonnet-4-20250514',
              has_checkpoints: metrics.checkpointCount > 0,
              checkpoint_count: metrics.checkpointCount,
              was_resumed: metrics.wasResumed,
              
              // Agent context (if applicable)
              agent_type: activeAgent?.type || undefined,
              agent_name: activeAgent?.name || undefined,
              agent_success: activeAgent ? activeAgent.success : success,
              
              // Stop context
              stop_source: 'completed',
              final_state: success ? 'success' : 'failed',
              has_pending_prompts: queuedPrompts.length > 0,
              pending_prompts_count: queuedPrompts.length,
            });
          }

          if (effectiveSession && success) {
            try {
              const settings = await api.getCheckpointSettings(
                effectiveSession.id,
                effectiveSession.project_id,
                projectPath
              );

              if (settings.auto_checkpoint_enabled) {
                await api.checkAutoCheckpoint(
                  effectiveSession.id,
                  effectiveSession.project_id,
                  projectPath,
                  prompt
                );
                // Timeline will auto-refresh from the hook
              }
            } catch (err) {
            }
          }

          // Process queued prompts after completion
          if (queuedPromptsRef.current.length > 0) {
            const [nextPrompt, ...remainingPrompts] = queuedPromptsRef.current;
            setQueuedPrompts(remainingPrompts);
            
            // Small delay to ensure UI updates
            setTimeout(() => {
              handleSendPrompt(nextPrompt.prompt, nextPrompt.model);
            }, 100);
          }
        };

        const genericErrorUnlisten = await listen<string>('claude-error', (evt) => {
          setError(evt.payload);
        });

        const genericCompleteUnlisten = await listen<boolean>('claude-complete', (evt) => {
          processComplete(evt.payload);
        });

        // Store the generic unlisteners for now; they may be replaced later.
        unlistenRefs.current = [genericOutputUnlisten, genericErrorUnlisten, genericCompleteUnlisten];

        // --------------------------------------------------------------------
        // 2️⃣  Auto-checkpoint logic moved after listener setup (unchanged)
        // --------------------------------------------------------------------

        // Add the user message immediately to the UI (after setting up listeners)
        const userMessage: ClaudeStreamMessage = {
          type: "user",
          message: {
            content: [
              {
                type: "text",
                text: prompt
              }
            ]
          }
        };
        setMessages(prev => [...prev, userMessage]);
        
        // Update session metrics
        trackPrompt(model);
        
        // Track enhanced prompt submission
        const codeBlockMatches = prompt.match(/```[\s\S]*?```/g) || [];
        const hasCode = codeBlockMatches.length > 0;
        const conversationDepth = messages.filter(m => m.user_message).length;
        const sessionAge = sessionStartTime.current ? Date.now() - sessionStartTime.current : 0;
        const wordCount = prompt.split(/\s+/).filter(word => word.length > 0).length;
        
        trackEvent.enhancedPromptSubmitted({
          prompt_length: prompt.length,
          model: model,
          has_attachments: false, // TODO: Add attachment support when implemented
          source: 'keyboard', // TODO: Track actual source (keyboard vs button)
          word_count: wordCount,
          conversation_depth: conversationDepth,
          prompt_complexity: wordCount < 20 ? 'simple' : wordCount < 100 ? 'moderate' : 'complex',
          contains_code: hasCode,
          language_detected: hasCode ? codeBlockMatches?.[0]?.match(/```(\w+)/)?.[1] : undefined,
          session_age_ms: sessionAge
        });

        // Check if we're responding to a question from Claude
        const questionSessionId = sessionStorage.getItem('claudeQuestionSessionId');
        
        if (questionSessionId && isWaitingForResponse) {
          
          // Update the claudeSessionId to match the question session
          setClaudeSessionId(questionSessionId);
          
          trackEvent.sessionResumed(questionSessionId);
          trackEvent.modelSelected(model);
          
          // Clear the question session ID after using it
          sessionStorage.removeItem('claudeQuestionSessionId');
          
          // Resume the session with the user's response
          await api.resumeClaudeCode(projectPath, questionSessionId, prompt, model);
        } else if (effectiveSession && (!isFirstPrompt || resumeFlag)) {
          // Normal resume for existing session
          trackEvent.sessionResumed(effectiveSession.id);
          trackEvent.modelSelected(model);
          
          // Store session ID for potential future resume
          sessionStorage.setItem('lastClaudeSessionId', effectiveSession.id);
          
          await api.resumeClaudeCode(projectPath, effectiveSession.id, prompt, model);
        } else {
          // Start new session
          setIsFirstPrompt(false);
          trackEvent.sessionCreated(model, 'prompt_input');
          trackEvent.modelSelected(model);
          
          // Clear any stored session ID when starting fresh
          sessionStorage.removeItem('lastClaudeSessionId');
          sessionStorage.removeItem('claudeQuestionSessionId');
          
          await api.executeClaudeCode(projectPath, prompt, model);
        }
      }
    } catch (err) {
      const errorMessage = "Failed to send prompt";
      const categorizedError = categorizeError(errorMessage);
      setCurrentError(categorizedError);
      setError(errorMessage);
      setIsLoading(false);
      hasActiveSessionRef.current = false;
    }
  };

  const handleCopyAsJsonl = async () => {
    const jsonl = rawJsonlOutput.join('\n');
    await navigator.clipboard.writeText(jsonl);
    setCopyPopoverOpen(false);
  };

  const handleCopyAsMarkdown = async () => {
    let markdown = `# Claude Code Session\n\n`;
    markdown += `**Project:** ${projectPath}\n`;
    markdown += `**Date:** ${new Date().toISOString()}\n\n`;
    markdown += `---\n\n`;

    for (const msg of messages) {
      if (msg.type === "system" && msg.subtype === "init") {
        markdown += `## System Initialization\n\n`;
        markdown += `- Session ID: \`${msg.session_id || 'N/A'}\`\n`;
        markdown += `- Model: \`${msg.model || 'default'}\`\n`;
        if (msg.cwd) markdown += `- Working Directory: \`${msg.cwd}\`\n`;
        if (msg.tools?.length) markdown += `- Tools: ${msg.tools.join(', ')}\n`;
        markdown += `\n`;
      } else if (msg.type === "assistant" && msg.message) {
        markdown += `## Assistant\n\n`;
        for (const content of msg.message.content || []) {
          if (content.type === "text") {
            const textContent = typeof content.text === 'string' 
              ? content.text 
              : (content.text?.text || JSON.stringify(content.text || content));
            markdown += `${textContent}\n\n`;
          } else if (content.type === "tool_use") {
            markdown += `### Tool: ${content.name}\n\n`;
            markdown += `\`\`\`json\n${JSON.stringify(content.input, null, 2)}\n\`\`\`\n\n`;
          }
        }
        if (msg.message.usage) {
          markdown += `*Tokens: ${msg.message.usage.input_tokens} in, ${msg.message.usage.output_tokens} out*\n\n`;
        }
      } else if (msg.type === "user" && msg.message) {
        markdown += `## User\n\n`;
        for (const content of msg.message.content || []) {
          if (content.type === "text") {
            const textContent = typeof content.text === 'string' 
              ? content.text 
              : (content.text?.text || JSON.stringify(content.text));
            markdown += `${textContent}\n\n`;
          } else if (content.type === "tool_result") {
            markdown += `### Tool Result\n\n`;
            let contentText = '';
            if (typeof content.content === 'string') {
              contentText = content.content;
            } else if (content.content && typeof content.content === 'object') {
              if (content.content.text) {
                contentText = content.content.text;
              } else if (Array.isArray(content.content)) {
                contentText = content.content
                  .map((c: any) => (typeof c === 'string' ? c : c.text || JSON.stringify(c)))
                  .join('\n');
              } else {
                contentText = JSON.stringify(content.content, null, 2);
              }
            }
            markdown += `\`\`\`\n${contentText}\n\`\`\`\n\n`;
          }
        }
      } else if (msg.type === "result") {
        markdown += `## Execution Result\n\n`;
        if (msg.result) {
          markdown += `${msg.result}\n\n`;
        }
        if (msg.error) {
          markdown += `**Error:** ${msg.error}\n\n`;
        }
      }
    }

    await navigator.clipboard.writeText(markdown);
    setCopyPopoverOpen(false);
  };

  // Use the checkpoints hook
  const {
    checkpoints,
    timelineVersion,
    createCheckpoint,
    restoreCheckpoint,
    forkCheckpoint: forkFromCheckpoint
  } = useCheckpoints({
    sessionId: effectiveSession?.id || null,
    projectId: effectiveSession?.project_id || '',
    projectPath,
    onToast: (msg, type) => {
      if (type === 'error') setError(msg);
    }
  });

  const handleCheckpointSelect = async () => {
    // Reload messages from the checkpoint
    await loadSessionHistory();
  };
  
  const handleCheckpointCreated = () => {
    // Update checkpoint count in session metrics
    incrementCheckpoints();
  };

  const handleCancelExecution = async () => {
    if (!claudeSessionId || !isLoading) return;
    
    try {
      const sessionStartTime = messages.length > 0 ? messages[0].timestamp || Date.now() : Date.now();
      const duration = Date.now() - sessionStartTime;
      
      await api.cancelClaudeExecution(claudeSessionId);
      
      // Calculate metrics for enhanced analytics
      const metrics = getMetrics();
      const timeToFirstMessage = metrics.firstMessageTime 
        ? metrics.firstMessageTime - sessionStartTime.current 
        : undefined;
      const idleTime = Date.now() - metrics.lastActivityTime;
      const avgResponseTime = metrics.toolExecutionTimes.length > 0
        ? metrics.toolExecutionTimes.reduce((a: number, b: number) => a + b, 0) / metrics.toolExecutionTimes.length
        : undefined;
      
      // Track enhanced session stopped
      trackEvent.enhancedSessionStopped({
        // Basic metrics
        duration_ms: duration,
        messages_count: messages.length,
        reason: 'user_stopped',
        
        // Timing metrics
        time_to_first_message_ms: timeToFirstMessage,
        average_response_time_ms: avgResponseTime,
        idle_time_ms: idleTime,
        
        // Interaction metrics
        prompts_sent: metrics.promptsSent,
        tools_executed: metrics.toolsExecuted,
        tools_failed: metrics.toolsFailed,
        files_created: metrics.filesCreated,
        files_modified: metrics.filesModified,
        files_deleted: metrics.filesDeleted,
        
        // Content metrics
        total_tokens_used: totalTokens,
        code_blocks_generated: metrics.codeBlocksGenerated,
        errors_encountered: metrics.errorsEncountered,
        
        // Session context
        model: metrics.modelChanges.length > 0 
          ? metrics.modelChanges[metrics.modelChanges.length - 1].to 
          : 'claude-sonnet-4-20250514', // Default to sonnet 4
        has_checkpoints: metrics.checkpointCount > 0,
        checkpoint_count: metrics.checkpointCount,
        was_resumed: metrics.wasResumed,
        
        // Agent context (if applicable)
        agent_type: activeAgent?.type || undefined,
        agent_name: activeAgent?.name || undefined,
        agent_success: activeAgent?.success || undefined,
        
        // Stop context
        stop_source: 'user_button',
        final_state: 'cancelled',
        has_pending_prompts: queuedPrompts.length > 0,
        pending_prompts_count: queuedPrompts.length,
      });
      
      // Clean up listeners
      unlistenRefs.current.forEach(unlisten => unlisten());
      unlistenRefs.current = [];
      
      // Reset states
      setIsLoading(false);
      hasActiveSessionRef.current = false;
      isListeningRef.current = false;
      setError(null);
      
      // Clear queued prompts
      setQueuedPrompts([]);
      
      // Add a message indicating the session was cancelled
      const cancelMessage: ClaudeStreamMessage = {
        type: "system",
        subtype: "info",
        result: "Session cancelled by user",
        timestamp: new Date().toISOString()
      };
      setMessages(prev => [...prev, cancelMessage]);
    } catch (err) {
      
      // Even if backend fails, we should update UI to reflect stopped state
      // Add error message but still stop the UI loading state
      const errorMessageText = `Failed to cancel execution: ${err instanceof Error ? err.message : 'Unknown error'}. The process may still be running in the background.`;
      const categorizedError = categorizeError(errorMessageText);
      setCurrentError(categorizedError);
      
      const errorMessage: ClaudeStreamMessage = {
        type: "system",
        subtype: "error",
        result: errorMessageText,
        timestamp: new Date().toISOString()
      };
      setMessages(prev => [...prev, errorMessage]);
      
      // Clean up listeners anyway
      unlistenRefs.current.forEach(unlisten => unlisten());
      unlistenRefs.current = [];
      
      // Reset states to allow user to continue
      setIsLoading(false);
      hasActiveSessionRef.current = false;
      isListeningRef.current = false;
      setError(null);
      setCurrentError(null);
    }
  };

  const handleSendToBackground = async () => {
    if (!claudeSessionId) return;
    
    try {
      await api.sendToBackground(claudeSessionId);
      setIsInBackground(true);
      setIsLoading(false); // Stop loading since we're sending to background
    } catch (err) {
      const errorMessage = "Failed to send session to background";
      const categorizedError = categorizeError(errorMessage);
      setCurrentError(categorizedError);
      setError(errorMessage);
    }
  };

  const handleBringToForeground = async () => {
    if (!claudeSessionId) return;
    
    try {
      await api.bringToForeground(claudeSessionId);
      setIsInBackground(false);
      setIsLoading(true); // Resume loading state
    } catch (err) {
      const errorMessage = "Failed to bring session to foreground";
      const categorizedError = categorizeError(errorMessage);
      setCurrentError(categorizedError);
      setError(errorMessage);
    }
  };

  const handleRetryLastPrompt = useCallback(() => {
    // This would retry the last user prompt
    // For now, we can just clear the error and let the user retry manually
    setCurrentError(null);
    setError(null);
  }, []);

  const categorizeError = (errorMessage: string): ErrorDetails => {
    const timestamp = Date.now();
    
    // Check for common error patterns
    if (errorMessage.includes('tool') && errorMessage.includes('failed')) {
      return {
        type: 'tool_execution',
        message: errorMessage,
        timestamp,
        recoveryOptions: [
          'Retry the last tool execution',
          'Try a different approach',
          'Check tool parameters',
          'Review tool documentation'
        ]
      };
    }
    
    if (errorMessage.includes('file') || errorMessage.includes('permission') || errorMessage.includes('access')) {
      return {
        type: 'file_access',
        message: errorMessage,
        timestamp,
        recoveryOptions: [
          'Check file permissions',
          'Verify file path exists',
          'Try a different file path',
          'Run with elevated permissions'
        ]
      };
    }
    
    if (errorMessage.includes('network') || errorMessage.includes('connection') || errorMessage.includes('timeout')) {
      return {
        type: 'network',
        message: errorMessage,
        timestamp,
        recoveryOptions: [
          'Check internet connection',
          'Retry the operation',
          'Check firewall settings',
          'Try again later'
        ]
      };
    }
    
    if (errorMessage.includes('model') || errorMessage.includes('token') || errorMessage.includes('limit')) {
      return {
        type: 'model',
        message: errorMessage,
        timestamp,
        recoveryOptions: [
          'Switch to a different model',
          'Reduce prompt complexity',
          'Check API key validity',
          'Contact support'
        ]
      };
    }
    
    if (errorMessage.includes('checkpoint') || errorMessage.includes('snapshot')) {
      return {
        type: 'checkpoint',
        message: errorMessage,
        timestamp,
        recoveryOptions: [
          'Restore from previous checkpoint',
          'Restart from beginning',
          'Check disk space',
          'Review checkpoint settings'
        ]
      };
    }
    
    if (errorMessage.includes('session') || errorMessage.includes('process')) {
      return {
        type: 'session',
        message: errorMessage,
        timestamp,
        recoveryOptions: [
          'Restart the session',
          'Check running processes',
          'Clear session cache',
          'Create a new session'
        ]
      };
    }
    
    // Default unknown error
    return {
      type: 'unknown',
      message: errorMessage,
      timestamp,
      recoveryOptions: [
        'Retry the operation',
        'Restart the application',
        'Check logs for more details',
        'Contact support'
      ]
    };
  };

  const handleFork = (checkpointId: string) => {
    setForkCheckpointId(checkpointId);
    setForkSessionName(`Fork-${new Date().toISOString().slice(0, 10)}`);
    setShowForkDialog(true);
  };

  const handleConfirmFork = async () => {
    if (!forkCheckpointId || !forkSessionName.trim() || !effectiveSession) return;
    
    try {
      setIsLoading(true);
      setError(null);
      
      const forkedSession = await forkFromCheckpoint(forkCheckpointId, forkSessionName);
      
      if (forkedSession) {
        // Open the new forked session
        setShowForkDialog(false);
        setForkCheckpointId(null);
        setForkSessionName("");
      }
    } catch (err) {
      setError("Failed to fork checkpoint");
    } finally {
      setIsLoading(false);
    }
  };

  // Preview handlers are now provided by usePreview hook

  // Cleanup event listeners and track mount state
  useEffect(() => {
    isMountedRef.current = true;
    
    return () => {
      isMountedRef.current = false;
      isListeningRef.current = false;
      
      // Track session completion with engagement metrics
      if (effectiveSession) {
        trackEvent.sessionCompleted();
        
        // Track session engagement
        const sessionDuration = sessionStartTime.current ? Date.now() - sessionStartTime.current : 0;
        const messageCount = messages.filter(m => m.user_message).length;
        const toolsUsed = new Set<string>();
        messages.forEach(msg => {
          if (msg.type === 'assistant' && msg.message?.content) {
            const tools = msg.message.content.filter((c: any) => c.type === 'tool_use');
            tools.forEach((tool: any) => toolsUsed.add(tool.name));
          }
        });
        
        // Calculate engagement score (0-100)
        const engagementScore = Math.min(100, 
          (messageCount * 10) + 
          (toolsUsed.size * 5) + 
          (sessionDuration > 300000 ? 20 : sessionDuration / 15000) // 5+ min session gets 20 points
        );
        
        trackEvent.sessionEngagement({
          session_duration_ms: sessionDuration,
          messages_sent: messageCount,
          tools_used: Array.from(toolsUsed),
          files_modified: 0, // TODO: Track file modifications
          engagement_score: Math.round(engagementScore)
        });
      }
      
      // Clean up listeners
      unlistenRefs.current.forEach(unlisten => unlisten());
      unlistenRefs.current = [];
      
      // Clear checkpoint manager when session ends
      if (effectiveSession) {
        api.clearCheckpointManager(effectiveSession.id).catch(err => {
        });
      }
    };
  }, [effectiveSession, projectPath]);

  const messagesList = (
    <div ref={scrollContainerRef} className="flex-1 overflow-hidden flex flex-col w-full">
      <MessageList
        messages={displayableMessages}
        variant="enhanced"
        features={{
          showAgentIndicators: true,
          showMcpBadges: true,
          showCopyButtons: true,
          showTimestamps: true,
          showTokenCounts: true,
          showToolCalls: true,
          showThinking: isExtendedThinking,
          collapsibleSections: true
        }}
        isStreaming={isLoading}
        activeAgent={activeAgent}
        className="flex-1 w-full"
      />
      
      {/* Error Recovery System */}
      <ErrorRecoverySystem
        error={currentError}
        onDismiss={() => {
          setCurrentError(null);
          setError(null);
        }}
        onRetry={() => {
          // Simple retry - clear error and try again
          setCurrentError(null);
          setError(null);
        }}
        onRetryLastPrompt={handleRetryLastPrompt}
        claudeSessionId={claudeSessionId}
        projectPath={projectPath}
      />
    </div>
  );

  const projectPathInput = !session && (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay: 0.1 }}
      className="p-4 border-b border-border flex-shrink-0"
    >
      <Label htmlFor="project-path" className="text-sm font-medium">
        Project Directory
      </Label>
      <div className="flex items-center gap-2 mt-1">
        <Input
          id="project-path"
          value={projectPath}
          onChange={(e) => setProjectPath(e.target.value)}
          placeholder="/path/to/your/project"
          className="flex-1"
          disabled={isLoading}
        />
        <Button
          onClick={handleSelectPath}
          size="icon"
          variant="outline"
          disabled={isLoading}
        >
          <FolderOpen className="h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  );

  // If preview is maximized, render only the WebviewPreview in full screen
  if (showPreview && isPreviewMaximized) {
    return (
      <AnimatePresence>
        <motion.div 
          className="fixed inset-0 z-50 bg-background"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
        >
          <WebviewPreview
            initialUrl={previewUrl}
            onClose={handleClosePreview}
            isMaximized={isPreviewMaximized}
            onToggleMaximize={handleTogglePreviewMaximize}
            onUrlChange={handlePreviewUrlChange}
            className="h-full"
          />
        </motion.div>
      </AnimatePresence>
    );
  }

  // Handle agent execution
  const executeDetectedAgent = async () => {
    if (!detectedAgentOpportunity || !projectPath) return;
    
    try {
      // Get available agents
      const agents = await api.listAgents();
      const agent = agents.find(a => 
        a.name.toLowerCase().includes(detectedAgentOpportunity.agentType.toLowerCase())
      );
      
      if (!agent || !agent.id) {
        setError(`Agent ${detectedAgentOpportunity.agentType} not found`);
        return;
      }
      
      // Prepare context
      const context = {
        session_id: claudeSessionId || '',
        project_path: projectPath,
        recent_messages: messages.slice(-10).map(m => m.message || {}),
        checkpoints: checkpoints.map((c: any) => c.id),
        metrics: { totalTokens, messageCount: messages.length }
      };
      
      // Execute agent
      setActiveAgent({
        type: detectedAgentOpportunity.agentType,
        name: agent.name,
        taskId: `task-${Date.now()}`
      });
      
      const result = await api.executeAgentInSession(
        claudeSessionId || '',
        agent.id,
        detectedAgentOpportunity.reason,
        context
      );
      
      // Update agent status
      setActiveAgent(prev => prev ? { ...prev, success: result.success } : null);
      
      // Clear detection after execution
      setDetectedAgentOpportunity(null);
    } catch (err) {
      setError(`Failed to execute agent: ${err}`);
      setActiveAgent(null);
    }
  };

  return (
    <div className={cn("flex flex-col h-full bg-background", className)}>
      <div className="w-full h-full flex flex-col">
        {/* Agent Opportunity Alert */}
        {detectedAgentOpportunity && (
          <div className="mx-4 mt-2 p-3 bg-purple-500/10 border border-purple-500/20 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Bot className="h-5 w-5 text-purple-500" />
                <span className="text-sm font-medium">Agent Opportunity Detected</span>
                <Badge variant="secondary" className="text-xs">
                  {detectedAgentOpportunity.agentType}
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-xs text-muted-foreground">
                  Confidence: {Math.round(detectedAgentOpportunity.confidence * 100)}%
                </span>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setDetectedAgentOpportunity(null)}
                >
                  Dismiss
                </Button>
                <Button
                  size="sm"
                  variant="default"
                  onClick={executeDetectedAgent}
                  className="bg-purple-500 hover:bg-purple-600"
                >
                  <Play className="h-4 w-4 mr-1" />
                  Execute Agent
                </Button>
              </div>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {detectedAgentOpportunity.reason}
            </p>
          </div>
        )}
        
        {/* Active Agent Status */}
        {activeAgent && (
          <div className="mx-4 mt-2 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Bot className="h-5 w-5 text-blue-500 animate-pulse" />
                <span className="text-sm font-medium">Agent Running</span>
                <Badge variant="outline" className="text-xs">
                  {activeAgent.name}
                </Badge>
              </div>
              {activeAgent.success !== undefined && (
                <Badge variant={activeAgent.success ? "default" : "destructive"}>
                  {activeAgent.success ? "Completed" : "Failed"}
                </Badge>
              )}
            </div>
            {activeAgent.taskId && (
              <p className="text-xs text-muted-foreground mt-1">
                Task ID: {activeAgent.taskId}
              </p>
            )}
          </div>
        )}
        {/* Header */}
        <SessionHeader
          projectPath={projectPath}
          claudeSessionId={claudeSessionId}
          totalTokens={totalTokens}
          resumeFlag={resumeFlag}
          isLoading={isLoading}
          showSettings={showSettings}
          showTimeline={showTimeline}
          hasSession={!!effectiveSession}
          isInBackground={isInBackground}
          isStreaming={isLoading}
          hasMessages={messages.length > 0}
          copyPopoverOpen={copyPopoverOpen}
          onBack={onBack}
          onSelectPath={handleSelectPath}
          onCopyAsJsonl={handleCopyAsJsonl}
          onCopyAsMarkdown={handleCopyAsMarkdown}
          onToggleTimeline={() => setShowTimeline(!showTimeline)}
          onToggleSettings={() => setShowSettings(!showSettings)}
          onProjectSettings={projectPath && onProjectSettings ? () => onProjectSettings(projectPath) : undefined}
          onSlashCommandsSettings={() => setShowSlashCommandsSettings(true)}
          onUsageDashboard={onUsageDashboard}
          onSendToBackground={handleSendToBackground}
          onBringToForeground={handleBringToForeground}
          setCopyPopoverOpen={setCopyPopoverOpen}
        />
        
        {/* Checkpoint Settings Modal - only show when settings is active */}
        {showSettings && effectiveSession && (
          <div className="space-y-6">
            <SessionMetricsVisualizer 
              metrics={sessionMetrics}
              totalTokens={totalTokens}
            />
            <CheckpointSettings
              sessionId={effectiveSession.id}
              projectId={effectiveSession.project_id}
              projectPath={projectPath}
            />
          </div>
        )}

        {/* Extended Thinking Indicator */}
        <ExtendedThinkingIndicator
          isActive={isExtendedThinking}
          progress={thinkingProgress}
          stage={thinkingStage}
          estimatedTime={thinkingEstimatedTime}
        />

        {/* Plan Mode Indicator */}
        <PlanModeIndicator
          isActive={isPlanMode}
          plan={currentPlan}
          onToggle={() => setIsPlanMode(!isPlanMode)}
          onApprove={async () => {
            // Send approval to continue execution
            if (claudeSessionId) {
              try {
                await api.approvePlanExecution(claudeSessionId);
                setIsPlanMode(false);
              } catch (err) {
              }
            }
          }}
          onReject={async () => {
            // Reject the plan and cancel execution
            if (claudeSessionId) {
              try {
                await api.rejectPlanExecution(claudeSessionId);
                setIsPlanMode(false);
                setCurrentPlan(undefined);
              } catch (err) {
              }
            }
          }}
        />

        {/* Main Content Area with Agent/MCP Panel */}
        <div className="flex-1 flex overflow-hidden">
          <div className={cn(
            "flex-1 overflow-hidden transition-all duration-300",
            showTimeline && "sm:mr-96",
            ""
          )}>
            {showPreview ? (
              // Split pane layout when preview is active
              <SplitPane
                left={
                  <div className="h-full flex flex-col">
                    {projectPathInput}
                    {messagesList}
                  </div>
                }
                right={
                  <WebviewPreview
                    initialUrl={previewUrl}
                    onClose={handleClosePreview}
                    isMaximized={isPreviewMaximized}
                    onToggleMaximize={handleTogglePreviewMaximize}
                    onUrlChange={handlePreviewUrlChange}
                  />
                }
                initialSplit={splitPosition}
                onSplitChange={setSplitPosition}
                minLeftWidth={400}
                minRightWidth={400}
                className="h-full"
              />
            ) : (
              // Original layout when no preview
              <div className="h-full flex flex-col max-w-5xl mx-auto">
                {projectPathInput}
                {messagesList}
                
                {isLoading && messages.length === 0 && (
                  <div className="flex items-center justify-center h-full">
                    <div className="flex items-center gap-3">
                      <div className="rotating-symbol text-primary" />
                      <span className="text-sm text-muted-foreground">
                        {session ? "Loading session history..." : "Initializing Claude Code..."}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
          
          {/* Agent MCP and Orchestration panels removed - they run in background */}
        </div>

        {/* Floating Prompt Input - Always visible */}
        <ErrorBoundary>
          {/* Queued Prompts Display */}
          {queuedPrompts.length > 0 && (
            <div className="fixed bottom-24 left-1/2 -translate-x-1/2 z-30 w-full max-w-3xl px-4">
              <PromptQueue
                queuedPrompts={queuedPrompts}
                onRemove={(id) => setQueuedPrompts(prev => prev.filter(p => p.id !== id))}
                className="bg-background/95 backdrop-blur-md border rounded-lg shadow-lg"
              />
            </div>
          )}

          {/* Navigation Arrows - positioned above prompt bar with spacing */}
          {displayableMessages.length > 5 && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ delay: 0.5 }}
              className="fixed bottom-32 right-6 z-50"
            >
              <div className="flex items-center bg-background/95 backdrop-blur-md border rounded-full shadow-lg overflow-hidden">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    // Scroll to top of the container
                    if (displayableMessages.length > 0 && scrollContainerRef.current) {
                      const messageList = scrollContainerRef.current.querySelector('.overflow-y-auto');
                      messageList?.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                      });
                    }
                  }}
                  className="px-3 py-2 hover:bg-accent rounded-none"
                  title="Scroll to top"
                >
                  <ChevronUp className="h-4 w-4" />
                </Button>
                <div className="w-px h-4 bg-border" />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    // Scroll to bottom of the container
                    if (displayableMessages.length > 0 && scrollContainerRef.current) {
                      const messageList = scrollContainerRef.current.querySelector('.overflow-y-auto');
                      if (messageList) {
                        messageList.scrollTo({
                          top: messageList.scrollHeight,
                          behavior: 'smooth'
                        });
                      }
                    }
                  }}
                  className="px-3 py-2 hover:bg-accent rounded-none"
                  title="Scroll to bottom"
                >
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </div>
            </motion.div>
          )}

          <div className={cn(
            "fixed bottom-0 left-0 right-0 transition-all duration-300 z-50",
            showTimeline && "sm:right-96",
            ""
          )}>
            {/* Waiting for Response Indicator */}
            {isWaitingForResponse && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
                className="absolute -top-16 left-1/2 -translate-x-1/2 bg-yellow-500/10 border border-yellow-500/30 rounded-lg px-4 py-2 flex items-center gap-2"
              >
                <div className="flex items-center gap-2">
                  <div className="animate-pulse">
                    <MessageCircle className="h-4 w-4 text-yellow-500" />
                  </div>
                  <span className="text-sm text-yellow-600 dark:text-yellow-400">
                    Claude is waiting for your response...
                  </span>
                </div>
              </motion.div>
            )}
            
            <FloatingPromptInput
              ref={floatingPromptRef}
              onSend={handleSendPrompt}
              onCancel={handleCancelExecution}
              isLoading={isLoading}
              disabled={!projectPath}
              sessionId={effectiveSession?.id}
              projectId={effectiveSession?.project_id}
              projectPath={projectPath}
            />
          </div>

          {/* Token Counter - positioned under the Send button */}
          {totalTokens > 0 && (
            <div className="fixed bottom-0 left-0 right-0 z-30 pointer-events-none">
              <div className="max-w-5xl mx-auto">
                <div className="flex justify-end px-4 pb-2">
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    className="bg-background/95 backdrop-blur-md border rounded-full px-3 py-1 shadow-lg pointer-events-auto"
                  >
                    <div className="flex items-center gap-1.5 text-xs">
                      <Hash className="h-3 w-3 text-muted-foreground" />
                      <span className="font-mono">{totalTokens.toLocaleString()}</span>
                      <span className="text-muted-foreground">tokens</span>
                    </div>
                  </motion.div>
                </div>
              </div>
            </div>
          )}
        </ErrorBoundary>

        {/* Timeline */}
        <AnimatePresence>
          {showTimeline && effectiveSession && (
            <motion.div
              initial={{ x: "100%" }}
              animate={{ x: 0 }}
              exit={{ x: "100%" }}
              transition={{ type: "spring", damping: 20, stiffness: 300 }}
              className="fixed right-0 top-0 h-full w-full sm:w-96 bg-background border-l border-border shadow-xl z-30 overflow-hidden"
            >
              <div className="h-full flex flex-col">
                {/* Timeline Header */}
                <div className="flex items-center justify-between p-4 border-b border-border">
                  <h3 className="text-lg font-semibold">Session Timeline</h3>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setShowTimeline(false)}
                    className="h-8 w-8"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                
                {/* Timeline Content */}
                <div className="flex-1 overflow-y-auto p-4">
                  <TimelineNavigator
                    sessionId={effectiveSession.id}
                    projectId={effectiveSession.project_id}
                    projectPath={projectPath}
                    currentMessageIndex={messages.length - 1}
                    onCheckpointSelect={handleCheckpointSelect}
                    onFork={handleFork}
                    onCheckpointCreated={handleCheckpointCreated}
                    refreshVersion={timelineVersion}
                  />
                </div>
              </div>
            </motion.div>
          )}
          
          {/* SuperClaude Panel */}
          
          {/* Agent Workflow Panel removed - runs in background */}
        </AnimatePresence>
      </div>

      {/* Fork Dialog */}
      <Dialog open={showForkDialog} onOpenChange={setShowForkDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Fork Session</DialogTitle>
            <DialogDescription>
              Create a new session branch from the selected checkpoint.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="fork-name">New Session Name</Label>
              <Input
                id="fork-name"
                placeholder="e.g., Alternative approach"
                value={forkSessionName}
                onChange={(e) => setForkSessionName(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === "Enter" && !isLoading) {
                    handleConfirmFork();
                  }
                }}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowForkDialog(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleConfirmFork}
              disabled={isLoading || !forkSessionName.trim()}
            >
              Create Fork
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Settings Dialog */}
      {showSettings && effectiveSession && (
        <Dialog open={showSettings} onOpenChange={setShowSettings}>
          <DialogContent className="max-w-2xl">
            <DialogTitle className="sr-only">Checkpoint Settings</DialogTitle>
            <CheckpointSettings
              sessionId={effectiveSession.id}
              projectId={effectiveSession.project_id}
              projectPath={projectPath}
              onClose={() => setShowSettings(false)}
            />
          </DialogContent>
        </Dialog>
      )}

      {/* Slash Commands Settings Dialog */}
      {showSlashCommandsSettings && (
        <Dialog open={showSlashCommandsSettings} onOpenChange={setShowSlashCommandsSettings}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
            <DialogHeader>
              <DialogTitle>Slash Commands</DialogTitle>
              <DialogDescription>
                Manage project-specific slash commands for {projectPath}
              </DialogDescription>
            </DialogHeader>
            <div className="flex-1 overflow-y-auto">
              <SlashCommandsManager projectPath={projectPath} />
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};
