import React, { lazy, Suspense, useState } from 'react';
import { format } from 'date-fns';
import { useQueryClient } from '@tanstack/react-query';

// UI Components
import { Toast, ToastContainer } from '@/components/ui/toast';

// React Query Hooks
import {
  useQuarterlyAllocations,
  useDepartmentAllocations,
  useCategoryLimits,
  useExpenses,
  useAllocationRules,
  useBudgetAnalytics,
  useCreateExpense,
  useUpdateExpense,
  useDeleteExpense,
  useUpdateExpenseStatus,
  budgetKeys
} from '@/hooks/queries/useBudgetQueries';

// Local Components
import { MainLayout } from './components/layouts/MainLayout';
import { BudgetSidebar } from './components/panels/BudgetSidebar';
import { BudgetHeader } from './components/panels/BudgetHeader';
import { TabManager } from './components/TabManager';
import { QuickActionsMenu } from './components/quickactions';
import { DialogManager } from './components/dialogs/DialogManager';

// Lazy-loaded Pages
const BudgetTabPage = lazy(() => import('./pages/BudgetTabPage'));
const ExpensesTabPage = lazy(() => import('./pages/ExpensesTabPage'));
const AnalyticsTabPage = lazy(() => import('./pages/AnalyticsTabPage'));
const SettingsPage = lazy(() => import('./pages/SettingsPage'));

// Local Hooks
import { useBudgetData } from './hooks/useBudgetData';
import { useBudgetActions } from './hooks/useBudgetActions';
import { useBudgetTabs } from './hooks/useBudgetTabs';
import { useToast } from './hooks/useToast';
import { useBudgetHealth } from './hooks/useBudgetHealth';
import { useHeatmapData } from './hooks/useHeatmapData';
import { useCalendarEvents } from './hooks/useCalendarEvents';
import { useExpenseManagement } from './hooks/useExpenseManagement';
import { useDialogState } from './hooks/useDialogState';
import { useTabConfigurations } from './hooks/useTabConfigurations';
import { useQuickActionsConfiguration } from './hooks/useQuickActionsConfiguration';
import { useBudgetApiOperations } from './hooks/useBudgetApiOperations';

// Local Utils
import { formatCurrency } from './utils/formatters';

// Local Types
import { Expense } from './types/expense.types';
import { ExpenseFormData } from './types/expense.types';

/**
 * Main Training Budget component that orchestrates the entire budget management interface.
 * Integrates all budget-related functionality including expense management, analytics,
 * health monitoring, and interactive visualizations.
 *
 * @returns React component for the training budget interface
 */
export const TrainingBudget: React.FC = () => {
  // Query client for cache management
  const queryClient = useQueryClient();

  // Core budget data and state management
  const { year, stats, budget, loading } = useBudgetData();
  const { updateBudget, isUpdating } = useBudgetActions();

  // Dialog state management for modals and forms
  const dialogState = useDialogState();

  // Year management
  const changeYear = (newYear: number) => {
    window.location.hash = `#/budget/${newYear}`;
  };
  const { toast, showToast, hideToast } = useToast();

  // Tab management
  const { activeTab, setActiveTab } = useBudgetTabs('budget');


  // Sidebar view state management
  const [sidebarView, setSidebarView] = useState<'health' | 'heatmap' | 'calendar'>('health');

  // React Query data fetching with proper loading states
  const { data: quarterlyAllocations = [] } = useQuarterlyAllocations(year);
  const { data: departmentAllocations = [] } = useDepartmentAllocations(year);
  const { data: categoryLimits = [] } = useCategoryLimits(year);
  const { data: expenses = [], isLoading: expensesLoading } = useExpenses(year);
  const { data: allocationRules = [], isLoading: rulesLoading } = useAllocationRules();
  const { data: analytics = null, isLoading: analyticsLoading } = useBudgetAnalytics(year);

  // React Query mutations for expense operations
  const createExpenseMutation = useCreateExpense();
  const updateExpenseMutation = useUpdateExpense();
  const deleteExpenseMutation = useDeleteExpense();
  const updateExpenseStatusMutation = useUpdateExpenseStatus();

  // Budget API operations with toast notifications
  const budgetApiOperations = useBudgetApiOperations({ year, showToast });

  // Calculated data with proper type safety
  const healthMetrics = useBudgetHealth(stats as any, budget || null, year); // Cast to BudgetStats
  const heatmapData = useHeatmapData(expenses as Expense[]);
  const calendarEvents = useCalendarEvents(expenses as Expense[], stats as any, year); // Cast to BudgetStats

  // Expense management with proper types
  const addExpense = async (data: ExpenseFormData) => {
    try {
      // Ensure all required fields are provided for API compatibility
      const expenseData = {
        ...data,
        description: data.description || '',
        department_id: data.department_id || null,
        receipt_url: data.receipt_url || null,
        recurrence: data.recurrence || 'none',
        recurrence_end_date: data.recurrence_end_date || null,
      };
      await createExpenseMutation.mutateAsync(expenseData);
      return true;
    } catch (error) {
      return false;
    }
  };

  const updateExpense = async (id: string, data: Partial<Expense>) => {
    await updateExpenseMutation.mutateAsync({ id, updates: data });
  };

  const deleteExpense = async (id: string) => {
    await deleteExpenseMutation.mutateAsync(id);
  };

  const updateExpenseStatus = async (id: string, status: string) => {
    await updateExpenseStatusMutation.mutateAsync({ id, status });
  };

  const expenseActions = {
    addExpense: async (data: any): Promise<void> => {
      try {
        await createExpenseMutation.mutateAsync(data);
      } catch (error) {
        console.error('Failed to add expense:', error);
      }
    },
    updateExpense,
    deleteExpense,
    updateExpenseStatus,
  };

  const {
    showExpenseForm,
    editingExpense,
    expenseTemplates,
    handleExpenseSubmit,
    handleSaveTemplate,
    handleExpenseEdit,
    handleExpenseDelete,
    handleStatusChange,
    handleViewReceipt,
    openExpenseForm,
    closeExpenseForm,
  } = useExpenseManagement({
    expenses: expenses as Expense[],
    ...expenseActions,
  });

  // Data is automatically fetched by React Query hooks
  // No manual fetching needed as hooks handle this automatically

  const handleBudgetUpdate = async (amount: number) => {
    const result = await updateBudget(year, amount);

    if (result.success) {
      showToast('Budget updated successfully!', 'success');
    } else {
      showToast(result.error || 'Failed to update budget', 'error');
    }

    return result;
  };

  const handleRefresh = () => {
    // Data will be automatically refetched by React Query
  };

  // Configuration
  const quickActionsConfig = useQuickActionsConfiguration({
    setAdjustBudgetOpen: dialogState.setAdjustBudgetOpen,
    setTemplatesOpen: dialogState.setTemplatesOpen,
    setQuarterlyPlanOpen: dialogState.setQuarterlyPlanOpen,
    setAutoRulesOpen: dialogState.setAutoRulesOpen,
    setShowReserveFund: dialogState.setShowReserveFund,
    setShowScenarioPlanner: dialogState.setShowScenarioPlanner,
    setShowCarryoverRules: dialogState.setShowCarryoverRules,
    openExpenseForm,
    setActiveTab,
    showToast
  });

  const quickActions = quickActionsConfig.quickActions;
  const mobileStats = quickActionsConfig.mobileStats(stats || null);

  // Tab configurations
  const tabConfigs = useTabConfigurations({
    year,
    budget: budget || null,
    stats: stats || null,
    quarterlyAllocations,
    departmentAllocations,
    categoryLimits,
    allocationRules: allocationRules as any, // Different AllocationRule types
    expenses: expenses as Expense[],
    analytics,
    loading,
    rulesLoading,
    expensesLoading,
    analyticsLoading,
    onUpdateQuarterlyAllocations: budgetApiOperations.updateQuarterlyAllocations,
    onUpdateDepartmentAllocation: budgetApiOperations.updateDepartmentAllocation,
    onAddDepartment: budgetApiOperations.addDepartment,
    onRemoveDepartment: budgetApiOperations.removeDepartment,
    onSetCategoryLimit: budgetApiOperations.setCategoryLimit,
    onRemoveCategoryLimit: budgetApiOperations.removeCategoryLimit,
    onAddAllocationRule: budgetApiOperations.addAllocationRule,
    onUpdateAllocationRule: budgetApiOperations.updateAllocationRule,
    onDeleteAllocationRule: budgetApiOperations.deleteAllocationRule,
    onToggleAllocationRule: budgetApiOperations.toggleAllocationRule,
    onExecuteAllocationRule: budgetApiOperations.executeAllocationRule,
    onExpenseSubmit: handleExpenseSubmit,
    onExpenseEdit: handleExpenseEdit,
    onExpenseDelete: handleExpenseDelete,
    onStatusChange: handleStatusChange,
    onViewReceipt: handleViewReceipt,
    onSaveTemplate: handleSaveTemplate,
    addExpense,
    updateExpense,
    deleteExpense,
    updateExpenseStatus,
    expenseTemplates,
    showToast,
    onTabChange: setActiveTab,
  });

  // Diagnostic logging for tab configurations
  console.log('TrainingBudget: Tab configurations received:', tabConfigs.map(tab => ({
    value: tab.value,
    label: tab.label,
    componentName: tab.component.displayName || tab.component.name || 'Unknown'
  })));

  // Track active tab changes
  console.log('TrainingBudget: Active tab:', activeTab);

  return (
    <ToastContainer>
      {toast.isVisible && (
        <Toast
          message={toast.message}
          type={toast.type}
          onDismiss={hideToast}
          duration={3000}
        />
      )}

      <MainLayout
        sidebar={
          <BudgetSidebar
            isCollapsed={false}
            onToggleCollapse={() => {}}
            sidebarView={sidebarView}
            onViewChange={setSidebarView}
            stats={stats || null}
            healthMetrics={healthMetrics}
            heatmapData={heatmapData}
            calendarEvents={calendarEvents}
            loading={loading}
            onHeatmapDayClick={(date: Date, data: any) => {
              showToast(`Spent ${formatCurrency(data.amount)} on ${format(date, 'MMM d')}`, 'info');
            }}
            onCalendarEventClick={(event: any) => {
              showToast(`Event: ${event.title}`, 'info');
            }}
            onCalendarDateClick={(date: Date) => {
              console.log('Date clicked:', date);
            }}
            showToast={showToast}
          />
        }
        header={
          <BudgetHeader
            year={year}
            onYearChange={changeYear}
            stats={stats || null}
            budget={budget || null}
            healthMetrics={healthMetrics}
            loading={loading}
            isUpdating={isUpdating}
            onBudgetUpdate={handleBudgetUpdate}
            tabConfigs={tabConfigs}
            activeTab={activeTab}
            onTabChange={setActiveTab}
            sidebarCollapsed={false}
            onToggleSidebar={() => {}}
          />
        }
        content={
          <div className="flex-1 overflow-auto">
            <div className="max-w-7xl mx-auto p-6">
              <TabManager
                tabs={tabConfigs}
                activeTab={activeTab}
                onTabChange={setActiveTab}
                showTabList={false}
                gridCols={4}
              />
            </div>
          </div>
        }
        quickActions={
          <div className="hidden md:block">
            <QuickActionsMenu
              actions={quickActions}
              position="bottom-right"
              fabStyle={true}
              onActionExecute={(actionId) => {
                showToast(`Action "${actionId}" executed`, 'success');
              }}
            />
          </div>
        }
        dialogs={
          <DialogManager
            adjustBudgetOpen={dialogState.adjustBudgetOpen}
            templatesOpen={dialogState.templatesOpen}
            quarterlyPlanOpen={dialogState.quarterlyPlanOpen}
            autoRulesOpen={dialogState.autoRulesOpen}
            showReserveFund={dialogState.showReserveFund}
            showScenarioPlanner={dialogState.showScenarioPlanner}
            showCarryoverRules={dialogState.showCarryoverRules}
            showExpenseForm={showExpenseForm}
            setAdjustBudgetOpen={dialogState.setAdjustBudgetOpen}
            setTemplatesOpen={dialogState.setTemplatesOpen}
            setQuarterlyPlanOpen={dialogState.setQuarterlyPlanOpen}
            setAutoRulesOpen={dialogState.setAutoRulesOpen}
            setShowReserveFund={dialogState.setShowReserveFund}
            setShowScenarioPlanner={dialogState.setShowScenarioPlanner}
            setShowCarryoverRules={dialogState.setShowCarryoverRules}
            editingExpense={editingExpense}
            expenseTemplates={expenseTemplates}
            onExpenseSubmit={handleExpenseSubmit}
            onSaveExpenseTemplate={handleSaveTemplate}
            onExpenseEdit={handleExpenseEdit}
            onExpenseDelete={handleExpenseDelete}
            onStatusChange={handleStatusChange}
            onViewReceipt={handleViewReceipt}
            openExpenseForm={openExpenseForm}
            closeExpenseForm={closeExpenseForm}
            year={year}
            stats={stats || null}
            budget={budget || null}
            departmentAllocations={departmentAllocations}
            categoryLimits={categoryLimits}
            quarterlyAllocations={quarterlyAllocations}
            allocationRules={allocationRules as any}
            onBudgetUpdate={handleBudgetUpdate}
            onUpdateQuarterlyAllocations={budgetApiOperations.updateQuarterlyAllocations}
            onUpdateDepartmentAllocation={budgetApiOperations.updateDepartmentAllocation}
            onAddDepartment={budgetApiOperations.addDepartment}
            onRemoveDepartment={budgetApiOperations.removeDepartment}
            onSetCategoryLimit={budgetApiOperations.setCategoryLimit}
            onRemoveCategoryLimit={budgetApiOperations.removeCategoryLimit}
            onAddAllocationRule={budgetApiOperations.addAllocationRule}
            onUpdateAllocationRule={budgetApiOperations.updateAllocationRule}
            onDeleteAllocationRule={budgetApiOperations.deleteAllocationRule}
            onToggleAllocationRule={budgetApiOperations.toggleAllocationRule}
            onExecuteAllocationRule={budgetApiOperations.executeAllocationRule}
            onApplyBudgetTemplate={async (templateId: string) => {
              const { applyBudgetTemplate } = await import('@/lib/services/budgetApi');
              await applyBudgetTemplate(templateId, year);
              queryClient.invalidateQueries({ queryKey: budgetKeys.budget(year) });
              queryClient.invalidateQueries({ queryKey: budgetKeys.stats(year) });
              showToast('Template applied successfully!', 'success');
            }}
            onSaveTemplate={async (template: any) => {
              console.log('Saving template:', template);
              showToast('Template saved successfully!', 'success');
            }}
            onDeleteTemplate={async (templateId: string) => {
              console.log('Deleting template:', templateId);
              showToast('Template deleted successfully!', 'success');
            }}
            onExportTemplate={async (templateId: string) => {
              console.log('Exporting template:', templateId);
            }}
            onImportTemplate={async (file: File) => {
              console.log('Importing template:', file);
              showToast('Template imported successfully!', 'success');
            }}
            onUpdateReserveFund={async (year: number, amount: number) => {
              const { updateReserveFund } = await import('@/lib/services/budgetApi');
              await updateReserveFund(year, amount);
              queryClient.invalidateQueries({ queryKey: budgetKeys.budget(year) });
              showToast('Reserve fund updated!', 'success');
            }}
            onSetReserveTarget={async (year: number, target: number) => {
              const { setReserveTarget } = await import('@/lib/services/budgetApi');
              await setReserveTarget(year, target);
              queryClient.invalidateQueries({ queryKey: budgetKeys.budget(year) });
              showToast('Reserve target set!', 'success');
            }}
            onSaveScenario={async (scenario: any) => {
              const { saveBudgetScenario } = await import('@/lib/services/budgetApi');
              await saveBudgetScenario(year, scenario);
              queryClient.invalidateQueries({ queryKey: budgetKeys.analytics(year) });
              showToast('Scenario saved!', 'success');
            }}
            onApplyScenario={async (scenario: any) => {
              const { applyBudgetScenario } = await import('@/lib/services/budgetApi');
              await applyBudgetScenario(year, scenario);
              queryClient.invalidateQueries({ queryKey: budgetKeys.budget(year) });
              queryClient.invalidateQueries({ queryKey: budgetKeys.stats(year) });
              showToast('Scenario applied!', 'success');
            }}
            onApplyCarryoverRules={async (fromYear: number, toYear: number, rules: any) => {
              const { applyCarryoverRules } = await import('@/lib/services/budgetApi');
              await applyCarryoverRules(fromYear, toYear, rules);
              queryClient.invalidateQueries({ queryKey: budgetKeys.budget(toYear) });
              queryClient.invalidateQueries({ queryKey: budgetKeys.stats(toYear) });
              showToast('Carryover rules applied!', 'success');
            }}
            showToast={showToast}
          />
        }
      />
    </ToastContainer>
  );
};

export default TrainingBudget;