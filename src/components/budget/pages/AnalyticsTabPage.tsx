import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { SpendingTrendsChart } from '../components/analytics/charts/SpendingTrendsChart';
import { BudgetBurndown } from '../components/analytics/charts/BudgetBurndown';
import { YearOverYearComparison } from '../components/analytics/charts/YearOverYearComparison';
import { ForecastProjections } from '../components/analytics/charts/ForecastProjections';
import { CategoryDistribution } from '../components/analytics/charts/CategoryDistribution';
import { ReportGenerator } from '../components/reporting/ReportGenerator';
import { CustomReportBuilder } from '../components/reporting/CustomReportBuilder';
import { RealTimeChart } from '../components/charts/RealTimeChart';
import { BudgetStats } from '../types/budget.types';
import { Budget } from '@/stores/budgetStore';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { FileText, Download, BarChart3, TrendingUp, Activity, PieChart, Target } from 'lucide-react';

interface AnalyticsData {
  trends: {
    monthly?: any[];
    yearly?: any[];
  };
  insights?: {
    top_categories?: any[];
  };
}

interface AnalyticsTabPageProps {
  analytics: AnalyticsData | null;
  budget: Budget | null;
  stats: BudgetStats | null;
  roiMetrics?: any[];
  year: number;
  loading: boolean;
  onAddROIMetric?: (metric: any) => void;
  onUpdateROIMetric?: (id: string, updates: any) => void;
}

export const AnalyticsTabPage: React.FC<AnalyticsTabPageProps> = ({
  analytics,
  budget,
  stats,
  roiMetrics,
  year,
  loading,
  onAddROIMetric,
  onUpdateROIMetric
}) => {
  const [showReportGenerator, setShowReportGenerator] = useState(false);
  const [showCustomReport, setShowCustomReport] = useState(false);
  const [showRealTimeChart, setShowRealTimeChart] = useState(false);
  
  const totalBudget = budget?.total_amount || stats?.total || 0;
  const currentSpent = stats?.spent || 0;
  const remainingDays = Math.floor(
    (new Date(year, 11, 31).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
  );

  // Transform data for charts
  const burndownData = analytics?.trends?.monthly?.map((item: any) => ({
    date: item.month,
    planned: item.budget || totalBudget,
    actual: item.spent,
    projected: item.spent * 1.1
  })) || [];

  const yearComparisonData = analytics?.insights?.top_categories?.map((cat: any) => ({
    category: cat.category,
    [year - 1]: cat.previousAmount || 0, // Use actual previous year data if available
    [year]: cat.amount
  })) || [];

  const yearSummaries = analytics?.trends?.yearly?.map((y: any) => ({
    year: y.year,
    totalBudget: y.budget,
    totalSpent: y.spent,
    efficiency: y.budget > 0 ? (y.spent / y.budget) * 100 : 0,
    growth: y.growth || 0,
    categories: {},
    departments: {}
  })) || [];

  const historicalData = analytics?.trends?.monthly?.map((item: any) => ({
    date: item.month,
    actual: item.spent,
    forecast: item.budget,
    optimistic: item.budget * 1.1,
    realistic: item.budget,
    pessimistic: item.budget * 0.9,
    confidence: 0.8
  })) || [];

  return (
    <div className="space-y-6">
      {/* Action Buttons */}
      <div className="flex flex-wrap gap-2 justify-end">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowRealTimeChart(!showRealTimeChart)}
          className="gap-2"
        >
          <Activity className="h-4 w-4" />
          {showRealTimeChart ? 'Hide' : 'Show'} Real-Time
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowReportGenerator(!showReportGenerator)}
          className="gap-2"
        >
          <FileText className="h-4 w-4" />
          Generate Report
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowCustomReport(!showCustomReport)}
          className="gap-2"
        >
          <Download className="h-4 w-4" />
          Custom Report
        </Button>
      </div>

      {/* Optional Components */}
      {(showRealTimeChart || showReportGenerator || showCustomReport) && (
        <div className="grid grid-cols-1 gap-6">
          {/* Real-Time Chart */}
          {showRealTimeChart && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <RealTimeChart
                title="Live Budget Activity"
                dataKey="amount"
                color="#8884d8"
                updateInterval={5000}
                maxDataPoints={20}
                fetchData={async () => ({
                  timestamp: new Date().toISOString(),
                  amount: Math.random() * 1000 + currentSpent
                })}
              />
            </motion.div>
          )}

          {/* Report Generator */}
          {showReportGenerator && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <ReportGenerator
                budgetData={{
                  total: totalBudget,
                  spent: currentSpent,
                  remaining: totalBudget - currentSpent,
                  categories: stats?.by_category || []
                }}
                dateRange={{
                  start: new Date(year, 0, 1).toISOString(),
                  end: new Date(year, 11, 31).toISOString()
                }}
                onGenerateReport={(report) => {
                  console.log('Generated report:', report);
                }}
                onScheduleReport={(schedule) => {
                  console.log('Scheduled report:', schedule);
                }}
              />
            </motion.div>
          )}

          {/* Custom Report Builder */}
          {showCustomReport && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <CustomReportBuilder
                availableData={{
                  budget: budget || undefined,
                  expenses: [],
                  categories: stats?.by_category || [],
                  departments: [],
                  trends: analytics?.trends?.monthly || []
                }}
                onSaveReport={(report) => {
                  console.log('Saved custom report:', report);
                }}
                onExportReport={(format) => {
                  console.log('Export custom report as:', format);
                }}
              />
            </motion.div>
          )}
        </div>
      )}

      {/* Clean 2-Column Grid Layout for Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        
        {/* Primary Column - Left */}
        <div className="space-y-6">
          {/* Spending Trends */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <SpendingTrendsChart
              data={analytics?.trends?.monthly?.map((item: any) => ({
                period: item.month,
                budget: item.budget || 0,
                spent: item.spent || 0,
                categories: item.categories || {}
              })) || []}
              period="monthly"
              onPeriodChange={() => {}}
              loading={loading}
            />
          </motion.div>
          
          {/* Budget Burndown */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <BudgetBurndown
              data={burndownData}
              totalBudget={totalBudget}
              currentSpent={currentSpent}
              endDate={new Date(year, 11, 31).toISOString()}
              loading={loading}
            />
          </motion.div>

          {/* Forecast Projections */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <ForecastProjections
              historicalData={historicalData}
              totalBudget={totalBudget}
              currentSpent={currentSpent}
              remainingDays={remainingDays}
              loading={loading}
            />
          </motion.div>
        </div>
        
        {/* Secondary Column - Right */}
        <div className="space-y-6">
          {/* Quick Stats Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <Target className="h-5 w-5" />
                Quick Stats
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Utilization Rate</p>
                  <p className="text-2xl font-bold">
                    {totalBudget > 0 ? ((currentSpent / totalBudget) * 100).toFixed(1) : 0}%
                  </p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Days Remaining</p>
                  <p className="text-2xl font-bold">{remainingDays}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Daily Burn Rate</p>
                  <p className="text-2xl font-bold">
                    ${remainingDays > 0 ? ((totalBudget - currentSpent) / remainingDays).toFixed(0) : 0}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Projected End</p>
                  <p className="text-lg font-bold text-green-600">
                    {currentSpent < totalBudget ? 'On Track' : 'Over Budget'}
                  </p>
                </div>
              </div>
            </Card>
          </motion.div>
          
          {/* Category Distribution */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <CategoryDistribution
              data={stats?.by_category?.map(cat => ({
                category: cat.category,
                amount: cat.spent || 0,
                percentage: stats.spent > 0 ? ((cat.spent / stats.spent) * 100) : 0
              })) || []}
              loading={loading}
              title="Spending by Category"
            />
          </motion.div>
          
          {/* Year-over-Year Comparison */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <YearOverYearComparison
              data={yearComparisonData}
              currentYear={year}
              summaries={yearSummaries}
              comparisonYears={[year - 1, year]}
              loading={loading}
            />
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsTabPage;