import React from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Wallet, Calendar as CalendarIcon, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import { formatCurrency } from '../../utils/formatters';
import { YearSelector } from '../YearSelector';

interface BudgetHeaderProps {
  year: number;
  onYearChange: (year: number) => void;
  stats: {
    total?: number;
    spent?: number;
    remaining?: number;
  } | null | undefined;
  budget: {
    total_amount?: number;
  } | null | undefined;
  healthMetrics: {
    status?: string;
  } | null;
  loading: boolean;
  isUpdating: boolean;
  onBudgetUpdate: (amount: number) => Promise<{ success: boolean; error?: string }>;
  tabConfigs: Array<{
    value: string;
    label: string;
  }>;
  activeTab: string;
  onTabChange: (value: string) => void;
  sidebarCollapsed: boolean;
  onToggleSidebar: () => void;
}

export const BudgetHeader: React.FC<BudgetHeaderProps> = ({
  year,
  onYearChange,
  stats,
  budget,
  healthMetrics,
  loading,
  isUpdating,
  onBudgetUpdate,
  tabConfigs,
  activeTab,
  onTabChange,
  sidebarCollapsed,
  onToggleSidebar
}) => {
  return (
    <>
      {/* Sidebar Toggle Button - Only show when collapsed */}
      {sidebarCollapsed && (
        <button
          onClick={onToggleSidebar}
          className="absolute left-2 top-6 z-20 p-2 rounded-lg bg-neutral-100/80 backdrop-blur border-neutral-200 hover:bg-neutral-200 transition-colors text-neutral-400 hover:text-neutral-900"
          aria-label="Expand sidebar"
        >
          <ChevronRight className="h-4 w-4" />
        </button>
      )}

      {/* Fixed Header Section */}
      <div className="flex-shrink-0 bg-gradient-to-r from-neutral-100 to-neutral-100/80 border-b border-neutral-200 sticky top-0 z-10">
        <div className="container mx-auto px-4 py-3">
          <div className="space-y-4">
            {/* Top row - Title and Year Selector */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex items-center gap-4"
              >
                <div className="flex items-center gap-3">
                  <Wallet className="h-8 w-8 text-primary-500" aria-hidden="true" />
                  <div>
                    <h1 className="text-xl font-semibold tracking-tight text-neutral-900">Training Budget</h1>
                    <p className="text-xs text-neutral-400">Fiscal Year {year}</p>
                  </div>
                </div>
              </motion.div>

              <YearSelector
                year={year}
                onYearChange={onYearChange}
              />
            </div>

            {/* Bottom row - Budget Stats and Input */}
            <div className="space-y-4">
              {/* Budget Stats Cards */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="grid grid-cols-3 gap-2"
              >
                {/* Total Budget Card */}
                <Card className="p-3 bg-neutral-100 border-neutral-200">
                  <div className="space-y-1">
                    <div className="flex items-center justify-between">
                      <p className="text-xs font-medium text-neutral-400">Total Budget</p>
                      <Badge variant="outline" className="text-xs text-success-500 border-success-500">
                        {healthMetrics?.status?.toUpperCase() || 'EXCELLENT'}
                      </Badge>
                    </div>
                    <div className="text-lg font-semibold text-neutral-900">
                      {loading ? (
                        <Skeleton className="h-5 w-20" />
                      ) : (
                        formatCurrency(stats?.total ?? 0)
                      )}
                    </div>
                  </div>
                </Card>

                {/* Spent Card */}
                <Card className="p-3 bg-neutral-100 border-neutral-200">
                  <div className="space-y-1">
                    <p className="text-xs font-medium text-neutral-400">Spent</p>
                    <div className="text-lg font-semibold text-warning-500">
                      {loading ? (
                        <Skeleton className="h-5 w-20" />
                      ) : (
                        formatCurrency(stats?.spent ?? 0)
                      )}
                    </div>
                  </div>
                </Card>

                {/* Remaining Card */}
                <Card className="p-3 bg-neutral-100 border-neutral-200">
                  <div className="space-y-1">
                    <p className="text-xs font-medium text-neutral-400">Remaining</p>
                    <div className="text-lg font-semibold text-success-500">
                      {loading ? (
                        <Skeleton className="h-5 w-20" />
                      ) : (
                        formatCurrency(stats?.remaining ?? 0)
                      )}
                    </div>
                  </div>
                </Card>
              </motion.div>

            </div>

            {/* Tabs Navigation - Part of fixed header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="mt-4"
            >
              <div className="flex space-x-1 border-b border-neutral-200">
                {tabConfigs.map((tab) => (
                  <button
                    key={tab.value}
                    onClick={() => onTabChange(tab.value)}
                    className={`px-4 py-2 text-sm font-medium transition-colors hover:text-primary-600 ${
                      activeTab === tab.value
                        ? 'text-primary-500 border-b-2 border-primary-500'
                        : 'text-neutral-400'
                    }`}
                  >
                    {tab.label}
                  </button>
                ))}
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </>
  );
};

export default BudgetHeader;