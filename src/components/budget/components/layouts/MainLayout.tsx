import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface MainLayoutProps {
  sidebar: React.ReactNode;
  header: React.ReactNode;
  content: React.ReactNode;
  quickActions?: React.ReactNode;
  dialogs?: React.ReactNode;
  className?: string;
}

export const MainLayout: React.FC<MainLayoutProps> = ({
  sidebar,
  header,
  content,
  quickActions,
  dialogs,
  className = ''
}) => {
  return (
    <div className={cn("min-h-screen bg-gradient-to-br from-neutral-50 via-neutral-50 to-neutral-100/20", className)}>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
        className="h-screen grid grid-cols-[384px_1fr] grid-rows-[auto_1fr] overflow-hidden"
      >
        {/* Sidebar - Grid Item 1 */}
        <aside className="h-full overflow-hidden">
          {sidebar}
        </aside>

        {/* Main Content Area - Grid Item 2 */}
        <main className="h-full grid grid-rows-[auto_1fr] overflow-hidden">
          {/* Header */}
          <header className="flex-shrink-0">
            {header}
          </header>

          {/* Scrollable Content */}
          <div className="flex-1 overflow-y-auto">
            {content}
          </div>
        </main>

        {/* Quick Actions - Positioned absolutely */}
        <div className="absolute top-0 right-0 z-50">
          {quickActions}
        </div>

        {/* Dialogs - Positioned absolutely */}
        <div className="absolute inset-0 z-50">
          {dialogs}
        </div>
      </motion.div>
    </div>
  );
};

export default MainLayout;