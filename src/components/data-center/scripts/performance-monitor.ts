#!/usr/bin/env node
/**
 * Performance Monitoring Script
 * Monitors runtime performance metrics and generates reports
 */

import { PerformanceMonitor, ComponentPerformanceTracker, MemoryMonitor } from '../utils/performanceUtils';
import * as fs from 'fs/promises';
import * as path from 'path';

interface PerformanceReport {
  timestamp: string;
  metrics: Record<string, { avg: number; min: number; max: number; count: number }>;
  componentStats: Record<string, any>;
  memoryStats: any;
  recommendations: string[];
}

class PerformanceReporter {
  private monitor: PerformanceMonitor;
  private memoryMonitor: MemoryMonitor;
  private outputDir: string;
  private isMonitoring = false;

  constructor() {
    this.monitor = new PerformanceMonitor();
    this.memoryMonitor = MemoryMonitor.getInstance();
    this.outputDir = path.join(__dirname, '../../../dist/performance');
  }

  async startMonitoring(durationMs = 60000): Promise<PerformanceReport> {
    console.log('🚀 Starting performance monitoring...');
    
    // Ensure output directory exists
    await fs.mkdir(this.outputDir, { recursive: true });

    this.isMonitoring = true;
    this.memoryMonitor.startMonitoring(1000); // Monitor every second

    // Simulate some performance data collection
    await this.simulateActivity(durationMs);

    this.memoryMonitor.stopMonitoring();
    this.isMonitoring = false;

    // Generate report
    const report = await this.generateReport();
    await this.saveReport(report);

    console.log('✅ Performance monitoring complete!');
    console.log(`📊 Report saved to: ${this.outputDir}/performance-report.json`);

    return report;
  }

  private async simulateActivity(durationMs: number): Promise<void> {
    const startTime = Date.now();
    const endTime = startTime + durationMs;
    
    console.log(`⏱️  Monitoring for ${durationMs / 1000} seconds...`);

    while (Date.now() < endTime && this.isMonitoring) {
      // Simulate component render tracking
      ComponentPerformanceTracker.startRender('DataCenter');
      await this.sleep(Math.random() * 100 + 50); // 50-150ms render time
      ComponentPerformanceTracker.endRender('DataCenter');

      ComponentPerformanceTracker.startRender('DataPreview');
      await this.sleep(Math.random() * 50 + 25); // 25-75ms render time
      ComponentPerformanceTracker.endRender('DataPreview');

      ComponentPerformanceTracker.startRender('ProcessingPanel');
      await this.sleep(Math.random() * 200 + 100); // 100-300ms render time
      ComponentPerformanceTracker.endRender('ProcessingPanel');

      // Record some metrics
      this.monitor.recordMetric('api_response_time', Math.random() * 500 + 100);
      this.monitor.recordMetric('data_processing_time', Math.random() * 1000 + 200);
      this.monitor.recordMetric('ui_update_time', Math.random() * 50 + 10);

      await this.sleep(1000); // Wait 1 second between cycles
    }
  }

  private async generateReport(): Promise<PerformanceReport> {
    const metrics = this.monitor.getMetrics();
    const componentStats = ComponentPerformanceTracker.getAllStats();
    const memoryStats = this.memoryMonitor.getMemoryStats();
    
    const recommendations = this.generateRecommendations(metrics, componentStats, memoryStats);

    return {
      timestamp: new Date().toISOString(),
      metrics,
      componentStats,
      memoryStats,
      recommendations,
    };
  }

  private generateRecommendations(
    metrics: Record<string, any>,
    componentStats: Record<string, any>,
    memoryStats: any
  ): string[] {
    const recommendations: string[] = [];

    // Analyze API response times
    if (metrics.api_response_time?.avg > 300) {
      recommendations.push('Consider implementing API response caching to reduce average response time');
    }

    // Analyze data processing times
    if (metrics.data_processing_time?.avg > 800) {
      recommendations.push('Optimize data processing algorithms or consider web workers for heavy computations');
    }

    // Analyze component render times
    Object.entries(componentStats).forEach(([componentName, stats]: [string, any]) => {
      if (stats.averageRenderTime > 100) {
        recommendations.push(`Consider optimizing ${componentName} component - average render time is ${stats.averageRenderTime.toFixed(2)}ms`);
      }
      if (stats.renderCount > 50) {
        recommendations.push(`${componentName} component is re-rendering frequently (${stats.renderCount} times) - check for unnecessary re-renders`);
      }
    });

    // Analyze memory usage
    if (memoryStats.current > 50 * 1024 * 1024) { // 50MB
      recommendations.push('High memory usage detected - consider implementing memory optimization strategies');
    }

    if (recommendations.length === 0) {
      recommendations.push('Performance looks good! No immediate optimizations needed.');
    }

    return recommendations;
  }

  private async saveReport(report: PerformanceReport): Promise<void> {
    // Save JSON report
    await fs.writeFile(
      path.join(this.outputDir, 'performance-report.json'),
      JSON.stringify(report, null, 2)
    );

    // Generate HTML report
    const htmlReport = this.generateHtmlReport(report);
    await fs.writeFile(
      path.join(this.outputDir, 'performance-report.html'),
      htmlReport
    );

    // Generate CSV for metrics
    const csvData = this.generateCsvReport(report);
    await fs.writeFile(
      path.join(this.outputDir, 'performance-metrics.csv'),
      csvData
    );
  }

  private generateHtmlReport(report: PerformanceReport): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <title>Performance Monitoring Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .metric { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
        .component-stat { background: #e8f5e9; padding: 15px; margin: 10px 0; border-radius: 6px; }
        .recommendation { background: #fff3cd; padding: 15px; margin: 10px 0; border-left: 4px solid #ffc107; border-radius: 4px; }
        .good { color: #28a745; }
        .warning { color: #ffc107; }
        .error { color: #dc3545; }
        .chart { width: 100%; height: 200px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6; }
        th { background-color: #f8f9fa; font-weight: 600; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Performance Monitoring Report</h1>
        <p><strong>Generated:</strong> ${report.timestamp}</p>
        
        <h2>📊 Performance Metrics</h2>
        ${Object.entries(report.metrics).map(([name, stats]) => `
            <div class="metric">
                <h3>${name.replace(/_/g, ' ').toUpperCase()}</h3>
                <table>
                    <tr><th>Average</th><td>${stats.avg.toFixed(2)}ms</td></tr>
                    <tr><th>Minimum</th><td>${stats.min.toFixed(2)}ms</td></tr>
                    <tr><th>Maximum</th><td>${stats.max.toFixed(2)}ms</td></tr>
                    <tr><th>Count</th><td>${stats.count}</td></tr>
                </table>
            </div>
        `).join('')}
        
        <h2>🧩 Component Performance</h2>
        ${Object.entries(report.componentStats).map(([name, stats]: [string, any]) => `
            <div class="component-stat">
                <h3>${name}</h3>
                <table>
                    <tr><th>Average Render Time</th><td>${stats.averageRenderTime?.toFixed(2) || 'N/A'}ms</td></tr>
                    <tr><th>Render Count</th><td>${stats.renderCount || 0}</td></tr>
                    <tr><th>Total Time</th><td>${stats.totalTime?.toFixed(2) || 'N/A'}ms</td></tr>
                </table>
            </div>
        `).join('')}
        
        <h2>💾 Memory Usage</h2>
        <div class="metric">
            <table>
                <tr><th>Current Usage</th><td>${this.formatBytes(report.memoryStats.current || 0)}</td></tr>
                <tr><th>Peak Usage</th><td>${this.formatBytes(report.memoryStats.peak || 0)}</td></tr>
                <tr><th>Average Usage</th><td>${this.formatBytes(report.memoryStats.average || 0)}</td></tr>
            </table>
        </div>
        
        <h2>💡 Recommendations</h2>
        ${report.recommendations.map(rec => `<div class="recommendation">${rec}</div>`).join('')}
        
        <p><em>Report generated by Data Center Performance Monitor</em></p>
    </div>
</body>
</html>
`;
  }

  private generateCsvReport(report: PerformanceReport): string {
    const headers = ['Metric', 'Average', 'Min', 'Max', 'Count'];
    const rows = Object.entries(report.metrics).map(([name, stats]) => [
      name,
      stats.avg.toFixed(2),
      stats.min.toFixed(2),
      stats.max.toFixed(2),
      stats.count.toString()
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  stopMonitoring(): void {
    this.isMonitoring = false;
    this.memoryMonitor.stopMonitoring();
  }
}

// CLI interface - only run in Node.js environment
if (typeof window === 'undefined' && typeof require !== 'undefined' && require.main === module) {
  const duration = process.argv[2] ? parseInt(process.argv[2]) * 1000 : 60000; // Default 60 seconds
  const reporter = new PerformanceReporter();
  
  console.log(`Starting performance monitoring for ${duration / 1000} seconds...`);
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Stopping performance monitoring...');
    reporter.stopMonitoring();
    process.exit(0);
  });
  
  reporter.startMonitoring(duration).catch(console.error);
}

export { PerformanceReporter };