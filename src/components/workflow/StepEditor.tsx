import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  Plus,
  Trash2,
  Save,
  Copy,
  Settings,
  Code,
  Terminal,
  Globe,
  File,
  GitBranch,
  Clock,
  RefreshCw,
  AlertCircle,
  ChevronRight,
  Zap,
  Bell,
  Repeat,
  Bot,
  Users,
  Vote,
  Workflow
} from 'lucide-react';
import { api, type Agent } from '@/lib/api';
import { WorkflowStep, StepType, StepConfig, Condition, RetryConfig } from '@/types/workflow';

interface StepEditorProps {
  step?: WorkflowStep;
  availableSteps: WorkflowStep[];
  availableVariables: string[];
  onSave: (step: WorkflowStep) => void;
  onCancel: () => void;
}

const STEP_TYPES = [
  { value: 'command', label: 'Command', icon: Terminal },
  { value: 'script', label: 'Script', icon: Code },
  { value: 'api_call', label: 'API Call', icon: Globe },
  { value: 'file_operation', label: 'File Operation', icon: File },
  { value: 'decision', label: 'Decision', icon: GitBranch },
  { value: 'loop', label: 'Loop', icon: Repeat },
  { value: 'parallel', label: 'Parallel', icon: Zap },
  { value: 'wait', label: 'Wait', icon: Clock },
  { value: 'transform', label: 'Transform', icon: RefreshCw },
  { value: 'notification', label: 'Notification', icon: Bell },
  // Agent step types
  { value: 'agent_execution', label: 'Agent Execution', icon: Bot },
  { value: 'agent_collaboration', label: 'Agent Collaboration', icon: Users },
  { value: 'agent_voting', label: 'Agent Voting', icon: Vote },
  { value: 'agent_parallel', label: 'Agent Parallel', icon: Workflow }
];

const CONDITION_OPERATORS = [
  { value: 'equals', label: 'Equals' },
  { value: 'not_equals', label: 'Not Equals' },
  { value: 'greater_than', label: 'Greater Than' },
  { value: 'less_than', label: 'Less Than' },
  { value: 'contains', label: 'Contains' },
  { value: 'not_contains', label: 'Not Contains' },
  { value: 'matches', label: 'Matches (Regex)' },
  { value: 'in', label: 'In' },
  { value: 'not_in', label: 'Not In' }
];

const ERROR_HANDLING_OPTIONS = [
  { value: 'fail', label: 'Fail Workflow' },
  { value: 'continue', label: 'Continue' },
  { value: 'retry', label: 'Retry' },
  { value: 'fallback', label: 'Fallback' }
];

export const StepEditor: React.FC<StepEditorProps> = ({
  step,
  availableSteps,
  availableVariables,
  onSave,
  onCancel
}) => {
  const [agents, setAgents] = useState<Agent[]>([]);
  
  // Load agents on mount
  useEffect(() => {
    api.listAgents().then(setAgents).catch(console.error);
  }, []);
  const [editedStep, setEditedStep] = useState<WorkflowStep>(
    step || {
      id: `step_${Date.now()}`,
      name: '',
      step_type: { type: 'command' },
      config: {
        input_mapping: {},
        output_mapping: {},
        error_handling: 'fail'
      }
    }
  );

  const [conditions, setConditions] = useState<Condition[]>(step?.conditions || []);
  const [inputMappings, setInputMappings] = useState<Array<{ key: string; value: string }>>(
    Object.entries(step?.config.input_mapping || {}).map(([key, value]) => ({ key, value }))
  );
  const [outputMappings, setOutputMappings] = useState<Array<{ key: string; value: string }>>(
    Object.entries(step?.config.output_mapping || {}).map(([key, value]) => ({ key, value }))
  );
  const [activeTab, setActiveTab] = useState('basic');

  // Update step type
  const updateStepType = (type: string) => {
    const newStepType: any = { type };
    
    // Set default properties based on type
    switch (type) {
      case 'command':
        newStepType.command = '';
        newStepType.args = [];
        break;
      case 'script':
        newStepType.language = 'bash';
        newStepType.code = '';
        break;
      case 'api_call':
        newStepType.method = 'GET';
        newStepType.url = '';
        newStepType.headers = {};
        break;
      case 'file_operation':
        newStepType.operation = { type: 'read', path: '' };
        break;
      case 'wait':
        newStepType.duration_ms = 1000;
        break;
      case 'notification':
        newStepType.channel = 'console';
        newStepType.template = '';
        break;
      case 'agent_execution':
        newStepType.agent_id = '';
        newStepType.task = '';
        newStepType.timeout_seconds = 300;
        break;
      case 'agent_collaboration':
        newStepType.collaboration_type = 'sequential';
        newStepType.participant_agents = [];
        newStepType.workflow_definition = {};
        break;
      case 'agent_voting':
        newStepType.topic = '';
        newStepType.voter_agents = [];
        newStepType.deadline_seconds = 3600;
        newStepType.voting_type = 'majority';
        break;
      case 'agent_parallel':
        newStepType.parallel_agents = [];
        newStepType.synchronization = 'wait_all';
        newStepType.max_concurrent = 5;
        break;
    }
    
    setEditedStep(prev => ({ ...prev, step_type: newStepType }));
  };

  // Add condition
  const addCondition = () => {
    setConditions([...conditions, { field: '', operator: 'equals', value: '' }]);
  };

  // Remove condition
  const removeCondition = (index: number) => {
    setConditions(conditions.filter((_, i) => i !== index));
  };

  // Update condition
  const updateCondition = (index: number, field: keyof Condition, value: any) => {
    setConditions(conditions.map((c, i) => i === index ? { ...c, [field]: value } : c));
  };

  // Add input mapping
  const addInputMapping = () => {
    setInputMappings([...inputMappings, { key: '', value: '' }]);
  };

  // Remove input mapping
  const removeInputMapping = (index: number) => {
    setInputMappings(inputMappings.filter((_, i) => i !== index));
  };

  // Update input mapping
  const updateInputMapping = (index: number, field: 'key' | 'value', value: string) => {
    setInputMappings(inputMappings.map((m, i) => i === index ? { ...m, [field]: value } : m));
  };

  // Add output mapping
  const addOutputMapping = () => {
    setOutputMappings([...outputMappings, { key: '', value: '' }]);
  };

  // Remove output mapping
  const removeOutputMapping = (index: number) => {
    setOutputMappings(outputMappings.filter((_, i) => i !== index));
  };

  // Update output mapping
  const updateOutputMapping = (index: number, field: 'key' | 'value', value: string) => {
    setOutputMappings(outputMappings.map((m, i) => i === index ? { ...m, [field]: value } : m));
  };

  // Save step
  const handleSave = () => {
    if (!editedStep.name) {
      return;
    }

    // Convert mappings to objects
    const inputMapping: Record<string, string> = {};
    inputMappings.forEach(m => {
      if (m.key && m.value) {
        inputMapping[m.key] = m.value;
      }
    });

    const outputMapping: Record<string, string> = {};
    outputMappings.forEach(m => {
      if (m.key && m.value) {
        outputMapping[m.key] = m.value;
      }
    });

    const finalStep: WorkflowStep = {
      ...editedStep,
      config: {
        ...editedStep.config,
        input_mapping: inputMapping,
        output_mapping: outputMapping
      },
      conditions: conditions.filter(c => c.field && c.operator)
    };

    onSave(finalStep);
  };

  // Render step type specific fields
  const renderStepTypeFields = () => {
    const { step_type } = editedStep;
    
    switch (step_type.type) {
      case 'command':
        return (
          <div className="space-y-4">
            <div>
              <Label>Command</Label>
              <Input
                value={step_type.command || ''}
                onChange={(e) => setEditedStep(prev => ({
                  ...prev,
                  step_type: { ...prev.step_type, command: e.target.value }
                }))}
                placeholder="e.g., npm, git, python"
              />
            </div>
            <div>
              <Label>Arguments</Label>
              <Textarea
                value={(step_type.args || []).join('\n')}
                onChange={(e) => setEditedStep(prev => ({
                  ...prev,
                  step_type: { ...prev.step_type, args: e.target.value.split('\n').filter(Boolean) }
                }))}
                placeholder="One argument per line"
                rows={3}
              />
            </div>
          </div>
        );
        
      case 'script':
        return (
          <div className="space-y-4">
            <div>
              <Label>Language</Label>
              <Select
                value={step_type.language || 'bash'}
                onValueChange={(value) => setEditedStep(prev => ({
                  ...prev,
                  step_type: { ...prev.step_type, language: value }
                }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="bash">Bash</SelectItem>
                  <SelectItem value="python">Python</SelectItem>
                  <SelectItem value="javascript">JavaScript</SelectItem>
                  <SelectItem value="powershell">PowerShell</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label>Code</Label>
              <Textarea
                value={step_type.code || ''}
                onChange={(e) => setEditedStep(prev => ({
                  ...prev,
                  step_type: { ...prev.step_type, code: e.target.value }
                }))}
                placeholder="Enter script code..."
                rows={10}
                className="font-mono text-sm"
              />
            </div>
          </div>
        );
        
      case 'api_call':
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Method</Label>
                <Select
                  value={step_type.method || 'GET'}
                  onValueChange={(value) => setEditedStep(prev => ({
                    ...prev,
                    step_type: { ...prev.step_type, method: value }
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="GET">GET</SelectItem>
                    <SelectItem value="POST">POST</SelectItem>
                    <SelectItem value="PUT">PUT</SelectItem>
                    <SelectItem value="PATCH">PATCH</SelectItem>
                    <SelectItem value="DELETE">DELETE</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>URL</Label>
                <Input
                  value={step_type.url || ''}
                  onChange={(e) => setEditedStep(prev => ({
                    ...prev,
                    step_type: { ...prev.step_type, url: e.target.value }
                  }))}
                  placeholder="https://api.example.com/endpoint"
                />
              </div>
            </div>
            <div>
              <Label>Headers (JSON)</Label>
              <Textarea
                value={JSON.stringify(step_type.headers || {}, null, 2)}
                onChange={(e) => {
                  try {
                    const headers = JSON.parse(e.target.value);
                    setEditedStep(prev => ({
                      ...prev,
                      step_type: { ...prev.step_type, headers }
                    }));
                  } catch {}
                }}
                placeholder='{"Authorization": "Bearer token"}'
                rows={4}
                className="font-mono text-sm"
              />
            </div>
          </div>
        );
        
      case 'file_operation':
        return (
          <div className="space-y-4">
            <div>
              <Label>Operation</Label>
              <Select
                value={step_type.operation?.type || 'read'}
                onValueChange={(value) => setEditedStep(prev => ({
                  ...prev,
                  step_type: { 
                    ...prev.step_type, 
                    operation: { ...prev.step_type.operation, type: value }
                  }
                }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="read">Read</SelectItem>
                  <SelectItem value="write">Write</SelectItem>
                  <SelectItem value="copy">Copy</SelectItem>
                  <SelectItem value="move">Move</SelectItem>
                  <SelectItem value="delete">Delete</SelectItem>
                  <SelectItem value="create_directory">Create Directory</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label>Path</Label>
              <Input
                value={step_type.operation?.path || ''}
                onChange={(e) => setEditedStep(prev => ({
                  ...prev,
                  step_type: { 
                    ...prev.step_type, 
                    operation: { ...prev.step_type.operation, path: e.target.value }
                  }
                }))}
                placeholder="/path/to/file"
              />
            </div>
            {(step_type.operation?.type === 'write') && (
              <div>
                <Label>Content</Label>
                <Textarea
                  value={step_type.operation?.content || ''}
                  onChange={(e) => setEditedStep(prev => ({
                    ...prev,
                    step_type: { 
                      ...prev.step_type, 
                      operation: { ...prev.step_type.operation, content: e.target.value }
                    }
                  }))}
                  placeholder="File content..."
                  rows={5}
                />
              </div>
            )}
          </div>
        );
        
      case 'wait':
        return (
          <div>
            <Label>Duration (milliseconds)</Label>
            <Input
              type="number"
              value={step_type.duration_ms || 1000}
              onChange={(e) => setEditedStep(prev => ({
                ...prev,
                step_type: { ...prev.step_type, duration_ms: parseInt(e.target.value) }
              }))}
              min={0}
            />
          </div>
        );
        
      case 'notification':
        return (
          <div className="space-y-4">
            <div>
              <Label>Channel</Label>
              <Select
                value={step_type.channel || 'console'}
                onValueChange={(value) => setEditedStep(prev => ({
                  ...prev,
                  step_type: { ...prev.step_type, channel: value }
                }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="console">Console</SelectItem>
                  <SelectItem value="log">Log</SelectItem>
                  <SelectItem value="email">Email</SelectItem>
                  <SelectItem value="slack">Slack</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label>Message Template</Label>
              <Textarea
                value={step_type.template || ''}
                onChange={(e) => setEditedStep(prev => ({
                  ...prev,
                  step_type: { ...prev.step_type, template: e.target.value }
                }))}
                placeholder="Workflow {{status}} - {{message}}"
                rows={3}
              />
            </div>
          </div>
        );

      case 'agent_execution':
        return (
          <div className="space-y-4">
            <div>
              <Label>Agent</Label>
              <Select
                value={step_type.agent_id || ''}
                onValueChange={(value) => setEditedStep(prev => ({
                  ...prev,
                  step_type: { ...prev.step_type, agent_id: value }
                }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select an agent" />
                </SelectTrigger>
                <SelectContent>
                  {agents.map(agent => (
                    <SelectItem key={agent.id} value={agent.id?.toString() || ''}>
                      {agent.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label>Task Description</Label>
              <Textarea
                value={step_type.task || ''}
                onChange={(e) => setEditedStep(prev => ({
                  ...prev,
                  step_type: { ...prev.step_type, task: e.target.value }
                }))}
                placeholder="Describe the task for the agent to perform..."
                rows={4}
              />
            </div>
            <div>
              <Label>Timeout (seconds)</Label>
              <Input
                type="number"
                value={step_type.timeout_seconds || 300}
                onChange={(e) => setEditedStep(prev => ({
                  ...prev,
                  step_type: { ...prev.step_type, timeout_seconds: parseInt(e.target.value) }
                }))}
                min={30}
                max={3600}
              />
            </div>
          </div>
        );

      case 'agent_collaboration':
        return (
          <div className="space-y-4">
            <div>
              <Label>Collaboration Type</Label>
              <Select
                value={step_type.collaboration_type || 'sequential'}
                onValueChange={(value) => setEditedStep(prev => ({
                  ...prev,
                  step_type: { ...prev.step_type, collaboration_type: value }
                }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sequential">Sequential</SelectItem>
                  <SelectItem value="parallel">Parallel</SelectItem>
                  <SelectItem value="pipeline">Pipeline</SelectItem>
                  <SelectItem value="mapreduce">MapReduce</SelectItem>
                  <SelectItem value="consensus">Consensus</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label>Participant Agents</Label>
              <div className="space-y-2">
                {(step_type.participant_agents || []).map((agentId: string, index: number) => (
                  <div key={index} className="flex items-center space-x-2">
                    <Select
                      value={agentId}
                      onValueChange={(value) => {
                        const newParticipants = [...(step_type.participant_agents || [])];
                        newParticipants[index] = value;
                        setEditedStep(prev => ({
                          ...prev,
                          step_type: { ...prev.step_type, participant_agents: newParticipants }
                        }));
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select agent" />
                      </SelectTrigger>
                      <SelectContent>
                        {agents.map(agent => (
                          <SelectItem key={agent.id} value={agent.id?.toString() || ''}>
                            {agent.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => {
                        const newParticipants = (step_type.participant_agents || []).filter((_: any, i: number) => i !== index);
                        setEditedStep(prev => ({
                          ...prev,
                          step_type: { ...prev.step_type, participant_agents: newParticipants }
                        }));
                      }}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    const newParticipants = [...(step_type.participant_agents || []), ''];
                    setEditedStep(prev => ({
                      ...prev,
                      step_type: { ...prev.step_type, participant_agents: newParticipants }
                    }));
                  }}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Agent
                </Button>
              </div>
            </div>
          </div>
        );

      case 'agent_voting':
        return (
          <div className="space-y-4">
            <div>
              <Label>Voting Topic</Label>
              <Input
                value={step_type.topic || ''}
                onChange={(e) => setEditedStep(prev => ({
                  ...prev,
                  step_type: { ...prev.step_type, topic: e.target.value }
                }))}
                placeholder="What decision needs to be voted on?"
              />
            </div>
            <div>
              <Label>Voting Type</Label>
              <Select
                value={step_type.voting_type || 'majority'}
                onValueChange={(value) => setEditedStep(prev => ({
                  ...prev,
                  step_type: { ...prev.step_type, voting_type: value }
                }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="majority">Majority</SelectItem>
                  <SelectItem value="unanimous">Unanimous</SelectItem>
                  <SelectItem value="supermajority">Super Majority (2/3)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label>Voter Agents</Label>
              <div className="space-y-2">
                {(step_type.voter_agents || []).map((agentId: string, index: number) => (
                  <div key={index} className="flex items-center space-x-2">
                    <Select
                      value={agentId}
                      onValueChange={(value) => {
                        const newVoters = [...(step_type.voter_agents || [])];
                        newVoters[index] = value;
                        setEditedStep(prev => ({
                          ...prev,
                          step_type: { ...prev.step_type, voter_agents: newVoters }
                        }));
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select voter agent" />
                      </SelectTrigger>
                      <SelectContent>
                        {agents.map(agent => (
                          <SelectItem key={agent.id} value={agent.id?.toString() || ''}>
                            {agent.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => {
                        const newVoters = (step_type.voter_agents || []).filter((_: any, i: number) => i !== index);
                        setEditedStep(prev => ({
                          ...prev,
                          step_type: { ...prev.step_type, voter_agents: newVoters }
                        }));
                      }}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    const newVoters = [...(step_type.voter_agents || []), ''];
                    setEditedStep(prev => ({
                      ...prev,
                      step_type: { ...prev.step_type, voter_agents: newVoters }
                    }));
                  }}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Voter
                </Button>
              </div>
            </div>
            <div>
              <Label>Deadline (seconds)</Label>
              <Input
                type="number"
                value={step_type.deadline_seconds || 3600}
                onChange={(e) => setEditedStep(prev => ({
                  ...prev,
                  step_type: { ...prev.step_type, deadline_seconds: parseInt(e.target.value) }
                }))}
                min={60}
                max={86400}
              />
            </div>
          </div>
        );

      case 'agent_parallel':
        return (
          <div className="space-y-4">
            <div>
              <Label>Parallel Agents</Label>
              <div className="space-y-2">
                {(step_type.parallel_agents || []).map((agentConfig: any, index: number) => (
                  <div key={index} className="p-3 border rounded-lg space-y-2">
                    <div className="flex items-center justify-between">
                      <Label className="text-sm font-medium">Agent {index + 1}</Label>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => {
                          const newAgents = (step_type.parallel_agents || []).filter((_: any, i: number) => i !== index);
                          setEditedStep(prev => ({
                            ...prev,
                            step_type: { ...prev.step_type, parallel_agents: newAgents }
                          }));
                        }}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                    <Select
                      value={agentConfig.agent_id || ''}
                      onValueChange={(value) => {
                        const newAgents = [...(step_type.parallel_agents || [])];
                        newAgents[index] = { ...newAgents[index], agent_id: value };
                        setEditedStep(prev => ({
                          ...prev,
                          step_type: { ...prev.step_type, parallel_agents: newAgents }
                        }));
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select agent" />
                      </SelectTrigger>
                      <SelectContent>
                        {agents.map(agent => (
                          <SelectItem key={agent.id} value={agent.id?.toString() || ''}>
                            {agent.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Textarea
                      value={agentConfig.task || ''}
                      onChange={(e) => {
                        const newAgents = [...(step_type.parallel_agents || [])];
                        newAgents[index] = { ...newAgents[index], task: e.target.value };
                        setEditedStep(prev => ({
                          ...prev,
                          step_type: { ...prev.step_type, parallel_agents: newAgents }
                        }));
                      }}
                      placeholder="Task for this agent..."
                      rows={2}
                    />
                  </div>
                ))}
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    const newAgents = [...(step_type.parallel_agents || []), { agent_id: '', task: '' }];
                    setEditedStep(prev => ({
                      ...prev,
                      step_type: { ...prev.step_type, parallel_agents: newAgents }
                    }));
                  }}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Parallel Agent
                </Button>
              </div>
            </div>
            <div>
              <Label>Synchronization</Label>
              <Select
                value={step_type.synchronization || 'wait_all'}
                onValueChange={(value) => setEditedStep(prev => ({
                  ...prev,
                  step_type: { ...prev.step_type, synchronization: value }
                }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="wait_all">Wait for All</SelectItem>
                  <SelectItem value="wait_any">Wait for Any</SelectItem>
                  <SelectItem value="wait_majority">Wait for Majority</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label>Max Concurrent</Label>
              <Input
                type="number"
                value={step_type.max_concurrent || 5}
                onChange={(e) => setEditedStep(prev => ({
                  ...prev,
                  step_type: { ...prev.step_type, max_concurrent: parseInt(e.target.value) }
                }))}
                min={1}
                max={20}
              />
            </div>
          </div>
        );
        
      default:
        return (
          <div className="text-muted-foreground">
            Configuration for this step type is not yet implemented
          </div>
        );
    }
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{step ? 'Edit Step' : 'New Step'}</CardTitle>
          <div className="flex items-center space-x-2">
            <Button size="sm" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button size="sm" onClick={handleSave}>
              <Save className="w-4 h-4 mr-1" />
              Save
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="overflow-y-auto">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic">Basic</TabsTrigger>
            <TabsTrigger value="config">Configuration</TabsTrigger>
            <TabsTrigger value="conditions">Conditions</TabsTrigger>
            <TabsTrigger value="advanced">Advanced</TabsTrigger>
          </TabsList>
          
          <TabsContent value="basic" className="space-y-4">
            <div>
              <Label>Step Name</Label>
              <Input
                value={editedStep.name}
                onChange={(e) => setEditedStep(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter step name"
              />
            </div>
            
            <div>
              <Label>Step Type</Label>
              <Select
                value={editedStep.step_type.type}
                onValueChange={updateStepType}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {STEP_TYPES.map(type => {
                    const Icon = type.icon;
                    return (
                      <SelectItem key={type.value} value={type.value}>
                        <div className="flex items-center">
                          <Icon className="w-4 h-4 mr-2" />
                          {type.label}
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>
            
            <div className="border-t pt-4">
              {renderStepTypeFields()}
            </div>
          </TabsContent>
          
          <TabsContent value="config" className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label>Input Mappings</Label>
                <Button size="sm" variant="outline" onClick={addInputMapping}>
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
              <div className="space-y-2">
                {inputMappings.map((mapping, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <Input
                      placeholder="Input key"
                      value={mapping.key}
                      onChange={(e) => updateInputMapping(index, 'key', e.target.value)}
                    />
                    <ChevronRight className="w-4 h-4" />
                    <Select
                      value={mapping.value}
                      onValueChange={(value) => updateInputMapping(index, 'value', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select variable" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableVariables.map(v => (
                          <SelectItem key={v} value={v}>{v}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => removeInputMapping(index)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label>Output Mappings</Label>
                <Button size="sm" variant="outline" onClick={addOutputMapping}>
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
              <div className="space-y-2">
                {outputMappings.map((mapping, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <Input
                      placeholder="Output key"
                      value={mapping.key}
                      onChange={(e) => updateOutputMapping(index, 'key', e.target.value)}
                    />
                    <ChevronRight className="w-4 h-4" />
                    <Input
                      placeholder="Variable name"
                      value={mapping.value}
                      onChange={(e) => updateOutputMapping(index, 'value', e.target.value)}
                    />
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => removeOutputMapping(index)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
            
            <div>
              <Label>Error Handling</Label>
              <Select
                value={editedStep.config.error_handling}
                onValueChange={(value: any) => setEditedStep(prev => ({
                  ...prev,
                  config: { ...prev.config, error_handling: value }
                }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {ERROR_HANDLING_OPTIONS.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </TabsContent>
          
          <TabsContent value="conditions" className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label>Execution Conditions</Label>
                <Button size="sm" variant="outline" onClick={addCondition}>
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
              <div className="space-y-2">
                {conditions.map((condition, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <Select
                      value={condition.field}
                      onValueChange={(value) => updateCondition(index, 'field', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Variable" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableVariables.map(v => (
                          <SelectItem key={v} value={v}>{v}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Select
                      value={condition.operator}
                      onValueChange={(value) => updateCondition(index, 'operator', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {CONDITION_OPERATORS.map(op => (
                          <SelectItem key={op.value} value={op.value}>
                            {op.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Input
                      placeholder="Value"
                      value={condition.value}
                      onChange={(e) => updateCondition(index, 'value', e.target.value)}
                    />
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => removeCondition(index)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="advanced" className="space-y-4">
            <div>
              <Label>Timeout (ms)</Label>
              <Input
                type="number"
                value={editedStep.timeout_ms || ''}
                onChange={(e) => setEditedStep(prev => ({
                  ...prev,
                  timeout_ms: e.target.value ? parseInt(e.target.value) : undefined
                }))}
                placeholder="No timeout"
                min={0}
              />
            </div>
            
            <div>
              <Label>Retry Configuration</Label>
              <div className="space-y-2 mt-2">
                <div className="flex items-center space-x-2">
                  <Label className="w-32">Max Attempts</Label>
                  <Input
                    type="number"
                    value={editedStep.retry_config?.max_attempts || ''}
                    onChange={(e) => setEditedStep(prev => ({
                      ...prev,
                      retry_config: {
                        ...prev.retry_config,
                        max_attempts: parseInt(e.target.value) || 1,
                        delay_ms: prev.retry_config?.delay_ms || 1000,
                        backoff_multiplier: prev.retry_config?.backoff_multiplier || 2
                      }
                    }))}
                    min={1}
                    max={10}
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <Label className="w-32">Delay (ms)</Label>
                  <Input
                    type="number"
                    value={editedStep.retry_config?.delay_ms || ''}
                    onChange={(e) => setEditedStep(prev => ({
                      ...prev,
                      retry_config: prev.retry_config ? {
                        ...prev.retry_config,
                        delay_ms: parseInt(e.target.value) || 1000
                      } : undefined
                    }))}
                    min={0}
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <Label className="w-32">Backoff</Label>
                  <Input
                    type="number"
                    step="0.1"
                    value={editedStep.retry_config?.backoff_multiplier || ''}
                    onChange={(e) => setEditedStep(prev => ({
                      ...prev,
                      retry_config: prev.retry_config ? {
                        ...prev.retry_config,
                        backoff_multiplier: parseFloat(e.target.value) || 2
                      } : undefined
                    }))}
                    min={1}
                    max={5}
                  />
                </div>
              </div>
            </div>
            
            <div>
              <Label>On Success</Label>
              <Select
                value={editedStep.on_success?.[0] || ''}
                onValueChange={(value) => setEditedStep(prev => ({
                  ...prev,
                  on_success: value ? [value] : undefined
                }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Next step" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Default (next step)</SelectItem>
                  {availableSteps
                    .filter(s => s.id !== editedStep.id)
                    .map(s => (
                      <SelectItem key={s.id} value={s.id}>{s.name}</SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label>On Failure</Label>
              <Select
                value={editedStep.on_failure?.[0] || ''}
                onValueChange={(value) => setEditedStep(prev => ({
                  ...prev,
                  on_failure: value ? [value] : undefined
                }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Error handler" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Default (fail workflow)</SelectItem>
                  {availableSteps
                    .filter(s => s.id !== editedStep.id)
                    .map(s => (
                      <SelectItem key={s.id} value={s.id}>{s.name}</SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};