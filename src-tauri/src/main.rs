// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

mod claude_binary;
mod agents;
mod usage_tracker;
mod training;
mod context_watcher;
mod mcp;
mod workflow;
mod superclaude;
mod budget_templates;
mod budget_db;
mod budget_commands;
// mod mermaid; // Removed - module deleted
mod beacon_ai_service;
mod beacon_document_processor;
// mod backend; // Disabled - duplicate commands moved to data_center
mod data_center;
mod graduate_trainee_service;
mod graduate_trainee_db;
mod email_service;
mod notification_scheduler;
mod auth;
mod commands {
    pub mod slidev;
    // pub mod mermaid_commands; // Removed - module deleted
    pub mod auth;
    pub mod migration;
}

use sanity_lib::checkpoint::state::CheckpointState;
use sanity_lib::process::ProcessRegistryState;
use std::sync::{Arc, Mutex};
use tokio::sync::RwLock;
use tauri::Manager;
use context_watcher::ContextWatcherState;

// Import commands directly from their modules

// Import all other commands from the crate root (re-exported in lib.rs)
use sanity_lib::{
    // Agent commands
    init_database, AgentDb, ClaudeProcessState, apply_proxy_settings, ProxySettings,
    
    // State types
    ModelSwitcherState, SummarizerState, ErrorRecoveryState, TrainingState, initialize_enhanced_mcp, initialize_workflow,
};

// Import non-command types from specific modules
use sanity_lib::commands::conversation_summary::ConversationSummarizer;
use sanity_lib::commands::error_recovery::ErrorRecoverySystem;
use sanity_lib::commands::model_switcher::SmartModelSwitcher;

fn main() {
    // Initialize logger
    env_logger::init();


    tauri::Builder::default()
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_fs::init())
        .setup(|app| {
            // Initialize agents database
            let conn = init_database(&app.handle()).expect("Failed to initialize agents database");
            
            // Initialize training budget DB
            let app_dir = app
                .path()
                .app_data_dir()
                .map_err(|e| anyhow::anyhow!(e))
                .expect("Failed to resolve app data dir");
            sanity_lib::db::init_db(app_dir.clone()).expect("Failed to init training budget DB");
            
            // Initialize insights database
            sanity_lib::commands::insights::init_insights_db(app_dir.clone())
                .expect("Failed to init insights DB");
            
            // Mermaid database initialization removed - module deleted
            
            // Initialize Graduate Trainee database tables
            if let Ok(conn) = sanity_lib::db::get_connection() {
                graduate_trainee_db::init_graduate_trainee_db(&conn)
                    .expect("Failed to init Graduate Trainee tables");
            }
            
            // Initialize budget templates database
            let budget_db_path = app_dir.join("budget_templates.db");
            let budget_db = budget_db::BudgetDatabase::new(&budget_db_path)
                .expect("Failed to initialize budget templates database");
            app.manage(budget_commands::BudgetState {
                db: Mutex::new(budget_db),
            });
            
            // Load and apply proxy settings from the database
            {
                let db = AgentDb(Mutex::new(conn));
                let proxy_settings = match db.0.lock() {
                    Ok(conn) => {
                        // Directly query proxy settings from the database
                        let mut settings = ProxySettings::default();
                        
                        let keys = vec![
                            ("proxy_enabled", "enabled"),
                            ("proxy_http", "http_proxy"),
                            ("proxy_https", "https_proxy"),
                            ("proxy_no", "no_proxy"),
                            ("proxy_all", "all_proxy"),
                        ];
                        
                        for (db_key, field) in keys {
                            if let Ok(value) = conn.query_row(
                                "SELECT value FROM app_settings WHERE key = ?1",
                                rusqlite::params![db_key],
                                |row| row.get::<_, String>(0),
                            ) {
                                match field {
                                    "enabled" => settings.enabled = value == "true",
                                    "http_proxy" => settings.http_proxy = Some(value).filter(|s: &String| !s.is_empty()),
                                    "https_proxy" => settings.https_proxy = Some(value).filter(|s: &String| !s.is_empty()),
                                    "no_proxy" => settings.no_proxy = Some(value).filter(|s: &String| !s.is_empty()),
                                    "all_proxy" => settings.all_proxy = Some(value).filter(|s: &String| !s.is_empty()),
                                    _ => {}
                                }
                            }
                        }
                        
                        log::info!("Loaded proxy settings: enabled={}", settings.enabled);
                        settings
                    }
                    Err(e) => {
                        log::warn!("Failed to lock database for proxy settings: {}", e);
                        ProxySettings::default()
                    }
                };
                
                // Apply the proxy settings
                apply_proxy_settings(&proxy_settings);
            }
            
            // Re-open the connection for the app to manage
            let conn = init_database(&app.handle()).expect("Failed to initialize agents database");
            app.manage(AgentDb(Mutex::new(conn)));
            
            // Initialize Authentication state
            let auth_state = sanity_lib::commands::auth::AuthState::new();
            app.manage(auth_state);
            
            // Initialize Claude Code API state
            let claude_code_api_state = tauri::async_runtime::block_on(
                sanity_lib::commands::claude_code_api::init_claude_code_api_state()
            );
            app.manage(claude_code_api_state);

            // Initialize checkpoint state
            let checkpoint_state = CheckpointState::new();
            
            // Initialize DataCenter
            let data_center_config = data_center::DataCenterConfig {
                max_threads: 4,
                chunk_size: 1024,
                enable_simd: true,
                python_path: "python3".to_string(),
                cache_size: 100,
            };
            let data_center = std::sync::Arc::new(data_center::DataCenter::new(data_center_config));
            app.manage(data_center);

            // Set the Claude directory path
            if let Ok(claude_dir) = dirs::home_dir()
                .ok_or_else(|| "Could not find home directory")
                .and_then(|home| {
                    let claude_path = home.join(".claude");
                    claude_path
                        .canonicalize()
                        .map_err(|_| "Could not find ~/.claude directory")
                })
            {
                let state_clone = checkpoint_state.clone();
                tauri::async_runtime::spawn(async move {
                    state_clone.set_claude_dir(claude_dir).await;
                });
            }

            app.manage(checkpoint_state.clone());
            
            // Initialize Agent System
            let agent_system = Arc::new(agents::AgentSystem::new(app_dir.clone()));
            
            // Initialize the agent system asynchronously
            {
                let agent_system_clone = agent_system.clone();
                tauri::async_runtime::spawn(async move {
                    if let Err(e) = agent_system_clone.initialize().await {
                        log::error!("Failed to initialize agent system: {}", e);
                    } else {
                        log::info!("Agent system initialized successfully");
                    }
                });
            }
            
            // Store agent system for use by other components
            app.manage(agent_system.clone());

            // Initialize process registry
            app.manage(ProcessRegistryState::default());

            // Initialize Claude process state
            app.manage(ClaudeProcessState::default());
            
            // Initialize Task Master state
            // app.manage(TaskMasterState::default()); // TaskMasterState removed with mock data
            
            // Initialize Model Switcher state
            app.manage(Arc::new(RwLock::new(SmartModelSwitcher::default())) as ModelSwitcherState);
            
            // Initialize Conversation Summary state  
            app.manage(Arc::new(RwLock::new(ConversationSummarizer::default())) as SummarizerState);
            
            // Initialize Error Recovery state
            app.manage(Arc::new(RwLock::new(ErrorRecoverySystem::default())) as ErrorRecoveryState);

            // Initialize Training state
            app.manage(TrainingState::new());
            
            // Initialize Health Monitor state - for now keep as None
            app.manage(Arc::new(RwLock::new(None)) as sanity_lib::commands::health_monitor_commands::HealthMonitorState);
            
            // Initialize Workflow Executor state - for now keep as None
            app.manage(Arc::new(RwLock::new(None)) as sanity_lib::commands::workflow_executor_commands::WorkflowExecutorState);
            
            // Initialize Git Hook Manager state - for now keep as None
            app.manage(Arc::new(RwLock::new(None)) as sanity_lib::commands::git_hook_commands::GitHookManagerState);
            
            // Initialize Agent Orchestrator state - for now keep as None
            app.manage(Arc::new(RwLock::new(None)) as sanity_lib::commands::agent_orchestration_commands::AgentOrchestratorState);
            
            // Initialize Agent Workflow state - for now keep as None
            app.manage(Arc::new(RwLock::new(None)) as sanity_lib::commands::agent_workflow_commands::AgentWorkflowState);

            // Initialize Context Watcher state
            let context_watcher_state = ContextWatcherState::new();
            context_watcher_state.initialize(app.handle().clone());
            app.manage(context_watcher_state);

            // Initialize Enhanced MCP state with a runtime
            let enhanced_mcp_state = {
                let runtime = tokio::runtime::Runtime::new().expect("Failed to create runtime");
                runtime.block_on(initialize_enhanced_mcp(&app.handle()))
                    .expect("Failed to initialize Enhanced MCP")
            };
            app.manage(enhanced_mcp_state);

            // Initialize Workflow state with a runtime
            let workflow_state = {
                let runtime = tokio::runtime::Runtime::new().expect("Failed to create runtime");
                runtime.block_on(initialize_workflow(&app.handle()))
                    .expect("Failed to initialize Workflow")
            };
            app.manage(workflow_state);

            // Initialize SuperClaude state with a runtime
            let superclaude_state = {
                let runtime = tokio::runtime::Runtime::new().expect("Failed to create runtime");
                runtime.block_on(sanity_lib::commands::superclaude::initialize_superclaude(&app.handle()))
                    .expect("Failed to initialize SuperClaude")
            };
            app.manage(superclaude_state);
            
            // Initialize Content Creators state
            let content_creator_service = sanity_lib::content_creators::ContentCreatorService::new()
                .expect("Failed to initialize Content Creator Service");
            let content_state = Arc::new(RwLock::new(content_creator_service));
            
            // Initialize analytics in background after runtime is available
            {
                let content_state_clone = content_state.clone();
                tauri::async_runtime::spawn(async move {
                    let service = content_state_clone.read().await;
                    if let Err(e) = service.initialize_analytics().await {
                        log::error!("Failed to initialize content creator analytics: {}", e);
                    }
                });
            }
            
            app.manage(sanity_lib::commands::content_creators::ContentCreatorState(content_state));
            
            // Initialize Document Processing state
            // Temporarily disabled to fix compilation
            // let document_processor_state = sanity_lib::commands::document_processing::DocumentProcessorState::new();
            // app.manage(document_processor_state);

            // Initialize Graduate Trainee sample data
            tauri::async_runtime::spawn(async move {
                if let Err(e) = crate::training::graduate_trainee_service::GraduateTraineeService::initialize_sample_data().await {
                    log::warn!("Failed to initialize graduate trainee sample data: {}", e);
                } else {
                    log::info!("Graduate trainee sample data initialized successfully");
                }
            });

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            // Claude & Project Management
            sanity_lib::commands::claude::list_projects,
            
            // Training Budget
            sanity_lib::commands::budget::get_budget,
            sanity_lib::commands::budget::set_budget,
            sanity_lib::commands::budget::get_stats,
            sanity_lib::commands::budget::list_requests,
            sanity_lib::commands::budget::create_request,
            sanity_lib::commands::budget::update_request,
            sanity_lib::commands::budget::delete_request,
            sanity_lib::commands::budget::approve_request,
            sanity_lib::commands::budget::reject_request,
            // Expenses (database-backed)
            sanity_lib::commands::expenses::get_expenses,
            sanity_lib::commands::expenses::add_expense,
            sanity_lib::commands::expenses::update_expense,
            sanity_lib::commands::expenses::delete_expense,
            sanity_lib::commands::expenses::update_expense_status,
            // Basic Training Commands (keeping existing)
            sanity_lib::commands::training::list_training_needs, 
            sanity_lib::commands::training::create_training_need, 
            sanity_lib::commands::training::update_training_need, 
            sanity_lib::commands::training::delete_training_need,
            sanity_lib::commands::training::list_training_programs, 
            sanity_lib::commands::training::create_training_program, 
            sanity_lib::commands::training::update_training_program, 
            sanity_lib::commands::training::delete_training_program,
            sanity_lib::commands::training::list_training_analyses, 
            sanity_lib::commands::training::create_training_analysis, 
            sanity_lib::commands::training::update_training_analysis, 
            sanity_lib::commands::training::delete_training_analysis,
            
            // Enhanced Training Commands
            sanity_lib::commands::training_enhanced::create_training_need_enhanced,
            sanity_lib::commands::training_enhanced::get_training_needs_enhanced,
            sanity_lib::commands::training_enhanced::update_training_need_enhanced,
            sanity_lib::commands::training_enhanced::delete_training_need_enhanced,
            sanity_lib::commands::training_enhanced::analyze_skills_gap,
            sanity_lib::commands::training_enhanced::get_skill_recommendations,
            sanity_lib::commands::training_enhanced::create_training_program_enhanced,
            sanity_lib::commands::training_enhanced::get_training_programs_enhanced,
            sanity_lib::commands::training_enhanced::match_programs_to_needs,
            sanity_lib::commands::training_enhanced::update_module_progress,
            sanity_lib::commands::training_enhanced::create_approval_workflow,
            sanity_lib::commands::training_enhanced::process_approval,
            sanity_lib::commands::training_enhanced::get_pending_approvals,
            sanity_lib::commands::training_enhanced::get_training_metrics,
            sanity_lib::commands::training_enhanced::get_department_metrics,
            sanity_lib::commands::training_enhanced::get_roi_analysis,
            sanity_lib::commands::training_enhanced::import_training_needs_csv,
            sanity_lib::commands::training_enhanced::export_training_needs_csv,
            sanity_lib::commands::training_enhanced::generate_csv_template,
            sanity_lib::commands::training_enhanced::batch_create_training_needs,
            sanity_lib::commands::training_enhanced::batch_update_status,
            sanity_lib::commands::training_enhanced::search_training_needs,
            sanity_lib::commands::training_enhanced::get_training_suggestions,
            sanity_lib::commands::training_enhanced::send_training_reminder,
            sanity_lib::commands::training_enhanced::schedule_training_notifications,
            // Content Creators commands
            sanity_lib::commands::content_creators::create_course,
            sanity_lib::commands::content_creators::get_course,
            sanity_lib::commands::content_creators::update_course,
            sanity_lib::commands::content_creators::publish_course,
            sanity_lib::commands::content_creators::get_courses,
            sanity_lib::commands::content_creators::create_module,
            sanity_lib::commands::content_creators::update_module,
            sanity_lib::commands::content_creators::create_lesson,
            sanity_lib::commands::content_creators::update_lesson,
            sanity_lib::commands::content_creators::create_assessment,
            sanity_lib::commands::content_creators::update_assessment,
            sanity_lib::commands::content_creators::upload_media,
            sanity_lib::commands::content_creators::get_media_library,
            sanity_lib::commands::content_creators::create_content_template,
            sanity_lib::commands::content_creators::get_content_templates,
            sanity_lib::commands::content_creators::submit_content_review,
            sanity_lib::commands::content_creators::get_content_reviews,
            sanity_lib::commands::content_creators::get_content_analytics,
            sanity_lib::commands::content_creators::generate_scorm_package,
            sanity_lib::commands::content_creators::create_content_version,
            sanity_lib::commands::content_creators::get_content_versions,
            sanity_lib::commands::content_creators::get_collaboration_ws_url,
            sanity_lib::commands::content_creators::invite_collaborator,
            sanity_lib::commands::content_creators::get_ai_suggestions,
            sanity_lib::commands::content_creators::generate_ai_suggestions,
            sanity_lib::commands::content_creators::apply_ai_suggestion,
            sanity_lib::commands::content_creators::get_content_localizations,
            sanity_lib::commands::content_creators::create_localization,
            sanity_lib::commands::content_creators::export_content,
            sanity_lib::commands::content_creators::import_content,
            sanity_lib::commands::content_creators::get_learning_paths,
            sanity_lib::commands::content_creators::create_learning_path,
            sanity_lib::commands::content_creators::get_learning_progress,
            sanity_lib::commands::content_creators::update_learning_progress,
            sanity_lib::commands::claude::get_project_sessions,
            sanity_lib::commands::claude::get_claude_settings,
            sanity_lib::commands::claude::open_new_session,
            sanity_lib::commands::claude::get_system_prompt,
            sanity_lib::commands::claude::check_claude_version,
            sanity_lib::commands::claude::save_system_prompt,
            sanity_lib::commands::claude::save_claude_settings,
            sanity_lib::commands::claude::find_claude_md_files,
            sanity_lib::commands::claude::read_claude_md_file,
            sanity_lib::commands::claude::save_claude_md_file,
            sanity_lib::commands::claude::load_session_history,
            sanity_lib::commands::claude::execute_claude_code,
            sanity_lib::commands::claude::continue_claude_code,
            sanity_lib::commands::claude::resume_claude_code,
            sanity_lib::commands::claude::cancel_claude_execution,
            sanity_lib::commands::claude::list_running_claude_sessions,
            sanity_lib::commands::claude::get_claude_session_output,
            sanity_lib::commands::claude::list_directory_contents,
            sanity_lib::commands::claude::search_files,
            sanity_lib::commands::claude::get_recently_modified_files,
            sanity_lib::commands::claude::get_hooks_config,
            sanity_lib::commands::claude::update_hooks_config,
            sanity_lib::commands::claude::validate_hook_command,
            
            // Context System Commands
            sanity_lib::commands::claude::discover_claude_contexts,
            sanity_lib::commands::claude::get_context_templates,
            sanity_lib::commands::claude::load_project_context,
            sanity_lib::commands::claude::create_context_from_template,
            sanity_lib::commands::claude::get_context_history,
            sanity_lib::commands::claude::update_context_inheritance,
            sanity_lib::commands::claude::initialize_context_templates,
            
            // Checkpoint Management
            sanity_lib::commands::claude::create_checkpoint,
            sanity_lib::commands::claude::restore_checkpoint,
            sanity_lib::commands::claude::list_checkpoints,
            sanity_lib::commands::claude::fork_from_checkpoint,
            sanity_lib::commands::claude::get_session_timeline,
            sanity_lib::commands::claude::update_checkpoint_settings,
            sanity_lib::commands::claude::get_checkpoint_diff,
            sanity_lib::commands::claude::track_checkpoint_message,
            sanity_lib::commands::claude::track_session_messages,
            sanity_lib::commands::claude::check_auto_checkpoint,
            sanity_lib::commands::claude::get_checkpoint_manager_info,
            sanity_lib::commands::claude::cleanup_old_checkpoints,
            sanity_lib::commands::claude::get_checkpoint_settings,
            sanity_lib::commands::claude::clear_checkpoint_manager,
            sanity_lib::commands::claude::get_checkpoint_state_stats,
            sanity_lib::commands::claude::delete_checkpoint,
            sanity_lib::commands::claude::send_to_background,
            sanity_lib::commands::claude::bring_to_foreground,
            sanity_lib::commands::claude::approve_plan_execution,
            sanity_lib::commands::claude::reject_plan_execution,
            
            // Agent Management
            sanity_lib::commands::agents::list_agents,
            sanity_lib::commands::agents::create_agent,
            sanity_lib::commands::agents::update_agent,
            sanity_lib::commands::agents::delete_agent,
            sanity_lib::commands::agents::get_agent,
            sanity_lib::commands::agents::execute_agent,
            sanity_lib::commands::agents::load_cc_agents,
            sanity_lib::commands::agents::import_cc_agent,
            sanity_lib::commands::agents::import_all_cc_agents,
            sanity_lib::commands::agents::list_agent_runs,
            sanity_lib::commands::agents::get_agent_run,
            sanity_lib::commands::agents::list_agent_runs_with_metrics,
            sanity_lib::commands::agents::get_agent_run_with_real_time_metrics,
            sanity_lib::commands::agents::list_running_sessions,
            sanity_lib::commands::agents::kill_agent_session,
            sanity_lib::commands::agents::get_session_status,
            sanity_lib::commands::agents::cleanup_finished_processes,
            sanity_lib::commands::agents::get_session_output,
            sanity_lib::commands::agents::get_live_session_output,
            sanity_lib::commands::agents::stream_session_output,
            sanity_lib::commands::agents::load_agent_session_history,
            sanity_lib::commands::agents::get_claude_binary_path,
            sanity_lib::commands::agents::set_claude_binary_path,
            sanity_lib::commands::agents::list_claude_installations,
            sanity_lib::commands::agents::list_processes,
            sanity_lib::commands::agents::export_agent,
            sanity_lib::commands::agents::export_agent_to_file,
            sanity_lib::commands::agents::import_agent,
            sanity_lib::commands::agents::import_agent_from_file,
            sanity_lib::commands::agents::fetch_github_agents,
            sanity_lib::commands::agents::execute_agent_in_session,
            sanity_lib::commands::agents::detect_agent_opportunity,
            sanity_lib::commands::agents::fetch_github_agent_content,
            sanity_lib::commands::agents::import_agent_from_github,
            
            // Usage & Analytics
            sanity_lib::commands::usage::get_usage_stats,
            sanity_lib::commands::usage::get_usage_by_date_range,
            sanity_lib::commands::usage::get_usage_details,
            sanity_lib::commands::usage::get_session_stats,
            sanity_lib::commands::usage::get_model_usage_info,
            sanity_lib::commands::usage::update_model_usage_settings,
            sanity_lib::commands::usage::reset_model_usage,
            
            // Analytics & ML Insights
            sanity_lib::commands::analytics::get_agent_performance_metrics,
            sanity_lib::commands::analytics::get_session_analytics,
            sanity_lib::commands::analytics::get_workflow_analytics,
            sanity_lib::commands::analytics::detect_usage_patterns,
            sanity_lib::commands::analytics::generate_ml_insights,
            sanity_lib::commands::analytics::get_agent_recommendations,
            sanity_lib::commands::analytics::get_cost_analytics,
            sanity_lib::commands::analytics::get_performance_trend,
            sanity_lib::commands::analytics::get_real_time_metrics,
            sanity_lib::commands::analytics::detect_anomalies,
            sanity_lib::commands::analytics::get_benchmark_comparison,
            sanity_lib::commands::analytics::export_analytics,
            sanity_lib::commands::analytics::get_analytics_config,
            sanity_lib::commands::analytics::update_analytics_config,
            sanity_lib::commands::analytics::initialize_ml_models,
            sanity_lib::commands::analytics::get_agent_performance_history,
            sanity_lib::commands::analytics::get_cost_optimization_opportunities,
            sanity_lib::commands::analytics::get_predictive_analytics,
            sanity_lib::commands::usage::get_model_to_use,
            
            // Advanced Workflow System
            sanity_lib::commands::workflows::save_workflow,
            sanity_lib::commands::workflows::load_workflow,
            sanity_lib::commands::workflows::list_advanced_workflows,
            sanity_lib::commands::workflows::execute_advanced_workflow,
            sanity_lib::commands::workflows::get_advanced_workflow_analytics,
            sanity_lib::commands::workflows::get_optimization_suggestions,
            sanity_lib::commands::workflows::apply_optimization,
            sanity_lib::commands::workflows::export_workflow,
            sanity_lib::commands::workflows::import_workflow,
            sanity_lib::commands::workflows::share_workflow,
            sanity_lib::commands::workflows::execute_workflow_agent,
            sanity_lib::commands::workflows::execute_script,
            sanity_lib::commands::workflows::evaluate_ai_condition,
            sanity_lib::commands::workflows::apply_transformation,
            sanity_lib::commands::workflows::call_integration,
            sanity_lib::commands::workflows::make_ai_decision,
            sanity_lib::commands::workflows::get_cache,
            sanity_lib::commands::workflows::set_cache,
            sanity_lib::commands::workflows::execute_fallback,
            sanity_lib::commands::workflows::record_workflow_metrics,
            sanity_lib::commands::workflows::save_optimization_suggestions,
            sanity_lib::commands::workflows::run_compensation,
            sanity_lib::commands::workflows::activate_circuit_breaker,
            sanity_lib::commands::workflows::execute_parallel_task,
            sanity_lib::commands::workflows::apply_transform,
            sanity_lib::commands::workflows::generate_optimization_suggestions,
            sanity_lib::commands::workflows::record_execution_metrics,
            
            // MCP (Model Context Protocol)
            sanity_lib::commands::mcp::mcp_add,
            sanity_lib::commands::mcp::mcp_list,
            sanity_lib::commands::mcp::mcp_get,
            sanity_lib::commands::mcp::mcp_remove,
            sanity_lib::commands::mcp::mcp_add_json,
            sanity_lib::commands::mcp::mcp_add_from_claude_desktop,
            sanity_lib::commands::mcp::mcp_serve,
            sanity_lib::commands::mcp::mcp_test_connection,
            sanity_lib::commands::mcp::mcp_reset_project_choices,
            sanity_lib::commands::mcp::mcp_get_server_status,
            sanity_lib::commands::mcp::mcp_read_project_config,
            sanity_lib::commands::mcp::mcp_save_project_config,
            // MCP Tools and Resources
            sanity_lib::commands::mcp_tools::mcp_connect,
            sanity_lib::commands::mcp_tools::mcp_disconnect,
            sanity_lib::commands::mcp_tools::mcp_execute_tool,
            sanity_lib::commands::mcp_tools::mcp_fetch_resource,
            sanity_lib::commands::mcp_tools::mcp_ping,
            sanity_lib::commands::mcp_tools::get_mcp_favorites,
            sanity_lib::commands::mcp_tools::get_mcp_metrics,
            sanity_lib::commands::mcp_tools::mcp_get_tools,
            sanity_lib::commands::mcp_tools::mcp_get_resources,
            
            // Workflow Analytics
            sanity_lib::commands::workflow_analytics::get_workflow_session_analytics,
            sanity_lib::commands::workflow_analytics::get_workflow_agent_metrics,
            sanity_lib::commands::workflow_analytics::get_workflow_session_metrics,
            
            // Storage Management
            sanity_lib::commands::storage::storage_list_tables,
            sanity_lib::commands::storage::storage_read_table,
            sanity_lib::commands::storage::storage_update_row,
            sanity_lib::commands::storage::storage_delete_row,
            sanity_lib::commands::storage::storage_insert_row,
            sanity_lib::commands::storage::storage_execute_sql,
            sanity_lib::commands::storage::storage_reset_database,
            
            // Slash Commands
            sanity_lib::commands::slash_commands::slash_commands_list,
            sanity_lib::commands::slash_commands::slash_command_get,
            sanity_lib::commands::slash_commands::slash_command_save,
            sanity_lib::commands::slash_commands::slash_command_delete,
            
            // Enhanced Slash Commands
            sanity_lib::commands::slash_commands::enhanced_slash_commands_list,
            sanity_lib::commands::slash_commands::get_command_categories,
            sanity_lib::commands::slash_commands::execute_enhanced_command,
            sanity_lib::commands::slash_commands::execute_enhanced_command_with_context,
            sanity_lib::commands::slash_commands::get_context_aware_suggestions,
            
            // Proxy Settings
            sanity_lib::commands::proxy::get_proxy_settings,
            sanity_lib::commands::proxy::save_proxy_settings,
            
            // Budget Extended Commands (Real Backend)
            sanity_lib::commands::budget_extended::get_quarterly_allocations,
            sanity_lib::commands::budget_extended::update_quarterly_allocations,
            sanity_lib::commands::budget_extended::get_department_allocations,
            sanity_lib::commands::budget_extended::update_department_allocation,
            sanity_lib::commands::budget_extended::add_department,
            sanity_lib::commands::budget_extended::remove_department,
            sanity_lib::commands::budget_extended::get_category_limits,
            sanity_lib::commands::budget_extended::set_category_limit,
            sanity_lib::commands::budget_extended::remove_category_limit,
            // Commented out mock_api expense commands - using database-backed ones instead
            // get_expenses,
            // add_expense,
            // update_expense,
            // delete_expense,
            // update_expense_status,
            sanity_lib::commands::budget_extended::get_allocation_rules,
            sanity_lib::commands::budget_extended::add_allocation_rule,
            sanity_lib::commands::budget_extended::update_allocation_rule,
            sanity_lib::commands::budget_extended::delete_allocation_rule,
            sanity_lib::commands::budget_extended::toggle_allocation_rule,
            sanity_lib::commands::budget_extended::execute_allocation_rule,
            sanity_lib::commands::budget_extended::get_budget_analytics,
            sanity_lib::commands::budget_extended::get_analytics,
            sanity_lib::commands::budget_extended::get_forecast,
            sanity_lib::commands::budget_extended::bulk_import_expenses,
            sanity_lib::commands::budget_extended::get_audit_trail,
            
            // Budget Template Commands (Real Implementation)
            budget_commands::get_budget_templates,
            budget_commands::get_budget_template_by_id,
            budget_commands::save_budget_template,
            budget_commands::apply_budget_template,
            budget_commands::delete_budget_template,
            budget_commands::update_budget_template,
            
            // Graduate Trainee Commands
            sanity_lib::commands::graduate_trainee_commands::initialize_graduate_trainee_sample_data,
            sanity_lib::commands::graduate_trainee_commands::create_graduate_trainee_program,
            sanity_lib::commands::graduate_trainee_commands::get_graduate_trainee_program,
            sanity_lib::commands::graduate_trainee_commands::get_all_graduate_trainee_programs,
            sanity_lib::commands::graduate_trainee_commands::update_graduate_trainee_program,
            sanity_lib::commands::graduate_trainee_commands::delete_graduate_trainee_program,
            sanity_lib::commands::graduate_trainee_commands::filter_graduate_trainee_programs,
            
            // Model Switching Commands
            sanity_lib::commands::model_switcher_commands::analyze_task_complexity,
            sanity_lib::commands::model_switcher_commands::get_model_recommendation,
            sanity_lib::commands::model_switcher_commands::should_switch_model,
            sanity_lib::commands::model_switcher_commands::record_task_result,
            sanity_lib::commands::model_switcher_commands::get_model_switching_config,
            sanity_lib::commands::model_switcher_commands::update_model_switching_config,
            sanity_lib::commands::model_switcher_commands::get_model_performance_stats,
            sanity_lib::commands::model_switcher_commands::get_task_history,
            sanity_lib::commands::model_switcher_commands::clear_task_history,
            sanity_lib::commands::model_switcher_commands::get_smart_model_for_prompt,
            sanity_lib::commands::model_switcher_commands::get_model_switch_explanation,
            
            // Conversation Summary Commands
            sanity_lib::commands::conversation_summary_commands::generate_conversation_summary,
            sanity_lib::commands::conversation_summary_commands::get_conversation_summaries,
            sanity_lib::commands::conversation_summary_commands::add_message_to_summary,
            sanity_lib::commands::conversation_summary_commands::get_summary_config,
            sanity_lib::commands::conversation_summary_commands::update_summary_config,
            sanity_lib::commands::conversation_summary_commands::get_current_session_metrics,
            sanity_lib::commands::conversation_summary_commands::create_new_summarizer_session,
            
            // Error Recovery Commands
            sanity_lib::commands::error_recovery_commands::attempt_error_recovery,
            sanity_lib::commands::error_recovery_commands::get_error_recovery_config,
            sanity_lib::commands::error_recovery_commands::update_error_recovery_config,
            sanity_lib::commands::error_recovery_commands::get_recovery_statistics,
            sanity_lib::commands::error_recovery_commands::get_recovery_history,
            sanity_lib::commands::error_recovery_commands::clear_recovery_history,
            
            // Context Watcher Commands
            context_watcher::start_context_watching,
            context_watcher::stop_context_watching,
            context_watcher::get_watched_projects,
            
            // Enhanced MCP Commands
            sanity_lib::commands::enhanced_mcp::get_enhanced_mcp_servers,
            sanity_lib::commands::enhanced_mcp::create_enhanced_mcp_server,
            sanity_lib::commands::enhanced_mcp::start_mcp_server,
            sanity_lib::commands::enhanced_mcp::stop_mcp_server,
            sanity_lib::commands::enhanced_mcp::delete_mcp_server,
            sanity_lib::commands::enhanced_mcp::get_mcp_server_health,
            sanity_lib::commands::enhanced_mcp::update_mcp_performance_metrics,
            sanity_lib::commands::enhanced_mcp::get_mcp_analytics,
            sanity_lib::commands::enhanced_mcp::record_mcp_tool_usage,
            sanity_lib::commands::enhanced_mcp::mcp_memory_store,
            sanity_lib::commands::enhanced_mcp::mcp_memory_retrieve,
            sanity_lib::commands::enhanced_mcp::mcp_memory_delete,
            sanity_lib::commands::enhanced_mcp::mcp_memory_query,
            sanity_lib::commands::enhanced_mcp::mcp_memory_cleanup_expired,
            sanity_lib::commands::enhanced_mcp::mcp_knowledge_add_node,
            sanity_lib::commands::enhanced_mcp::mcp_knowledge_get_node,
            sanity_lib::commands::enhanced_mcp::mcp_knowledge_query_nodes,
            sanity_lib::commands::enhanced_mcp::mcp_knowledge_export,
            sanity_lib::commands::enhanced_mcp::mcp_knowledge_import,
            sanity_lib::commands::enhanced_mcp::generate_mcp_tests,
            sanity_lib::commands::enhanced_mcp::run_mcp_coverage_analysis,
            
            // Workflow Commands
            sanity_lib::commands::workflow::create_workflow,
            sanity_lib::commands::workflow::get_workflow,
            sanity_lib::commands::workflow::list_workflows,
            sanity_lib::commands::workflow::update_workflow,
            sanity_lib::commands::workflow::delete_workflow,
            sanity_lib::commands::workflow::execute_workflow,
            sanity_lib::commands::workflow::get_execution,
            sanity_lib::commands::workflow::list_executions,
            sanity_lib::commands::workflow::cancel_execution,
            sanity_lib::commands::workflow::create_template,
            sanity_lib::commands::workflow::get_template,
            sanity_lib::commands::workflow::list_templates,
            sanity_lib::commands::workflow::search_templates,
            sanity_lib::commands::workflow::instantiate_template,
            sanity_lib::commands::workflow::update_template,
            sanity_lib::commands::workflow::delete_template,
            sanity_lib::commands::workflow::export_template,
            sanity_lib::commands::workflow::import_template,
            sanity_lib::commands::workflow::get_popular_templates,
            sanity_lib::commands::workflow::get_template_categories,
            sanity_lib::commands::workflow::create_trigger,
            sanity_lib::commands::workflow::list_triggers,
            sanity_lib::commands::workflow::stop_trigger,
            sanity_lib::commands::workflow::delete_trigger,
            sanity_lib::commands::workflow::manual_trigger_workflow,
            sanity_lib::commands::workflow::create_workflow_from_template,
            sanity_lib::commands::workflow::validate_workflow,
            sanity_lib::commands::workflow::get_workflow_statistics,
            
            // Health Monitor Commands
            sanity_lib::commands::health_monitor_commands::start_health_monitoring,
            sanity_lib::commands::health_monitor_commands::stop_health_monitoring,
            sanity_lib::commands::health_monitor_commands::get_server_health,
            sanity_lib::commands::health_monitor_commands::get_all_servers_health,
            sanity_lib::commands::health_monitor_commands::perform_health_check,
            sanity_lib::commands::health_monitor_commands::reset_health_statistics,
            sanity_lib::commands::health_monitor_commands::set_health_check_interval,
            
            // Workflow Executor Commands
            sanity_lib::commands::workflow_executor_commands::init_workflow_executor,
            sanity_lib::commands::workflow_executor_commands::execute_workflow_async,
            sanity_lib::commands::workflow_executor_commands::get_workflow_executor_status,
            sanity_lib::commands::workflow_executor_commands::stop_workflow_execution,
            sanity_lib::commands::workflow_executor_commands::pause_workflow_execution,
            sanity_lib::commands::workflow_executor_commands::resume_workflow_execution,
            sanity_lib::commands::workflow_executor_commands::get_running_workflows,
            
            // Git Hook Commands
            sanity_lib::commands::git_hook_commands::init_git_hook_manager,
            sanity_lib::commands::git_hook_commands::create_git_hook,
            sanity_lib::commands::git_hook_commands::install_git_hook,
            sanity_lib::commands::git_hook_commands::uninstall_git_hook,
            sanity_lib::commands::git_hook_commands::test_git_hook,
            sanity_lib::commands::git_hook_commands::list_git_hooks,
            sanity_lib::commands::git_hook_commands::get_git_hook,
            sanity_lib::commands::git_hook_commands::update_git_hook,
            sanity_lib::commands::git_hook_commands::delete_git_hook,
            sanity_lib::commands::git_hook_commands::get_git_hook_executions,
            
            // Agent Orchestration Commands
            sanity_lib::commands::agent_orchestration_commands::init_agent_orchestrator,
            sanity_lib::commands::agent_orchestration_commands::create_orchestration_session,
            sanity_lib::commands::agent_orchestration_commands::add_agent_to_orchestration,
            sanity_lib::commands::agent_orchestration_commands::start_orchestration,
            sanity_lib::commands::agent_orchestration_commands::stop_orchestration,
            sanity_lib::commands::agent_orchestration_commands::get_orchestration_status,
            sanity_lib::commands::agent_orchestration_commands::list_orchestration_sessions,
            sanity_lib::commands::agent_orchestration_commands::coordinate_task,
            sanity_lib::commands::agent_orchestration_commands::get_orchestration_metrics,
            sanity_lib::commands::agent_orchestration_commands::optimize_orchestration,
            
            // SuperClaude Commands
            sanity_lib::commands::superclaude::execute_superclaude_command,
            sanity_lib::commands::superclaude::get_superclaude_suggestions,
            sanity_lib::commands::superclaude::activate_personas,
            sanity_lib::commands::superclaude::get_session_personas,
            sanity_lib::commands::superclaude::save_command_history,
            sanity_lib::commands::superclaude::get_command_history,
            sanity_lib::commands::superclaude::enhance_prompt_with_context,
            sanity_lib::commands::superclaude::clear_superclaude_context,
            sanity_lib::commands::superclaude::get_superclaude_stats,
            sanity_lib::commands::superclaude::get_all_superclaude_commands,
            sanity_lib::commands::superclaude::get_all_superclaude_personas,
            sanity_lib::commands::superclaude::detect_personas_from_text,
            sanity_lib::commands::superclaude::load_superclaude_context,
            sanity_lib::commands::superclaude::save_superclaude_context,
            
            // Agent Workflow Commands
            sanity_lib::commands::agent_workflow_commands::init_agent_workflow_executor,
            sanity_lib::commands::agent_workflow_commands::execute_agent_workflow,
            sanity_lib::commands::agent_workflow_commands::execute_agent_task_direct,
            sanity_lib::commands::agent_workflow_commands::execute_agent_collaboration_direct,
            sanity_lib::commands::agent_workflow_commands::execute_agent_voting_direct,
            sanity_lib::commands::agent_workflow_commands::execute_agent_parallel_direct,
            sanity_lib::commands::agent_workflow_commands::get_agent_workflow_status,
            sanity_lib::commands::agent_workflow_commands::create_agent_orchestration_session,
            sanity_lib::commands::agent_workflow_commands::get_agent_workflow_templates,
            
            // Insights Commands
            sanity_lib::commands::insights::get_notebooks,
            sanity_lib::commands::insights::create_notebook,
            sanity_lib::commands::insights::update_notebook,
            sanity_lib::commands::insights::get_notebook,
            sanity_lib::commands::insights::delete_notebook,
            sanity_lib::commands::insights::get_sources,
            sanity_lib::commands::insights::create_source,
            sanity_lib::commands::insights::delete_source,
            sanity_lib::commands::insights::get_notes,
            sanity_lib::commands::insights::create_note,
            sanity_lib::commands::insights::update_note,
            sanity_lib::commands::insights::delete_note,
            sanity_lib::commands::insights::update_source,
            sanity_lib::commands::insights::get_source,
            sanity_lib::commands::insights::process_source,
            sanity_lib::commands::insights::generate_notebook_summary,
            sanity_lib::commands::insights::search_notebooks,
            sanity_lib::commands::insights::export_notebook,
            sanity_lib::commands::insights::import_notebook,
            
            // New Insights commands for chat and file management
            sanity_lib::commands::insights::get_chat_messages,
            sanity_lib::commands::insights::save_chat_message,
            sanity_lib::commands::insights::clear_chat_history,
            sanity_lib::commands::insights::send_chat_message,
            sanity_lib::commands::insights::upload_file,
            sanity_lib::commands::insights::get_file_path,
            sanity_lib::commands::insights::delete_file,
            // Efficient file upload commands
            sanity_lib::commands::file_upload::upload_file_native,
            sanity_lib::commands::file_upload::upload_file_rust,
            sanity_lib::commands::file_upload::extract_text_rust,
            sanity_lib::commands::insights::generate_notebook_content,
            sanity_lib::commands::insights::extract_document_text,
            sanity_lib::commands::insights::store_document_chunks,
            sanity_lib::commands::insights::search_document_chunks,
            sanity_lib::commands::insights::generate_audio_overview,
            sanity_lib::commands::insights::get_audio_file_path,
            
            // Authentication commands
            sanity_lib::commands::auth::auth_login,
            sanity_lib::commands::auth::auth_register,
            sanity_lib::commands::auth::auth_refresh,
            sanity_lib::commands::auth::auth_logout,
            sanity_lib::commands::auth::auth_validate_token,
            sanity_lib::commands::auth::auth_change_password,
            sanity_lib::commands::auth::log_security_event,
            sanity_lib::commands::auth::get_security_events,
            
            // Becon commands
            sanity_lib::commands::becon::get_user_count,
            sanity_lib::commands::becon::save_content,
            sanity_lib::commands::becon::get_all_content,
            sanity_lib::commands::becon::get_content_by_id,
            sanity_lib::commands::becon::delete_content,
            sanity_lib::commands::becon::generate_tags,
            sanity_lib::commands::becon::chat_with_ai,
            sanity_lib::commands::becon::get_conversation_history,
            
            // Document Processing commands (Real content processing with Rust+Python)
            // Temporarily disabled to fix compilation
            // sanity_lib::commands::document_processing::initialize_document_processor,
            // sanity_lib::commands::document_processing::process_document,
            // sanity_lib::commands::document_processing::process_url,
            // sanity_lib::commands::document_processing::process_pdf_with_ocr,
            // sanity_lib::commands::document_processing::transcribe_video,
            // sanity_lib::commands::document_processing::parse_github_repository,
            // sanity_lib::commands::document_processing::scrape_webpage,
            // sanity_lib::commands::document_processing::chunk_text,
            
            // Slidev Commands
            commands::slidev::generate_slides_from_prompt,
            commands::slidev::save_presentation,
            commands::slidev::load_presentation,
            commands::slidev::list_presentations,
            commands::slidev::delete_presentation,
            commands::slidev::export_presentation,
            // Mermaid commands removed - modules deleted
            
            // Claude Code API Commands
            sanity_lib::commands::claude_code_api::get_claude_code_api_config,
            sanity_lib::commands::claude_code_api::update_claude_code_api_config,
            sanity_lib::commands::claude_code_api::test_claude_code_api_connection,
            
            // Authentication Commands
            sanity_lib::commands::auth::auth_login,
            sanity_lib::commands::auth::auth_register,
            sanity_lib::commands::auth::auth_refresh,
            sanity_lib::commands::auth::auth_logout,
            sanity_lib::commands::auth::auth_validate_token,
            sanity_lib::commands::auth::auth_change_password,
            sanity_lib::commands::auth::log_security_event,
            sanity_lib::commands::auth::get_security_events,
            
            // Becon Commands
            sanity_lib::commands::becon::get_user_count,
            sanity_lib::commands::becon::save_content,
            sanity_lib::commands::becon::get_all_content,
            sanity_lib::commands::becon::get_content_by_id,
            sanity_lib::commands::becon::delete_content,
            sanity_lib::commands::becon::generate_tags,
            sanity_lib::commands::becon::chat_with_ai,
            sanity_lib::commands::becon::get_conversation_history,
            sanity_lib::commands::becon::get_supported_formats,
            
            // Content Processing Commands (Enhanced)
            sanity_lib::commands::content_processing_simple::process_url_content_enhanced,
            sanity_lib::commands::content_processing_simple::process_file_content_enhanced,
            sanity_lib::commands::content_processing_simple::transcribe_audio_file_enhanced,
            sanity_lib::commands::content_processing_simple::get_processed_content_by_id_enhanced,
            sanity_lib::commands::content_processing_simple::search_processed_content_enhanced,
            // Enhanced content commands
            sanity_lib::commands::enhanced_content::process_url_content,
            sanity_lib::commands::enhanced_content::process_file_content,
            sanity_lib::commands::enhanced_content::transcribe_audio_file,
            sanity_lib::commands::enhanced_content::process_batch_content,
            sanity_lib::commands::enhanced_content::get_enhanced_supported_formats,
            sanity_lib::commands::enhanced_content::get_processing_capabilities,
            // sanity_lib::commands::enhanced_content::validate_url,
            sanity_lib::commands::enhanced_content_upload::upload_and_process_file,
            sanity_lib::commands::enhanced_content_upload::select_and_process_file,
            
            // Visual processing commands
            sanity_lib::commands::visual_processing::process_image_file,
            sanity_lib::commands::visual_processing::process_video_file,
            sanity_lib::commands::visual_processing::get_visual_content,
            sanity_lib::commands::visual_processing::search_visual_content,
            
            // Production visual processing with ML
            sanity_lib::commands::visual_processing_production::process_image_production,
            sanity_lib::commands::visual_processing_production::process_video_production,
            
            // Vector Storage and Data Persistence Commands
            sanity_lib::commands::content_processing::process_content,
            sanity_lib::commands::content_processing::get_processed_content,
            sanity_lib::commands::content_processing::list_processed_content,
            sanity_lib::commands::content_processing::get_content_chunks,
            sanity_lib::commands::content_processing::store_content_embedding,
            sanity_lib::commands::content_processing::vector_similarity_search,
            sanity_lib::commands::content_processing::search_content,
            sanity_lib::commands::content_processing::get_content_stats,
            sanity_lib::commands::content_processing::set_cache_entry,
            sanity_lib::commands::content_processing::get_cache_entry,
            sanity_lib::commands::content_processing::cleanup_expired_cache,
            sanity_lib::commands::content_processing::check_content_duplicate,
            sanity_lib::commands::content_processing::optimize_database,
            
            // Test Runner Commands
            sanity_lib::commands::test_runner::run_content_processing_tests,
            sanity_lib::commands::test_runner::run_performance_benchmarks,
            sanity_lib::commands::test_runner::run_stress_test,
            
            // Vector Storage Test
            sanity_lib::run_vector_storage_test,
            
            // Beacon Commands
            sanity_lib::commands::beacon::get_all_sources,
            sanity_lib::commands::beacon::get_user_patterns,
            sanity_lib::commands::beacon::get_all_pages,
            sanity_lib::commands::beacon::get_page_templates,
            sanity_lib::commands::beacon::get_all_projects,
            sanity_lib::commands::beacon::get_project_templates,
            sanity_lib::commands::beacon::get_saved_searches,
            sanity_lib::commands::beacon::get_beacon_supported_formats,
            // Additional Beacon Commands
            sanity_lib::commands::beacon::create_page,
            sanity_lib::commands::beacon::update_page,
            sanity_lib::commands::beacon::delete_page,
            sanity_lib::commands::beacon::get_page,
            sanity_lib::commands::beacon::create_page_template,
            sanity_lib::commands::beacon::create_project,
            sanity_lib::commands::beacon::create_project_from_template,
            sanity_lib::commands::beacon::update_project,
            sanity_lib::commands::beacon::delete_project,
            sanity_lib::commands::beacon::get_project,
            
            // Data Center Commands - Only the implemented ones
            sanity_lib::process_data,
            
            // Graduate Trainee Commands (with RBAC)
            graduate_trainee_service::create_user,
            graduate_trainee_service::get_users,
            graduate_trainee_service::get_user_by_id,
            graduate_trainee_service::create_graduate_trainee,
            graduate_trainee_service::get_graduate_trainees,
            graduate_trainee_service::get_trainee_by_id,
            graduate_trainee_service::update_graduate_trainee,
            graduate_trainee_service::delete_graduate_trainee,
            graduate_trainee_service::create_training_program,
            graduate_trainee_service::get_training_programs,
            graduate_trainee_service::create_quarterly_review,
            graduate_trainee_service::get_quarterly_reviews,
            graduate_trainee_service::create_notification,
            graduate_trainee_service::get_notifications,
            graduate_trainee_service::mark_notification_read,
            // Graduate Trainee Reporting & Analytics Commands
            graduate_trainee_service::get_analytics_overview,
            graduate_trainee_service::export_report,
            
            // Email Service Commands
            email_service::send_email,
            email_service::get_email_config,
            email_service::update_email_config,
            email_service::test_email_connection,
            
            // Notification Scheduler Commands
            notification_scheduler::schedule_notification,
            notification_scheduler::send_immediate_notification,
            notification_scheduler::get_scheduled_notifications,
            notification_scheduler::cancel_scheduled_notification,
            notification_scheduler::process_pending_notifications,
            
            // Migration Commands
            commands::migration::get_migration_info,
            commands::migration::run_migrations,
            commands::migration::reset_database,
            commands::migration::verify_database_integrity,
            commands::migration::get_database_stats,
            
            // Graduate Trainee Security & Compliance Commands
            sanity_lib::create_audit_log_entry,
            sanity_lib::get_audit_log_entries,
            sanity_lib::create_database_backup_command,
            sanity_lib::get_backup_list_command,
            sanity_lib::restore_database_command,
            sanity_lib::cleanup_old_backups_command,
            sanity_lib::verify_backup_integrity_command,
            sanity_lib::get_backup_config_command,
            sanity_lib::update_backup_config_command,
            sanity_lib::init_graduate_trainee_database,
            sanity_lib::log_security_event_command,
            sanity_lib::generate_compliance_report,
            sanity_lib::create_security_policy_command,
            sanity_lib::get_security_policies_command,
            sanity_lib::create_compliance_check_command,
            sanity_lib::update_compliance_check_result_command,
            sanity_lib::get_compliance_checks_command,
            sanity_lib::create_security_incident_command,
            sanity_lib::get_security_incidents_command,
            sanity_lib::classify_data_command,
            sanity_lib::create_access_review_command,
            sanity_lib::get_compliance_dashboard_data_command,
            sanity_lib::run_automated_compliance_checks_command,
            sanity_lib::init_security_compliance_db_command,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
