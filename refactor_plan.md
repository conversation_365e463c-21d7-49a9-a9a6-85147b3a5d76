# CSS Grid Refactor Plan for Budget Layout

## Current Implementation Analysis

The current layout in `MainLayout.tsx` uses a flexbox-based approach:

```tsx
<div className="h-screen flex overflow-hidden">
  {/* Sidebar */}
  {sidebar}

  {/* Main Content Area */}
  <div className="flex-1 flex flex-col overflow-hidden relative">
    {/* Header */}
    {header}

    {/* Scrollable Content */}
    <div className="flex-1 overflow-y-auto">
      {content}
    </div>
  </div>

  {/* Quick Actions */}
  {quickActions}

  {/* Dialogs */}
  {dialogs}
</div>
```

The sidebar component (`BudgetSidebar.tsx`) currently uses:
- `w-96` (384px) when expanded
- `w-0` when collapsed
- Transition animations for collapsing/expanding

## Proposed CSS Grid Implementation

### 1. Main Layout Structure

```tsx
import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface MainLayoutProps {
  sidebar: React.ReactNode;
  header: React.ReactNode;
  content: React.ReactNode;
  quickActions?: React.ReactNode;
  dialogs?: React.ReactNode;
  className?: string;
}

export const MainLayout: React.FC<MainLayoutProps> = ({
  sidebar,
  header,
  content,
  quickActions,
  dialogs,
  className = ''
}) => {
  return (
    <div className={cn("min-h-screen bg-gradient-to-br from-neutral-50 via-neutral-50 to-neutral-100/20", className)}>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
        className="h-screen grid grid-cols-[384px_1fr] grid-rows-[auto_1fr]"
      >
        {/* Sidebar - Grid Item 1 */}
        {sidebar}

        {/* Header - Grid Item 2 */}
        {header}

        {/* Quick Actions */}
        {quickActions}

        {/* Dialogs */}
        {dialogs}
      </motion.div>
      
      {/* Scrollable Content Area */}
      <div className="overflow-y-auto">
        {content}
      </div>
    </div>
  );
};

export default MainLayout;
```

### 2. Grid Layout Explanation

The proposed grid layout uses:
- `grid-template-columns: [384px 1fr]` - Fixed 384px sidebar + flexible main content
- `grid-template-rows: [auto 1fr]` - Auto-sized header + flexible content area

Grid areas:
1. Sidebar (fixed 384px width)
2. Header (spans remaining width)
3. Quick Actions (positioned in grid)
4. Dialogs (positioned in grid)
5. Content (scrollable area)

### 3. Collapse Functionality

To maintain the collapse functionality, we'll use CSS variables or conditional class names:

```tsx
<motion.div
  initial={{ opacity: 0 }}
  animate={{ opacity: 1 }}
  transition={{ duration: 0.3 }}
  className={cn(
    "h-screen grid transition-all duration-300",
    isSidebarCollapsed 
      ? "grid-cols-[0px_1fr] grid-rows-[auto_1fr]" 
      : "grid-cols-[384px_1fr] grid-rows-[auto_1fr]"
  )}
>
```

### 4. Benefits of CSS Grid Approach

1. **Explicit Control**: Grid provides precise control over layout areas
2. **Semantic Structure**: Clear separation of layout components
3. **Responsive Behavior**: Easier to implement responsive breakpoints
4. **Maintainability**: Cleaner, more readable code structure
5. **Performance**: Potentially better performance with complex layouts

### 5. Implementation Steps

1. Replace flexbox container with CSS Grid container
2. Define grid-template-columns for fixed sidebar + flexible content
3. Define grid-template-rows for header + content areas
4. Maintain collapse functionality using grid column transitions
5. Preserve all existing animations and transitions
6. Test across different viewport sizes
7. Verify all existing functionality remains intact

### 6. Responsive Breakpoints

Add media queries for different screen sizes:

```css
/* Default desktop */
.h-screen {
  display: grid;
  grid-template-columns: 384px 1fr;
  grid-template-rows: auto 1fr;
}

/* Tablet */
@media (max-width: 1024px) {
  .h-screen {
    grid-template-columns: 320px 1fr;
  }
}

/* Mobile */
@media (max-width: 768px) {
  .h-screen {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto 1fr;
  }
}
```

### 7. Semantic HTML Structure

The new implementation will use semantic HTML elements:
- `<main>` for main content area
- `<aside>` for sidebar
- `<header>` for header section
- Proper ARIA attributes for accessibility

This approach maintains all existing functionality while providing a cleaner, more maintainable layout system.